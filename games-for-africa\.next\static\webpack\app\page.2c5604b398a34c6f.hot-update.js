"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/dataLoader.ts":
/*!*********************************!*\
  !*** ./src/utils/dataLoader.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   getCountriesByRegion: () => (/* binding */ getCountriesByRegion),\n/* harmony export */   getCountryById: () => (/* binding */ getCountryById),\n/* harmony export */   initializeDataLoading: () => (/* binding */ initializeDataLoading),\n/* harmony export */   loadCountriesData: () => (/* binding */ loadCountriesData),\n/* harmony export */   preloadFlagImage: () => (/* binding */ preloadFlagImage),\n/* harmony export */   preloadFlagImages: () => (/* binding */ preloadFlagImages)\n/* harmony export */ });\n// Optimized data loading with caching\n// In-memory cache for countries data\nlet countriesCache = null;\nlet cacheTimestamp = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n// Preload countries data with caching\nconst loadCountriesData = async ()=>{\n    // Check if we have valid cached data\n    const now = Date.now();\n    if (countriesCache && now - cacheTimestamp < CACHE_DURATION) {\n        return countriesCache;\n    }\n    try {\n        // Use dynamic import for better code splitting\n        const { default: countriesData } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_data_countries_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\", 19));\n        // Extract the countries array from the data structure\n        const countriesArray = countriesData.countries || [];\n        // Validate and process the data\n        const processedCountries = countriesArray.map((country)=>({\n                ...country,\n                // Ensure all required fields are present\n                population: country.population || 0,\n                exports: country.exports || [],\n                landmarks: country.landmarks || [],\n                wildlife: country.wildlife || [],\n                culturalElements: country.culturalElements || {\n                    traditionalClothing: [],\n                    cuisine: [],\n                    music: [],\n                    dances: []\n                },\n                notableFigures: country.notableFigures || []\n            }));\n        // Update cache\n        countriesCache = processedCountries;\n        cacheTimestamp = now;\n        return processedCountries;\n    } catch (error) {\n        console.error('Failed to load countries data:', error);\n        // Return empty array as fallback\n        return [];\n    }\n};\n// Preload specific country data by ID\nconst getCountryById = async (id)=>{\n    const countries = await loadCountriesData();\n    return countries.find((country)=>country.id === id) || null;\n};\n// Preload countries by region with caching\nconst regionCache = {};\nconst getCountriesByRegion = async (region)=>{\n    // Check region cache first\n    if (regionCache[region]) {\n        return regionCache[region];\n    }\n    const countries = await loadCountriesData();\n    const filteredCountries = countries.filter((country)=>country.region === region);\n    // Cache the result\n    regionCache[region] = filteredCountries;\n    return filteredCountries;\n};\n// Preload and cache flag images\nconst flagImageCache = new Map();\nconst preloadFlagImage = (countryId, flagUrl)=>{\n    return new Promise((resolve, reject)=>{\n        // Check if already cached\n        if (flagImageCache.has(countryId)) {\n            resolve();\n            return;\n        }\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.onload = ()=>{\n            flagImageCache.set(countryId, flagUrl);\n            resolve();\n        };\n        img.onerror = ()=>{\n            reject(new Error(\"Failed to load flag for \".concat(countryId)));\n        };\n        img.src = flagUrl;\n    });\n};\n// Batch preload flag images for better performance\nconst preloadFlagImages = async (countries)=>{\n    const preloadPromises = countries.map((country)=>preloadFlagImage(country.id, country.flagUrl).catch(()=>{\n            // Silently fail for individual flags to not block the entire batch\n            console.warn(\"Failed to preload flag for \".concat(country.name));\n        }));\n    try {\n        await Promise.allSettled(preloadPromises);\n    } catch (error) {\n        console.warn('Some flag images failed to preload:', error);\n    }\n};\n// Initialize data loading on app start\nconst initializeDataLoading = async ()=>{\n    try {\n        // Start loading countries data\n        const countriesPromise = loadCountriesData();\n        // Wait for countries to load, then preload flag images\n        const countries = await countriesPromise;\n        // Preload flag images in the background (don't wait)\n        preloadFlagImages(countries.slice(0, 20)).catch(()=>{\n        // Silently handle errors\n        });\n    } catch (error) {\n        console.error('Failed to initialize data loading:', error);\n    }\n};\n// Clear cache (useful for development or when data updates)\nconst clearCache = ()=>{\n    countriesCache = null;\n    cacheTimestamp = 0;\n    Object.keys(regionCache).forEach((key)=>delete regionCache[key]);\n    flagImageCache.clear();\n};\n// Get cache statistics for debugging\nconst getCacheStats = ()=>{\n    return {\n        countriesCache: {\n            loaded: !!countriesCache,\n            count: (countriesCache === null || countriesCache === void 0 ? void 0 : countriesCache.length) || 0,\n            age: Date.now() - cacheTimestamp\n        },\n        regionCache: {\n            regions: Object.keys(regionCache).length,\n            totalCountries: Object.values(regionCache).reduce((sum, countries)=>sum + countries.length, 0)\n        },\n        flagImageCache: {\n            count: flagImageCache.size\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/dataLoader.ts\n"));

/***/ })

});