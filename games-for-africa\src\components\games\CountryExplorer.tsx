'use client';

import React, { useState } from 'react';
import { Country } from '@/types';
import FlagImage from '@/components/ui/FlagImage';

interface CountryExplorerProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

const CountryExplorer: React.FC<CountryExplorerProps> = ({ countries, onComplete }) => {
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [visitedCountries, setVisitedCountries] = useState<Set<string>>(new Set());
  const [currentRegion, setCurrentRegion] = useState<string>('All');

  const regions = ['All', 'North Africa', 'West Africa', 'East Africa', 'Central Africa', 'Southern Africa'];

  const filteredCountries = currentRegion === 'All' 
    ? countries 
    : countries.filter(country => country.region === currentRegion);

  const handleCountryClick = (country: Country) => {
    setSelectedCountry(country);
    setVisitedCountries(new Set([...visitedCountries, country.id]));
  };

  const getCountryCardStyle = (country: Country) => {
    const isVisited = visitedCountries.has(country.id);
    const isSelected = selectedCountry?.id === country.id;
    
    let baseStyle = 'p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105';
    
    if (isSelected) {
      baseStyle += ' bg-yellow-400 bg-opacity-20 border-yellow-400 shadow-lg';
    } else if (isVisited) {
      baseStyle += ' bg-green-500 bg-opacity-20 border-green-400';
    } else {
      baseStyle += ' bg-gray-700 border-gray-600 hover:border-yellow-400';
    }
    
    return baseStyle;
  };

  const getRandomFact = (country: Country) => {
    const facts = [
      `${country.name} has a population of ${country.population.toLocaleString()} people.`,
      `The capital of ${country.name} is ${country.capital}.`,
      `${country.name} gained independence on ${new Date(country.independence).toLocaleDateString()}.`,
      `The currency used in ${country.name} is ${country.currency}.`,
      `Languages spoken include: ${country.languages.join(', ')}.`,
      `Major exports include: ${country.exports.slice(0, 3).join(', ')}.`,
      `Famous landmarks: ${country.landmarks.slice(0, 2).join(', ')}.`,
      `Wildlife includes: ${country.wildlife.slice(0, 3).join(', ')}.`,
    ];
    
    return facts[Math.floor(Math.random() * facts.length)];
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">🌍 Country Explorer</h2>
        <p className="text-xl text-gray-300 mb-6">
          Click on any African country to discover amazing facts and learn about its culture!
        </p>
        
        {/* Progress */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <div className="text-yellow-400 text-lg font-semibold">
            Countries Explored: {visitedCountries.size} / {countries.length}
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3 mt-2">
            <div
              className="bg-yellow-400 h-3 rounded-full transition-all duration-500"
              style={{ width: `${(visitedCountries.size / countries.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Region Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-6">
          {regions.map((region) => (
            <button
              key={region}
              onClick={() => setCurrentRegion(region)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentRegion === region
                  ? 'bg-yellow-400 text-gray-900'
                  : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
            >
              {region}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Countries Grid */}
        <div className="lg:col-span-2">
          <h3 className="text-xl font-semibold text-white mb-4">
            {currentRegion === 'All' ? 'All African Countries' : currentRegion}
            <span className="text-gray-400 ml-2">({filteredCountries.length} countries)</span>
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {filteredCountries.map((country) => (
              <div
                key={country.id}
                className={getCountryCardStyle(country)}
                onClick={() => handleCountryClick(country)}
              >
                <div className="text-center">
                  <div className="mb-2 flex justify-center">
                    <FlagImage
                      countryId={country.id}
                      size="large"
                      className="mx-auto"
                    />
                  </div>
                  <div className="text-white font-semibold">{country.name}</div>
                  <div className="text-gray-300 text-sm">{country.capital}</div>
                  {visitedCountries.has(country.id) && (
                    <div className="text-green-400 text-xs mt-1">✓ Explored</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Country Details Panel */}
        <div className="lg:col-span-1">
          {selectedCountry ? (
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 sticky top-6">
              <div className="text-center mb-6">
                <div className="mb-3 flex justify-center">
                  <FlagImage
                    countryId={selectedCountry.id}
                    size="xl"
                    className="mx-auto"
                  />
                </div>
                <h3 className="text-2xl font-bold text-white">{selectedCountry.name}</h3>
                <p className="text-yellow-400">{selectedCountry.capital}</p>
              </div>

              <div className="space-y-4">
                {/* Quick Facts */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Quick Facts</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Region:</span>
                      <span className="text-white">{selectedCountry.region}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Population:</span>
                      <span className="text-white">{selectedCountry.population.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Currency:</span>
                      <span className="text-white">{selectedCountry.currency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Independence:</span>
                      <span className="text-white">{new Date(selectedCountry.independence).getFullYear()}</span>
                    </div>
                  </div>
                </div>

                {/* Languages */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Languages</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCountry.languages.map((language, index) => (
                      <span
                        key={index}
                        className="bg-blue-500 bg-opacity-20 text-blue-300 px-2 py-1 rounded text-xs"
                      >
                        {language}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Cultural Elements */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Culture</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-400">Traditional Food:</span>
                      <p className="text-white">{selectedCountry.culturalElements.cuisine.slice(0, 2).join(', ')}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Music:</span>
                      <p className="text-white">{selectedCountry.culturalElements.music.slice(0, 2).join(', ')}</p>
                    </div>
                  </div>
                </div>

                {/* Notable Figures */}
                {selectedCountry.notableFigures.length > 0 && (
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Notable Figures</h4>
                    <div className="space-y-2">
                      {selectedCountry.notableFigures.slice(0, 2).map((figure, index) => (
                        <div key={index} className="text-sm">
                          <div className="text-white font-medium">{figure.name}</div>
                          <div className="text-gray-400 text-xs">{figure.field}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Random Fact */}
                <div className="bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-3">
                  <h4 className="text-yellow-400 font-semibold mb-2">💡 Did You Know?</h4>
                  <p className="text-white text-sm">{getRandomFact(selectedCountry)}</p>
                </div>

                {/* Landmarks */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Famous Landmarks</h4>
                  <div className="space-y-1">
                    {selectedCountry.landmarks.slice(0, 3).map((landmark, index) => (
                      <div key={index} className="text-sm text-gray-300">
                        • {landmark}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 text-center">
              <div className="text-6xl mb-4">🗺️</div>
              <h3 className="text-xl font-semibold text-white mb-2">Select a Country</h3>
              <p className="text-gray-300">
                Click on any country to explore its culture, history, and interesting facts!
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Achievement Notification */}
      {visitedCountries.size === countries.length && (
        <div className="fixed bottom-6 right-6 bg-green-500 text-white p-4 rounded-lg shadow-lg animate-bounce">
          <div className="text-center">
            <div className="text-2xl mb-1">🏆</div>
            <div className="font-bold">Explorer Achievement!</div>
            <div className="text-sm">You've visited all countries!</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CountryExplorer;
