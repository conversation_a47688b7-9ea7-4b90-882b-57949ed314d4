"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_MysteryLand_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/MysteryLand.tsx":
/*!**********************************************!*\
  !*** ./src/components/games/MysteryLand.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=getRandomItems,shuffleArray!=!@/utils */ \"(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MysteryLand = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentCountry, setCurrentCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [clues, setClues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revealedClues, setRevealedClues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnswer, setSelectedAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [round, setRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const maxRounds = 10;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MysteryLand.useEffect\": ()=>{\n            startNewRound();\n        }\n    }[\"MysteryLand.useEffect\"], []);\n    const generateClues = (country)=>{\n        const allClues = [];\n        // Geography clues\n        allClues.push({\n            text: \"I am located in \".concat(country.region, \".\"),\n            type: 'geography'\n        });\n        allClues.push({\n            text: \"My capital city is \".concat(country.capital, \".\"),\n            type: 'geography'\n        });\n        allClues.push({\n            text: \"My currency is \".concat(country.currency, \".\"),\n            type: 'geography'\n        });\n        // Landmark clues\n        if (country.landmarks.length > 0) {\n            allClues.push({\n                text: \"I am home to \".concat(country.landmarks[0], \".\"),\n                type: 'landmark'\n            });\n            if (country.landmarks.length > 1) {\n                allClues.push({\n                    text: \"You can visit \".concat(country.landmarks[1], \" in my territory.\"),\n                    type: 'landmark'\n                });\n            }\n        }\n        // Cultural clues\n        if (country.culturalElements.cuisine.length > 0) {\n            allClues.push({\n                text: \"My people enjoy traditional dishes like \".concat(country.culturalElements.cuisine[0], \".\"),\n                type: 'culture'\n            });\n        }\n        if (country.culturalElements.music.length > 0) {\n            allClues.push({\n                text: \"\".concat(country.culturalElements.music[0], \" music is popular in my culture.\"),\n                type: 'culture'\n            });\n        }\n        // Language clues\n        if (country.languages.length > 1) {\n            allClues.push({\n                text: \"My people speak \".concat(country.languages[0], \" and \").concat(country.languages[1], \".\"),\n                type: 'culture'\n            });\n        } else {\n            allClues.push({\n                text: \"The main language spoken here is \".concat(country.languages[0], \".\"),\n                type: 'culture'\n            });\n        }\n        // Wildlife clues\n        if (country.wildlife.length > 0) {\n            allClues.push({\n                text: \"\".concat(country.wildlife[0], \" can be found in my wilderness.\"),\n                type: 'geography'\n            });\n        }\n        // Historical clues\n        const independenceYear = new Date(country.independence).getFullYear();\n        if (independenceYear > 1800) {\n            allClues.push({\n                text: \"I gained independence in \".concat(independenceYear, \".\"),\n                type: 'history'\n            });\n        }\n        // Notable figures\n        if (country.notableFigures.length > 0) {\n            allClues.push({\n                text: \"\".concat(country.notableFigures[0].name, \" is one of my famous citizens, known for \").concat(country.notableFigures[0].field, \".\"),\n                type: 'notable-figure'\n            });\n        }\n        // Export clues\n        if (country.exports.length > 0) {\n            allClues.push({\n                text: \"I am known for exporting \".concat(country.exports[0], \".\"),\n                type: 'geography'\n            });\n        }\n        // Population clue\n        if (country.population > 50000000) {\n            allClues.push({\n                text: \"I have a large population of over \".concat(Math.floor(country.population / 1000000), \" million people.\"),\n                type: 'geography'\n            });\n        } else if (country.population < 5000000) {\n            allClues.push({\n                text: \"I am a smaller country with less than 5 million people.\",\n                type: 'geography'\n            });\n        }\n        return (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(allClues).slice(0, 6);\n    };\n    const startNewRound = ()=>{\n        const selectedCountry = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, 1)[0];\n        const wrongOptions = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries.filter((c)=>c.id !== selectedCountry.id), 3).map((c)=>c.name);\n        setCurrentCountry(selectedCountry);\n        setClues(generateClues(selectedCountry));\n        setRevealedClues(1);\n        setOptions((0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)([\n            selectedCountry.name,\n            ...wrongOptions\n        ]));\n        setSelectedAnswer(null);\n        setShowResult(false);\n        setIsCorrect(false);\n    };\n    const revealNextClue = ()=>{\n        if (revealedClues < clues.length) {\n            setRevealedClues(revealedClues + 1);\n        }\n    };\n    const handleGuess = (answer)=>{\n        if (selectedAnswer) return;\n        setSelectedAnswer(answer);\n        const correct = answer === (currentCountry === null || currentCountry === void 0 ? void 0 : currentCountry.name);\n        setIsCorrect(correct);\n        setShowResult(true);\n        if (correct) {\n            // Calculate score based on how many clues were revealed\n            const baseScore = 20;\n            const clueBonus = Math.max(0, (clues.length - revealedClues) * 5);\n            const roundScore = baseScore + clueBonus;\n            setScore(score + roundScore);\n        }\n        setTimeout(()=>{\n            if (round < maxRounds) {\n                setRound(round + 1);\n                startNewRound();\n            } else {\n                setGameComplete(true);\n                onComplete(score);\n            }\n        }, 3000);\n    };\n    const getClueIcon = (type)=>{\n        switch(type){\n            case 'landmark':\n                return '🏛️';\n            case 'culture':\n                return '🎭';\n            case 'geography':\n                return '🗺️';\n            case 'history':\n                return '📚';\n            case 'notable-figure':\n                return '👤';\n            default:\n                return '💡';\n        }\n    };\n    if (!currentCountry) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-4\",\n                        children: \"\\uD83D\\uDD75️ Mystery Land\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-6 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400\",\n                                children: [\n                                    \"Round: \",\n                                    round,\n                                    \"/\",\n                                    maxRounds\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400\",\n                                children: [\n                                    \"Score: \",\n                                    score\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Use the clues to guess which African country I am describing!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDD0D Clues\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: clues.slice(0, revealedClues).map((clue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 animate-slide-up\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: getClueIcon(clue.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white\",\n                                                            children: clue.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-xs mt-1 capitalize\",\n                                                            children: [\n                                                                clue.type.replace('-', ' '),\n                                                                \" clue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined),\n                            revealedClues < clues.length && !selectedAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: revealNextClue,\n                                className: \"w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-500 transition-colors\",\n                                children: [\n                                    \"\\uD83D\\uDCA1 Reveal Next Clue (\",\n                                    revealedClues,\n                                    \"/\",\n                                    clues.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm text-gray-400 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Clues Revealed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    revealedClues,\n                                                    \"/\",\n                                                    clues.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-700 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-400 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(revealedClues / clues.length * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDD14 Your Guess\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: options.map((option, index)=>{\n                                    const isSelected = selectedAnswer === option;\n                                    const isCorrect = option === currentCountry.name && showResult;\n                                    const isWrong = isSelected && !isCorrect && showResult;\n                                    let buttonClass = 'w-full p-4 rounded-lg border-2 transition-all duration-200 text-left';\n                                    if (showResult) {\n                                        if (isCorrect) {\n                                            buttonClass += ' bg-green-500 bg-opacity-20 border-green-400 text-green-300';\n                                        } else if (isWrong) {\n                                            buttonClass += ' bg-red-500 bg-opacity-20 border-red-400 text-red-300';\n                                        } else {\n                                            buttonClass += ' bg-gray-700 border-gray-600 text-gray-400';\n                                        }\n                                    } else {\n                                        buttonClass += ' bg-gray-700 border-gray-600 text-white hover:border-yellow-400 cursor-pointer';\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: buttonClass,\n                                        onClick: ()=>handleGuess(option),\n                                        disabled: !!selectedAnswer,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: option\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showResult && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                showResult && isWrong && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 47\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined),\n                            showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gray-700 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-2\",\n                                        children: isCorrect ? '🎉 Correct!' : '❌ Wrong Answer'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isCorrect ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Base Score: 20 points\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Clue Bonus: +\",\n                                                    Math.max(0, (clues.length - revealedClues) * 5),\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold\",\n                                                children: [\n                                                    \"Round Score: \",\n                                                    20 + Math.max(0, (clues.length - revealedClues) * 5),\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"The correct answer was: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: currentCountry.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 46\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No points earned this round.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined),\n                            !selectedAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-400 text-sm\",\n                                    children: [\n                                        \"\\uD83D\\uDCA1 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Tip:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 20\n                                        }, undefined),\n                                        \" Guess early for bonus points! Each unused clue gives you +5 points.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 150 ? '🏆' : score >= 100 ? '🎉' : '🕵️'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Mystery Solved!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-2\",\n                                        children: [\n                                            \"You completed all \",\n                                            maxRounds,\n                                            \" rounds!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-yellow-400 font-bold\",\n                                        children: [\n                                            \"Final Score: \",\n                                            score,\n                                            \" points\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm mt-2\",\n                                        children: score >= 150 ? 'Master Detective!' : score >= 100 ? 'Great Detective Work!' : 'Keep Investigating!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\MysteryLand.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MysteryLand, \"CiBIAt830DLPSlnaRY3GuRwAE74=\");\n_c = MysteryLand;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MysteryLand);\nvar _c;\n$RefreshReg$(_c, \"MysteryLand\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/MysteryLand.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts ***!
  \************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   formatScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatScore),\n/* harmony export */   formatTime: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.shuffleArray),\n/* harmony export */   validateAnswer: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.validateAnswer)\n/* harmony export */ });\n/* harmony import */ var C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/index.ts */ \"(app-pages-browser)/./src/utils/index.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPWdldFJhbmRvbUl0ZW1zLHNodWZmbGVBcnJheSE9IS4vc3JjL3V0aWxzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1FRUsgRURFTlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxHQU1FUyBGT1IgQVJJQ0FcXGdhbWVzLWZvci1hZnJpY2FcXHNyY1xcdXRpbHNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxNRUVLIEVERU5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcR0FNRVMgRk9SIEFSSUNBXFxcXGdhbWVzLWZvci1hZnJpY2FcXFxcc3JjXFxcXHV0aWxzXFxcXGluZGV4LnRzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\n"));

/***/ })

}]);