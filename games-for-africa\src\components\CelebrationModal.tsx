'use client';

import React, { useEffect, useState } from 'react';
import { Achievement } from '@/types/progress';

interface CelebrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'achievement' | 'levelUp' | 'gameComplete';
  data: {
    achievement?: Achievement;
    newLevel?: number;
    xpGained?: number;
    score?: number;
    gameTitle?: string;
  };
}

const CelebrationModal: React.FC<CelebrationModalProps> = ({
  isOpen,
  onClose,
  type,
  data,
}) => {
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShowConfetti(true);
      const timer = setTimeout(() => {
        setShowConfetti(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const renderContent = () => {
    switch (type) {
      case 'achievement':
        return (
          <div className="text-center space-y-6">
            <div className="text-8xl animate-bounce">{data.achievement?.icon}</div>
            <div>
              <h2 className="text-3xl font-bold text-yellow-400 mb-2">
                Achievement Unlocked!
              </h2>
              <h3 className="text-2xl font-semibold text-white mb-2">
                {data.achievement?.name}
              </h3>
              <p className="text-gray-300 text-lg">
                {data.achievement?.description}
              </p>
            </div>
            <div className="bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4">
              <p className="text-yellow-400 font-semibold">
                +100 XP Bonus for Achievement!
              </p>
            </div>
          </div>
        );

      case 'levelUp':
        return (
          <div className="text-center space-y-6">
            <div className="text-8xl animate-pulse">🎉</div>
            <div>
              <h2 className="text-3xl font-bold text-yellow-400 mb-2">
                Level Up!
              </h2>
              <h3 className="text-2xl font-semibold text-white mb-2">
                You reached Level {data.newLevel}!
              </h3>
              <p className="text-gray-300 text-lg">
                Keep exploring Africa to unlock more achievements!
              </p>
            </div>
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-4">
              <p className="text-gray-900 font-semibold">
                +{data.xpGained} XP Earned!
              </p>
            </div>
          </div>
        );

      case 'gameComplete':
        return (
          <div className="text-center space-y-6">
            <div className="text-8xl animate-spin-slow">⭐</div>
            <div>
              <h2 className="text-3xl font-bold text-yellow-400 mb-2">
                Game Complete!
              </h2>
              <h3 className="text-2xl font-semibold text-white mb-2">
                {data.gameTitle}
              </h3>
              <p className="text-gray-300 text-lg">
                Final Score: {data.score}
              </p>
            </div>
            <div className="bg-blue-400 bg-opacity-10 border border-blue-400 rounded-lg p-4">
              <p className="text-blue-400 font-semibold">
                +{data.xpGained} XP Earned!
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
      {/* Confetti Effect */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {Array.from({ length: 50 }).map((_, i) => (
            <div
              key={i}
              className="absolute animate-confetti"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`,
              }}
            >
              <div
                className="w-2 h-2 rounded-full"
                style={{
                  backgroundColor: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][
                    Math.floor(Math.random() * 6)
                  ],
                }}
              />
            </div>
          ))}
        </div>
      )}

      <div className="bg-gray-800 rounded-lg p-8 max-w-md w-full border border-gray-700 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white text-xl"
        >
          ✕
        </button>

        {renderContent()}

        <div className="mt-8 flex justify-center">
          <button
            onClick={onClose}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg font-bold hover:bg-yellow-300 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes confetti {
          0% {
            transform: translateY(-100vh) rotate(0deg);
            opacity: 1;
          }
          100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
          }
        }
        
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        
        .animate-confetti {
          animation: confetti linear infinite;
        }
        
        .animate-spin-slow {
          animation: spin-slow 2s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default CelebrationModal;
