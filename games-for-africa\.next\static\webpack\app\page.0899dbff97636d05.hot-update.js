"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (currentRound && gameStarted && !gameComplete && !showVictory) {\n                const settings = getDifficultySettings(difficulty);\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.checkGameCompletion['flag-matching'](currentRound.matches, settings.pairs)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        if (!currentRound) return;\n        const settings = getDifficultySettings(difficulty);\n        const totalTime = settings.timeLimit;\n        const completionTime = (Date.now() - gameStartTime) / 1000;\n        const gameData = {\n            gameType: 'flag-matching',\n            score,\n            timeRemaining: timeLeft,\n            totalTime,\n            perfectScore: currentRound.matches === settings.pairs && currentRound.attempts === settings.pairs,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.processGameCompletion)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        if (!showVictory) {\n            // Time ran out - no victory animation, just complete\n            setGameComplete(true);\n            setTimeout(()=>onComplete(score), 1000);\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        countryId: flag.country.id,\n                                                        size: \"xl\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                countryId: lastMatchedCountry.id,\n                                size: \"large\",\n                                className: \"mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 457,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\\'s Up!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound.matches\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    settings.pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            Math.round(currentRound.matches / Math.max(currentRound.attempts, 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 32\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 474,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"ToZimn3Niwr/0+VRb4yYWuUDEx0=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ })

});