import { useState, useEffect } from 'react';
import { Country } from '@/types';
import countriesData from '@/data/countries.json';

export const useCountries = () => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      setCountries(countriesData.countries);
      setLoading(false);
    } catch (err) {
      setError('Failed to load countries data');
      setLoading(false);
    }
  }, []);

  const getCountryById = (id: string): Country | undefined => {
    return countries.find(country => country.id === id);
  };

  const getCountriesByRegion = (region: string): Country[] => {
    return countries.filter(country => country.region === region);
  };

  const searchCountries = (query: string): Country[] => {
    const lowercaseQuery = query.toLowerCase();
    return countries.filter(country =>
      country.name.toLowerCase().includes(lowercaseQuery) ||
      country.capital.toLowerCase().includes(lowercaseQuery) ||
      country.languages.some(lang => lang.toLowerCase().includes(lowercaseQuery))
    );
  };

  return {
    countries,
    loading,
    error,
    getCountryById,
    getCountriesByRegion,
    searchCountries,
  };
};
