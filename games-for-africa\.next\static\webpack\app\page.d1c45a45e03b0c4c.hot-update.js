"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (currentRound && gameStarted && !gameComplete && !showVictory) {\n                const settings = getDifficultySettings(difficulty);\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['flag-matching'](currentRound.matches, settings.pairs)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        if (!currentRound) return;\n        const settings = getDifficultySettings(difficulty);\n        const totalTime = settings.timeLimit;\n        const completionTime = (Date.now() - gameStartTime) / 1000;\n        const gameData = {\n            gameType: 'flag-matching',\n            score,\n            timeRemaining: timeLeft,\n            totalTime,\n            perfectScore: currentRound.matches === settings.pairs && currentRound.attempts === settings.pairs,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletion)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        if (!showVictory) {\n            // Time ran out - no victory animation, just complete\n            setGameComplete(true);\n            setTimeout(()=>onComplete(score), 1000);\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        countryId: flag.country.id,\n                                                        size: \"xl\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                countryId: lastMatchedCountry.id,\n                                size: \"large\",\n                                className: \"mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 457,\n                columnNumber: 9\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 474,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && !showVictory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"⏰\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Time's Up!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: (currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    getDifficultySettings(difficulty).pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 483,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"ToZimn3Niwr/0+VRb4yYWuUDEx0=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/VictoryAnimation.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VictoryAnimation = (param)=>{\n    let { isVisible, completionData, onComplete } = param;\n    _s();\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationPhase, setAnimationPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('enter');\n    const config = _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__.celebrationConfigs[completionData.celebrationType];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VictoryAnimation.useEffect\": ()=>{\n            if (!isVisible) return;\n            setShowConfetti(true);\n            setAnimationPhase('enter');\n            const timeline = [\n                // Enter phase\n                {\n                    delay: 0,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('enter')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Display phase\n                {\n                    delay: 300,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('display')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Exit phase\n                {\n                    delay: config.duration - 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('exit')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Complete\n                {\n                    delay: config.duration,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            setShowConfetti(false);\n                            onComplete();\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                }\n            ];\n            const timeouts = timeline.map({\n                \"VictoryAnimation.useEffect.timeouts\": (param)=>{\n                    let { delay, action } = param;\n                    return setTimeout(action, delay);\n                }\n            }[\"VictoryAnimation.useEffect.timeouts\"]);\n            return ({\n                \"VictoryAnimation.useEffect\": ()=>{\n                    timeouts.forEach(clearTimeout);\n                }\n            })[\"VictoryAnimation.useEffect\"];\n        }\n    }[\"VictoryAnimation.useEffect\"], [\n        isVisible,\n        config.duration,\n        onComplete\n    ]);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: config.particles\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute animate-bounce\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                            animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full\",\n                            style: {\n                                backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],\n                                transform: \"rotate(\".concat(Math.random() * 360, \"deg)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          bg-gradient-to-br from-yellow-400 to-orange-500 \\n          rounded-2xl p-8 text-center max-w-md mx-4 \\n          transform transition-all duration-500 ease-out\\n          \".concat(animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : '', \"\\n          \").concat(animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : '', \"\\n          \").concat(animationPhase === 'exit' ? 'scale-110 opacity-90' : '', \"\\n        \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-8xl mb-4 animate-pulse\",\n                        children: config.emoji\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: completionData.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completionData.finalScore\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Final Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                \"+\",\n                                                completionData.timeBonus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Time Bonus\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-blue-900 font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: [\n                                        \"+\",\n                                        completionData.xpGained\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    children: \"XP Gained!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    completionData.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-900 font-semibold text-sm\",\n                                children: \"Achievements Unlocked:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined),\n                            completionData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm\",\n                                    style: {\n                                        animationDelay: \"\".concat(index * 200, \"ms\")\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 \",\n                                        achievement\n                                    ]\n                                }, achievement, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-300 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out\",\n                                    style: {\n                                        width: animationPhase === 'display' ? '100%' : '0%',\n                                        transitionDelay: '500ms'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-700 text-xs mt-2\",\n                                children: [\n                                    \"Auto-continuing in \",\n                                    Math.ceil(config.duration / 1000),\n                                    \" seconds...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-yellow-300 text-2xl animate-ping\",\n                            style: {\n                                left: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                top: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                                animationDuration: \"\".concat(2 + Math.random() * 2, \"s\")\n                            },\n                            children: \"⭐\"\n                        }, \"star-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from({\n                        length: 12\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-white text-lg animate-bounce\",\n                            style: {\n                                left: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                top: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                            },\n                            children: \"✨\"\n                        }, \"sparkle-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined),\n            animationPhase === 'display' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-white animate-bounce\",\n                    children: [\n                        completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊',\n                        completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟',\n                        completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏',\n                        completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VictoryAnimation, \"+mcClSZwhaCqK55+QlgJ1Q9DGWo=\");\n_c = VictoryAnimation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VictoryAnimation);\nvar _c;\n$RefreshReg$(_c, \"VictoryAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\n"));

/***/ })

});