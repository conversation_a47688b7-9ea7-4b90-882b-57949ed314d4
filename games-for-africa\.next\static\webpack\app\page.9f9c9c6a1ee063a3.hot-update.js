"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/countries.json":
/*!*********************************!*\
  !*** ./src/data/countries.json ***!
  \*********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"countries":[{"id":"nigeria","name":"Nigeria","capital":"Abuja","flagUrl":"🇳🇬","currency":"Nigerian Naira","languages":["English","Hausa","Yoruba","Igbo"],"population":218541000,"region":"West Africa","independence":"1960-10-01","exports":["Crude oil","Natural gas"],"landmarks":["Zuma Rock","Olumo Rock"],"wildlife":["African elephant","Chimpanzee"],"culturalElements":{"traditionalClothing":["Agbada","Dashiki"],"cuisine":["Jollof rice","Suya"],"music":["Afrobeat","Highlife"],"dances":["Bata","Ekombi"]},"notableFigures":[{"name":"Chinua Achebe","field":"Literature","achievement":"Author of \'Things Fall Apart\'"}]},{"id":"kenya","name":"Kenya","capital":"Nairobi","flagUrl":"🇰🇪","currency":"Kenyan Shilling","languages":["English","Swahili"],"population":54027000,"region":"East Africa","independence":"1963-12-12","exports":["Tea","Coffee"],"landmarks":["Mount Kenya","Maasai Mara"],"wildlife":["Lion","Elephant","Giraffe"],"culturalElements":{"traditionalClothing":["Kanga","Kitenge"],"cuisine":["Ugali","Nyama choma"],"music":["Benga","Taarab"],"dances":["Mugithi","Chakacha"]},"notableFigures":[{"name":"Wangari Maathai","field":"Environmental activism","achievement":"Nobel Peace Prize winner"}]},{"id":"south-africa","name":"South Africa","capital":"Cape Town","flagUrl":"🇿🇦","currency":"South African Rand","languages":["Afrikaans","English","Zulu"],"population":60414000,"region":"Southern Africa","independence":"1910-05-31","exports":["Gold","Diamonds"],"landmarks":["Table Mountain","Kruger National Park"],"wildlife":["Big Five","Penguin"],"culturalElements":{"traditionalClothing":["Shweshwe","Ndebele beadwork"],"cuisine":["Braai","Bobotie"],"music":["Kwaito","Amapiano"],"dances":["Gumboot dance","Zulu dance"]},"notableFigures":[{"name":"Nelson Mandela","field":"Politics","achievement":"Anti-apartheid leader"}]},{"id":"egypt","name":"Egypt","capital":"Cairo","flagUrl":"🇪🇬","currency":"Egyptian Pound","languages":["Arabic"],"population":104258000,"region":"North Africa","independence":"1922-02-28","exports":["Petroleum","Natural gas"],"landmarks":["Pyramids of Giza","Sphinx"],"wildlife":["Nile crocodile","Egyptian cobra"],"culturalElements":{"traditionalClothing":["Galabeya","Hijab"],"cuisine":["Ful medames","Koshari"],"music":["Shaabi","Classical Arabic"],"dances":["Raqs sharqi","Saidi"]},"notableFigures":[{"name":"Naguib Mahfouz","field":"Literature","achievement":"Nobel Prize winner"}]},{"id":"ghana","name":"Ghana","capital":"Accra","flagUrl":"🇬🇭","currency":"Ghanaian Cedi","languages":["English","Akan"],"population":********,"region":"West Africa","independence":"1957-03-06","exports":["Gold","Cocoa"],"landmarks":["Cape Coast Castle","Kakum National Park"],"wildlife":["Forest elephant","Bongo antelope"],"culturalElements":{"traditionalClothing":["Kente cloth","Adinkra symbols"],"cuisine":["Jollof rice","Banku"],"music":["Highlife","Hiplife"],"dances":["Adowa","Kpanlogo"]},"notableFigures":[{"name":"Kwame Nkrumah","field":"Politics","achievement":"First President of Ghana"}]},{"id":"ethiopia","name":"Ethiopia","capital":"Addis Ababa","flagUrl":"🇪🇹","currency":"Ethiopian Birr","languages":["Amharic","Oromo"],"population":*********,"region":"East Africa","independence":"Never colonized","exports":["Coffee","Gold"],"landmarks":["Lalibela churches","Simien Mountains"],"wildlife":["Ethiopian wolf","Gelada baboon"],"culturalElements":{"traditionalClothing":["Habesha kemis","Netela"],"cuisine":["Injera","Doro wat"],"music":["Traditional Ethiopian music","Ethio-jazz"],"dances":["Eskista","Traditional highland dances"]},"notableFigures":[{"name":"Haile Selassie","field":"Monarchy","achievement":"Last Emperor of Ethiopia"}]},{"id":"morocco","name":"Morocco","capital":"Rabat","flagUrl":"🇲🇦","currency":"Moroccan Dirham","languages":["Arabic","Berber"],"population":********,"region":"North Africa","independence":"1956-03-02","exports":["Phosphates","Textiles"],"landmarks":["Hassan II Mosque","Marrakech"],"wildlife":["Barbary macaque","Fennec fox"],"culturalElements":{"traditionalClothing":["Djellaba","Kaftan"],"cuisine":["Tagine","Couscous"],"music":["Gnawa","Chaabi"],"dances":["Chaabi dance","Gnawa ceremonies"]},"notableFigures":[{"name":"Mohammed V","field":"Monarchy","achievement":"Led Morocco to independence"}]},{"id":"tanzania","name":"Tanzania","capital":"Dodoma","flagUrl":"🇹🇿","currency":"Tanzanian Shilling","languages":["Swahili","English"],"population":61498000,"region":"East Africa","independence":"1961-12-09","exports":["Gold","Coffee"],"landmarks":["Mount Kilimanjaro","Serengeti"],"wildlife":["Big Five","Wildebeest"],"culturalElements":{"traditionalClothing":["Kanga","Kitenge"],"cuisine":["Ugali","Nyama choma"],"music":["Taarab","Bongo Flava"],"dances":["Traditional Tanzanian dances","Ngoma"]},"notableFigures":[{"name":"Julius Nyerere","field":"Politics","achievement":"Father of Tanzanian independence"}]},{"id":"senegal","name":"Senegal","capital":"Dakar","flagUrl":"🇸🇳","currency":"West African CFA Franc","languages":["French","Wolof"],"population":17196000,"region":"West Africa","independence":"1960-04-04","exports":["Fish","Peanuts"],"landmarks":["Gorée Island","Pink Lake"],"wildlife":["West African manatee","Various bird species"],"culturalElements":{"traditionalClothing":["Boubou","Grand boubou"],"cuisine":["Thieboudienne","Yassa"],"music":["Mbalax","Sabar"],"dances":["Sabar","Traditional Senegalese dances"]},"notableFigures":[{"name":"Léopold Sédar Senghor","field":"Politics/Literature","achievement":"First President and poet"}]},{"id":"uganda","name":"Uganda","capital":"Kampala","flagUrl":"🇺🇬","currency":"Ugandan Shilling","languages":["English","Swahili"],"population":47123000,"region":"East Africa","independence":"1962-10-09","exports":["Coffee","Fish"],"landmarks":["Bwindi Impenetrable Forest","Murchison Falls"],"wildlife":["Mountain gorilla","Chimpanzee"],"culturalElements":{"traditionalClothing":["Gomesi","Kanzu"],"cuisine":["Matoke","Posho"],"music":["Traditional Ugandan music","Kadongo Kamu"],"dances":["Traditional Ugandan dances","Kiganda dances"]},"notableFigures":[{"name":"Milton Obote","field":"Politics","achievement":"First Prime Minister"}]},{"id":"rwanda","name":"Rwanda","capital":"Kigali","flagUrl":"🇷🇼","currency":"Rwandan Franc","languages":["Kinyarwanda","French","English"],"population":13277000,"region":"East Africa","independence":"1962-07-01","exports":["Coffee","Tea"],"landmarks":["Volcanoes National Park","Nyungwe Forest"],"wildlife":["Mountain gorilla","Golden monkey"],"culturalElements":{"traditionalClothing":["Imvutano","Traditional wraps"],"cuisine":["Ugali","Ibirayi"],"music":["Traditional Rwandan music","Intore dance music"],"dances":["Intore","Traditional Rwandan dances"]},"notableFigures":[{"name":"Paul Kagame","field":"Politics","achievement":"Current President"}]}]}');

/***/ })

});