"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setRoundStartTime(Date.now());\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            countryId: currentCountry.id,\n                                            size: \"large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"cT4d+D4snhqfIe6om8d5KTnT0/g=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ })

});