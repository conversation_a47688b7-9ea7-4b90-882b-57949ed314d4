'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { getRandomItems, shuffleArray } from '@/utils';

interface WhereInAfricaProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface GameRound {
  country: Country;
  clueType: 'landmark' | 'food' | 'culture' | 'wildlife' | 'export';
  clue: string;
  options: string[];
  correctAnswer: string;
}

const WhereInAfrica: React.FC<WhereInAfricaProps> = ({ countries, onComplete }) => {
  const [currentRound, setCurrentRound] = useState(0);
  const [rounds, setRounds] = useState<GameRound[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [score, setScore] = useState(0);
  const [correctAnswers, setCorrectAnswers] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);

  const totalRounds = 10;

  const generateClue = (country: Country): { type: GameRound['clueType']; clue: string } => {
    const clueTypes: Array<{ type: GameRound['clueType']; items: string[] }> = [
      { type: 'landmark', items: country.landmarks },
      { type: 'food', items: country.culturalElements.cuisine },
      { type: 'culture', items: country.culturalElements.music },
      { type: 'wildlife', items: country.wildlife },
      { type: 'export', items: country.exports },
    ];

    const availableClues = clueTypes.filter(ct => ct.items.length > 0);
    const selectedClueType = availableClues[Math.floor(Math.random() * availableClues.length)];
    const selectedItem = selectedClueType.items[Math.floor(Math.random() * selectedClueType.items.length)];

    return {
      type: selectedClueType.type,
      clue: selectedItem,
    };
  };

  const generateRounds = () => {
    const selectedCountries = getRandomItems(countries, totalRounds);
    const gameRounds: GameRound[] = [];

    selectedCountries.forEach((country) => {
      const { type, clue } = generateClue(country);
      
      // Generate wrong options
      const wrongCountries = getRandomItems(
        countries.filter(c => c.id !== country.id),
        3
      );
      
      const options = shuffleArray([
        country.name,
        ...wrongCountries.map(c => c.name)
      ]);

      gameRounds.push({
        country,
        clueType: type,
        clue,
        options,
        correctAnswer: country.name,
      });
    });

    return gameRounds;
  };

  const startGame = () => {
    const newRounds = generateRounds();
    setRounds(newRounds);
    setCurrentRound(0);
    setScore(0);
    setCorrectAnswers(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setGameComplete(false);
    setGameStarted(true);
  };

  const handleAnswerSelect = (answer: string) => {
    if (selectedAnswer) return;

    setSelectedAnswer(answer);
    setShowResult(true);

    const isCorrect = answer === rounds[currentRound].correctAnswer;
    
    if (isCorrect) {
      setCorrectAnswers(correctAnswers + 1);
      setScore(score + 10);
    }

    setTimeout(() => {
      if (currentRound < totalRounds - 1) {
        setCurrentRound(currentRound + 1);
        setSelectedAnswer(null);
        setShowResult(false);
      } else {
        setGameComplete(true);
        const finalScore = Math.round((correctAnswers + (isCorrect ? 1 : 0)) / totalRounds * 100);
        setTimeout(() => onComplete(finalScore), 1000);
      }
    }, 2000);
  };

  const getClueIcon = (type: GameRound['clueType']) => {
    switch (type) {
      case 'landmark': return '🏛️';
      case 'food': return '🍽️';
      case 'culture': return '🎵';
      case 'wildlife': return '🦁';
      case 'export': return '📦';
      default: return '❓';
    }
  };

  const getClueDescription = (type: GameRound['clueType']) => {
    switch (type) {
      case 'landmark': return 'Famous landmark';
      case 'food': return 'Traditional dish';
      case 'culture': return 'Musical style';
      case 'wildlife': return 'Native animal';
      case 'export': return 'Major export';
      default: return 'Clue';
    }
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🗺️ Where in Africa?</h2>
          <div className="text-6xl mb-6">🌍</div>
          <p className="text-xl text-gray-300 mb-6">
            Guess the African country from clues about its landmarks, food, culture, and more!
          </p>
          
          <div className="space-y-4 text-gray-300 mb-8">
            <p>• {totalRounds} rounds of cultural clues</p>
            <p>• Clues include landmarks, food, music, wildlife, and exports</p>
            <p>• Multiple choice answers</p>
            <p>• Score points for correct guesses</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Adventure
          </button>
        </div>
      </div>
    );
  }

  if (!rounds[currentRound]) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  const round = rounds[currentRound];

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🗺️ Where in Africa?</h2>
          <div className="text-yellow-400 text-xl font-bold">
            Round {currentRound + 1}/{totalRounds}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{correctAnswers}</div>
            <div className="text-gray-400 text-sm">Correct</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{currentRound + 1}</div>
            <div className="text-gray-400 text-sm">Current</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {Math.round((correctAnswers / Math.max(currentRound, 1)) * 100)}%
            </div>
            <div className="text-gray-400 text-sm">Accuracy</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentRound + 1) / totalRounds) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="text-center mb-6">
          <div className="text-6xl mb-4">{getClueIcon(round.clueType)}</div>
          <h3 className="text-2xl font-bold text-white mb-2">
            Which African country is known for this?
          </h3>
          <div className="bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4 mb-4">
            <div className="text-yellow-400 text-sm font-medium mb-1">
              {getClueDescription(round.clueType)}
            </div>
            <div className="text-white text-xl font-bold">
              "{round.clue}"
            </div>
          </div>
        </div>

        {/* Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {round.options.map((option, index) => {
            const isSelected = selectedAnswer === option;
            const isCorrect = option === round.correctAnswer;
            const isIncorrect = isSelected && !isCorrect && showResult;
            
            let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';
            if (showResult) {
              if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';
              else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';
            } else if (isSelected) {
              optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';
            }

            return (
              <button
                key={index}
                className={optionClass}
                onClick={() => handleAnswerSelect(option)}
                disabled={showResult}
              >
                <div className="flex items-center justify-between">
                  <span className="text-left text-white font-medium">{option}</span>
                  {showResult && isCorrect && (
                    <span className="text-green-400 text-xl">✓</span>
                  )}
                  {showResult && isIncorrect && (
                    <span className="text-red-400 text-xl">✗</span>
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Result Explanation */}
        {showResult && (
          <div className="mt-6 p-4 bg-gray-700 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="text-3xl">{round.country.flagUrl}</div>
              <div>
                <h4 className="text-white font-bold text-lg">{round.country.name}</h4>
                <p className="text-gray-300 text-sm mb-2">
                  Capital: {round.country.capital} | Region: {round.country.region}
                </p>
                <p className="text-gray-400 text-sm">
                  {round.clueType === 'landmark' && `${round.clue} is one of the famous landmarks in ${round.country.name}.`}
                  {round.clueType === 'food' && `${round.clue} is a traditional dish from ${round.country.name}.`}
                  {round.clueType === 'culture' && `${round.clue} is a popular music style in ${round.country.name}.`}
                  {round.clueType === 'wildlife' && `${round.clue} can be found in the wild areas of ${round.country.name}.`}
                  {round.clueType === 'export' && `${round.clue} is one of the major exports of ${round.country.name}.`}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {correctAnswers >= 8 ? '🏆' : correctAnswers >= 6 ? '🎉' : '🗺️'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Adventure Complete!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Correct Answers: <span className="text-green-400">{correctAnswers}</span>/{totalRounds}</p>
                  <p>Final Score: <span className="text-yellow-400 font-bold">{Math.round((correctAnswers / totalRounds) * 100)}%</span></p>
                  <p className="text-sm">
                    {correctAnswers >= 8 ? 'Africa Expert!' : 
                     correctAnswers >= 6 ? 'Great Explorer!' : 
                     'Keep Exploring!'}
                  </p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Explore Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WhereInAfrica;
