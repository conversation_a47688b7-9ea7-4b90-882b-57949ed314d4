'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray, getRandomItems } from '@/utils';

interface FlagMatchingProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface MatchingPair {
  id: string;
  country: Country;
  isMatched: boolean;
  isSelected: boolean;
}

interface GameRound {
  flags: MatchingPair[];
  names: MatchingPair[];
  selectedFlag: string | null;
  selectedName: string | null;
  matches: number;
  attempts: number;
}

type DifficultyLevel = 'easy' | 'medium' | 'hard';

const FlagMatching: React.FC<FlagMatchingProps> = ({ countries, onComplete }) => {
  const [currentRound, setCurrentRound] = useState<GameRound | null>(null);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(120);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('easy');
  const [gameStarted, setGameStarted] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const [streak, setStreak] = useState(0);
  const [totalMatches, setTotalMatches] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const [lastMatchedCountry, setLastMatchedCountry] = useState<Country | null>(null);

  const getDifficultySettings = (diff: DifficultyLevel) => {
    switch (diff) {
      case 'easy': return { pairs: 6, timeLimit: 120, multiplier: 1 };
      case 'medium': return { pairs: 8, timeLimit: 100, multiplier: 1.5 };
      case 'hard': return { pairs: 10, timeLimit: 80, multiplier: 2 };
    }
  };

  const generateRound = (): GameRound => {
    const settings = getDifficultySettings(difficulty);
    const selectedCountries = getRandomItems(countries, settings.pairs);
    
    const flags: MatchingPair[] = selectedCountries.map(country => ({
      id: `flag-${country.id}`,
      country,
      isMatched: false,
      isSelected: false,
    }));

    const names: MatchingPair[] = selectedCountries.map(country => ({
      id: `name-${country.id}`,
      country,
      isMatched: false,
      isSelected: false,
    }));

    return {
      flags: shuffleArray(flags),
      names: shuffleArray(names),
      selectedFlag: null,
      selectedName: null,
      matches: 0,
      attempts: 0,
    };
  };

  useEffect(() => {
    if (gameStarted && timeLeft > 0 && !gameComplete) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 || (currentRound && currentRound.matches === getDifficultySettings(difficulty).pairs)) {
      handleGameEnd();
    }
  }, [gameStarted, timeLeft, gameComplete, currentRound]);

  const startGame = () => {
    const settings = getDifficultySettings(difficulty);
    const newRound = generateRound();
    setCurrentRound(newRound);
    setScore(0);
    setStreak(0);
    setTotalMatches(0);
    setTimeLeft(settings.timeLimit);
    setGameStarted(true);
    setGameComplete(false);
    setShowCelebration(false);
  };

  const handleFlagClick = (flagId: string) => {
    if (!currentRound || gameComplete) return;
    
    const flag = currentRound.flags.find(f => f.id === flagId);
    if (!flag || flag.isMatched) return;

    // Clear previous selections
    const updatedFlags = currentRound.flags.map(f => ({
      ...f,
      isSelected: f.id === flagId
    }));

    const updatedNames = currentRound.names.map(n => ({
      ...n,
      isSelected: false
    }));

    setCurrentRound({
      ...currentRound,
      flags: updatedFlags,
      names: updatedNames,
      selectedFlag: flagId,
      selectedName: null,
    });
  };

  const handleNameClick = (nameId: string) => {
    if (!currentRound || gameComplete) return;
    
    const name = currentRound.names.find(n => n.id === nameId);
    if (!name || name.isMatched) return;

    const updatedNames = currentRound.names.map(n => ({
      ...n,
      isSelected: n.id === nameId
    }));

    const newRound = {
      ...currentRound,
      names: updatedNames,
      selectedName: nameId,
      attempts: currentRound.attempts + 1,
    };

    // Check for match if both flag and name are selected
    if (currentRound.selectedFlag) {
      const selectedFlag = currentRound.flags.find(f => f.id === currentRound.selectedFlag);
      const selectedName = name;

      if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {
        // Match found!
        const updatedFlags = newRound.flags.map(f => ({
          ...f,
          isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,
          isSelected: false,
        }));

        const updatedNamesMatched = newRound.names.map(n => ({
          ...n,
          isMatched: n.id === nameId ? true : n.isMatched,
          isSelected: false,
        }));

        const settings = getDifficultySettings(difficulty);
        const basePoints = 10;
        const timeBonus = Math.floor(timeLeft / 10);
        const streakBonus = streak * 2;
        const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);

        setScore(score + roundScore);
        setStreak(streak + 1);
        setTotalMatches(totalMatches + 1);
        setLastMatchedCountry(selectedFlag.country);
        setShowCelebration(true);

        setTimeout(() => setShowCelebration(false), 2000);

        setCurrentRound({
          ...newRound,
          flags: updatedFlags,
          names: updatedNamesMatched,
          matches: newRound.matches + 1,
          selectedFlag: null,
          selectedName: null,
        });
      } else {
        // No match - reset selections after brief delay
        setStreak(0);
        setTimeout(() => {
          if (currentRound) {
            const resetFlags = newRound.flags.map(f => ({ ...f, isSelected: false }));
            const resetNames = newRound.names.map(n => ({ ...n, isSelected: false }));
            
            setCurrentRound({
              ...newRound,
              flags: resetFlags,
              names: resetNames,
              selectedFlag: null,
              selectedName: null,
            });
          }
        }, 1000);
      }
    } else {
      setCurrentRound(newRound);
    }
  };

  const handleGameEnd = () => {
    setGameComplete(true);
    setTimeout(() => onComplete(score), 1000);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🏁 Flag Matching Game</h2>
          <div className="text-6xl mb-6">🌍</div>
          <p className="text-xl text-gray-300 mb-6">
            Match African country flags with their names!
          </p>
          
          {/* Difficulty Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Choose Difficulty:</h3>
            <div className="flex justify-center gap-4">
              {(['easy', 'medium', 'hard'] as DifficultyLevel[]).map((diff) => {
                const settings = getDifficultySettings(diff);
                return (
                  <button
                    key={diff}
                    onClick={() => setDifficulty(diff)}
                    className={`px-6 py-3 rounded-lg transition-colors ${
                      difficulty === diff
                        ? 'bg-yellow-400 text-gray-900'
                        : 'bg-gray-700 text-white hover:bg-gray-600'
                    }`}
                  >
                    <div className="text-center">
                      <div className="font-semibold capitalize">{diff}</div>
                      <div className="text-sm">
                        {settings.pairs} pairs • {settings.timeLimit}s
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Click a flag, then click the matching country name</p>
            <p>• Complete all pairs before time runs out</p>
            <p>• Build streaks for bonus points</p>
            <p>• Learn about all 50+ African countries</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Matching
          </button>
        </div>
      </div>
    );
  }

  if (!currentRound) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  const settings = getDifficultySettings(difficulty);

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🏁 Flag Matching Game</h2>
          <div className="text-yellow-400 text-xl font-bold">
            {formatTime(timeLeft)}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{streak}</div>
            <div className="text-gray-400 text-sm">Streak</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{currentRound.matches}</div>
            <div className="text-gray-400 text-sm">Matches</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{settings.pairs}</div>
            <div className="text-gray-400 text-sm">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-400 capitalize">{difficulty}</div>
            <div className="text-gray-400 text-sm">Difficulty</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentRound.matches / settings.pairs) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Game Instructions */}
      <div className="text-center mb-6">
        <p className="text-gray-300">
          {currentRound.selectedFlag 
            ? "Now click the matching country name!" 
            : "Click a flag to start matching!"
          }
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Flags Column */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4 text-center">🏁 Flags</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {currentRound.flags.map((flag) => (
              <button
                key={flag.id}
                onClick={() => handleFlagClick(flag.id)}
                disabled={flag.isMatched}
                className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                  flag.isMatched 
                    ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed'
                    : flag.isSelected
                    ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105'
                    : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'
                }`}
              >
                <div className="text-center">
                  <div className="text-4xl mb-2">{flag.country.flagUrl}</div>
                  {flag.isMatched && (
                    <div className="text-green-400 text-sm font-medium">✓ Matched</div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Names Column */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4 text-center">📝 Country Names</h3>
          <div className="space-y-2">
            {currentRound.names.map((name) => (
              <button
                key={name.id}
                onClick={() => handleNameClick(name.id)}
                disabled={name.isMatched}
                className={`w-full p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                  name.isMatched 
                    ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed'
                    : name.isSelected
                    ? 'bg-yellow-400 bg-opacity-20 border-yellow-400'
                    : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">{name.country.name}</span>
                  {name.isMatched && (
                    <span className="text-green-400">✓</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Celebration Animation */}
      {showCelebration && lastMatchedCountry && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <div className="bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce">
            <div className="text-4xl mb-2">{lastMatchedCountry.flagUrl}</div>
            <div className="text-white font-bold text-xl">Perfect Match!</div>
            <div className="text-green-100">{lastMatchedCountry.name}</div>
          </div>
        </div>
      )}

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  {currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\'s Up!'}
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                  <p>Matches: <span className="text-green-400">{currentRound.matches}</span>/{settings.pairs}</p>
                  <p>Best Streak: <span className="text-blue-400">{streak}</span></p>
                  <p>Accuracy: <span className="text-purple-400">{Math.round((currentRound.matches / Math.max(currentRound.attempts, 1)) * 100)}%</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlagMatching;
