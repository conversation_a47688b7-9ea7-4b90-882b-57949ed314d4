"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_games_CountryNameScramble_tsx";
exports.ids = ["_ssr_src_components_games_CountryNameScramble_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(ssr)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(ssr)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(ssr)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(ssr)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst CountryNameScramble = ({ countries, onComplete })=>{\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: `letter-${index}-${Math.random()}`,\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = (newDifficulty)=>{\n        const targetDifficulty = newDifficulty || difficulty;\n        setDifficulty(targetDifficulty);\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setShowVictory(false);\n        setCompletionData(null);\n        setRoundStartTime(Date.now());\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const totalTime = 60;\n        const completionTime = (Date.now() - gameStartTime) / 1000;\n        const correctAnswers = rounds.filter((r)=>r.isComplete && r.userAnswer.replace(/\\s+/g, '').toUpperCase() === r.country.name.replace(/\\s+/g, '').toUpperCase()).length;\n        const gameData = {\n            gameType: 'country-name-scramble',\n            score,\n            timeRemaining: timeLeft,\n            totalTime,\n            perfectScore: correctAnswers === totalRounds,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletionWithProgression)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        // Check for immediate completion\n        if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['country-name-scramble'](currentRound, totalRounds - 1, true)) {\n            setTimeout(()=>{\n                handleImmediateCompletion();\n            }, 2000); // Brief delay to show the result\n        } else {\n            setTimeout(()=>{\n                if (currentRound < totalRounds - 1) {\n                    setCurrentRound(currentRound + 1);\n                    setUserAnswer('');\n                    setSelectedLetters([]);\n                    setShowResult(false);\n                    setShowHint(false);\n                    setRoundStartTime(Date.now());\n                    // Reset letters for next round\n                    const nextRoundData = updatedRounds[currentRound + 1];\n                    const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                            ...l,\n                            isUsed: false\n                        }));\n                    updatedRounds[currentRound + 1] = {\n                        ...nextRoundData,\n                        scrambledLetters: resetLetters\n                    };\n                    setRounds(updatedRounds);\n                } else {\n                    handleImmediateCompletion();\n                }\n            }, 3000);\n        }\n    };\n    const handleGameEnd = ()=>{\n        if (!showVictory) {\n            // Time ran out - no victory animation, just complete\n            setGameComplete(true);\n            setTimeout(()=>onComplete(score), 1000);\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const handleAutoProgress = (action, nextDifficulty)=>{\n        if (action === 'next-difficulty' && nextDifficulty) {\n            // Start the same game with next difficulty\n            startGame(nextDifficulty);\n        } else {\n            // Return to menu\n            if (completionData) {\n                onComplete(completionData.finalScore);\n            }\n        }\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: `px-6 py-3 rounded-lg transition-colors ${difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${(currentRound + 1) / totalRounds * 100}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: `w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 ${letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'}`,\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mt-6 p-4 rounded-lg border ${isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-2xl font-bold mb-2 ${isCorrect ? 'text-green-400' : 'text-red-400'}`,\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            countryId: currentCountry.id,\n                                            size: \"large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete,\n                onAutoProgress: handleAutoProgress\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && !showVictory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"⏰\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Time's Up!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Rounds Completed: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 40\n                                                    }, undefined),\n                                                    \"/\",\n                                                    totalRounds\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 522,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/games/CountryNameScramble.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(ssr)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FlagImage = ({ countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick })=>{\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = `${flagImageUrl}?cache=1`;\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = `\n    ${sizeClasses[size]} \n    object-cover \n    rounded-sm \n    border \n    border-gray-300 \n    shadow-sm\n    ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}\n    ${className}\n  `;\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${baseClasses} bg-gray-200 animate-pulse flex items-center justify-center`,\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n          ${sizeClasses[size]} \n          flex \n          items-center \n          justify-center \n          bg-gray-100 \n          rounded-sm \n          border \n          border-gray-300\n          ${onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : ''}\n          ${className}\n        `,\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${baseClasses} bg-gray-100 flex items-center justify-center`,\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FlagImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/VictoryAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/VictoryAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(ssr)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst VictoryAnimation = ({ isVisible, completionData, onComplete, onAutoProgress })=>{\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationPhase, setAnimationPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('enter');\n    const [progressCountdown, setProgressCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProgressing, setIsProgressing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__.celebrationConfigs[completionData.celebrationType];\n    const progressDelay = completionData.autoProgression.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VictoryAnimation.useEffect\": ()=>{\n            if (!isVisible) return;\n            setShowConfetti(true);\n            setAnimationPhase('enter');\n            setIsProgressing(false);\n            // Start countdown for auto-progression\n            const countdownStart = Math.ceil(progressDelay / 1000);\n            setProgressCountdown(countdownStart);\n            const timeline = [\n                // Enter phase\n                {\n                    delay: 0,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('enter')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Display phase\n                {\n                    delay: 300,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('display')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Start countdown\n                {\n                    delay: 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            const countdownInterval = setInterval({\n                                \"VictoryAnimation.useEffect.countdownInterval\": ()=>{\n                                    setProgressCountdown({\n                                        \"VictoryAnimation.useEffect.countdownInterval\": (prev)=>{\n                                            if (prev <= 1) {\n                                                clearInterval(countdownInterval);\n                                                return 0;\n                                            }\n                                            return prev - 1;\n                                        }\n                                    }[\"VictoryAnimation.useEffect.countdownInterval\"]);\n                                }\n                            }[\"VictoryAnimation.useEffect.countdownInterval\"], 1000);\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Exit phase\n                {\n                    delay: progressDelay - 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('exit')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Auto-progress\n                {\n                    delay: progressDelay,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            setShowConfetti(false);\n                            setIsProgressing(true);\n                            if (onAutoProgress) {\n                                onAutoProgress(completionData.autoProgression.action, completionData.autoProgression.nextDifficulty);\n                            } else {\n                                onComplete();\n                            }\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                }\n            ];\n            const timeouts = timeline.map({\n                \"VictoryAnimation.useEffect.timeouts\": ({ delay, action })=>setTimeout(action, delay)\n            }[\"VictoryAnimation.useEffect.timeouts\"]);\n            return ({\n                \"VictoryAnimation.useEffect\": ()=>{\n                    timeouts.forEach(clearTimeout);\n                }\n            })[\"VictoryAnimation.useEffect\"];\n        }\n    }[\"VictoryAnimation.useEffect\"], [\n        isVisible,\n        progressDelay,\n        onComplete,\n        onAutoProgress,\n        completionData.autoProgression\n    ]);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: config.particles\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute animate-bounce\",\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`,\n                            animationDelay: `${Math.random() * 2}s`,\n                            animationDuration: `${1 + Math.random() * 2}s`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full\",\n                            style: {\n                                backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],\n                                transform: `rotate(${Math.random() * 360}deg)`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          bg-gradient-to-br from-yellow-400 to-orange-500 \n          rounded-2xl p-8 text-center max-w-md mx-4 \n          transform transition-all duration-500 ease-out\n          ${animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : ''}\n          ${animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : ''}\n          ${animationPhase === 'exit' ? 'scale-110 opacity-90' : ''}\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-8xl mb-4 animate-pulse\",\n                        children: config.emoji\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: completionData.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completionData.finalScore\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Final Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                \"+\",\n                                                completionData.timeBonus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Time Bonus\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-blue-900 font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: [\n                                        \"+\",\n                                        completionData.xpGained\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    children: \"XP Gained!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    completionData.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-900 font-semibold text-sm\",\n                                children: \"Achievements Unlocked:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            completionData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm\",\n                                    style: {\n                                        animationDelay: `${index * 200}ms`\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 \",\n                                        achievement\n                                    ]\n                                }, achievement, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-300 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out\",\n                                    style: {\n                                        width: animationPhase === 'display' ? `${100 - progressCountdown / Math.ceil(progressDelay / 1000) * 100}%` : '0%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-700 text-xs mt-2\",\n                                children: isProgressing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 font-semibold\",\n                                    children: \"Progressing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined) : progressCountdown > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: completionData.autoProgression.action === 'next-difficulty' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Starting \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold capitalize\",\n                                                children: completionData.autoProgression.nextDifficulty\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 30\n                                            }, undefined),\n                                            \" difficulty in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Returning to menu in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-semibold\",\n                                    children: \"Ready!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-yellow-300 text-2xl animate-ping\",\n                            style: {\n                                left: `${20 + Math.random() * 60}%`,\n                                top: `${20 + Math.random() * 60}%`,\n                                animationDelay: `${Math.random() * 2}s`,\n                                animationDuration: `${2 + Math.random() * 2}s`\n                            },\n                            children: \"⭐\"\n                        }, `star-${i}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from({\n                        length: 12\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-white text-lg animate-bounce\",\n                            style: {\n                                left: `${10 + Math.random() * 80}%`,\n                                top: `${10 + Math.random() * 80}%`,\n                                animationDelay: `${Math.random() * 3}s`,\n                                animationDuration: `${1 + Math.random() * 2}s`\n                            },\n                            children: \"✨\"\n                        }, `sparkle-${i}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            animationPhase === 'display' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-white animate-bounce\",\n                    children: [\n                        completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊',\n                        completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟',\n                        completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏',\n                        completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VictoryAnimation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/VictoryAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = (countryId, format = 'svg')=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : `Flag of ${countryId}`;\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/flagUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/gameCompletion.ts":
/*!*************************************!*\
  !*** ./src/utils/gameCompletion.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   calculateXP: () => (/* binding */ calculateXP),\n/* harmony export */   celebrationConfigs: () => (/* binding */ celebrationConfigs),\n/* harmony export */   checkGameCompletion: () => (/* binding */ checkGameCompletion),\n/* harmony export */   getCompletionMessage: () => (/* binding */ getCompletionMessage),\n/* harmony export */   getNextAction: () => (/* binding */ getNextAction),\n/* harmony export */   processGameCompletion: () => (/* binding */ processGameCompletion),\n/* harmony export */   processGameCompletionWithProgression: () => (/* binding */ processGameCompletionWithProgression)\n/* harmony export */ });\n// Game completion utilities and animations\n// Calculate time bonus based on remaining time\nconst calculateTimeBonus = (timeRemaining, totalTime, difficulty)=>{\n    if (timeRemaining <= 0) return 0;\n    const timePercentage = timeRemaining / totalTime;\n    const difficultyMultiplier = {\n        easy: 1,\n        medium: 1.5,\n        hard: 2\n    }[difficulty];\n    // Base time bonus: up to 50 points for completing with full time remaining\n    const baseBonus = Math.floor(timePercentage * 50);\n    return Math.floor(baseBonus * difficultyMultiplier);\n};\n// Calculate XP based on performance\nconst calculateXP = (data)=>{\n    const baseXP = 20; // Base XP for completion\n    const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const difficultyBonus = {\n        easy: 0,\n        medium: 10,\n        hard: 20\n    }[data.difficulty];\n    const perfectBonus = data.perfectScore ? 30 : 0;\n    return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;\n};\n// Determine completion message and type\nconst getCompletionMessage = (data)=>{\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore && timePercentage > 0.7) {\n        return {\n            message: 'Perfect! Outstanding performance!',\n            type: 'perfect'\n        };\n    } else if (data.perfectScore && timePercentage > 0.4) {\n        return {\n            message: 'Excellent! Great job!',\n            type: 'excellent'\n        };\n    } else if (data.perfectScore) {\n        return {\n            message: 'Perfect Score! Well done!',\n            type: 'excellent'\n        };\n    } else if (timePercentage > 0.5) {\n        return {\n            message: 'Great work! Fast completion!',\n            type: 'good'\n        };\n    } else {\n        return {\n            message: 'Game Complete! Nice job!',\n            type: 'complete'\n        };\n    }\n};\n// Process game completion\nconst processGameCompletion = (data)=>{\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const finalScore = data.score + timeBonus;\n    const xpGained = calculateXP(data);\n    const { message, type } = getCompletionMessage(data);\n    // Determine achievements based on performance\n    const achievements = [];\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore) {\n        achievements.push('Perfect Score');\n    }\n    if (timePercentage > 0.8) {\n        achievements.push('Speed Demon');\n    }\n    if (data.difficulty === 'hard' && data.perfectScore) {\n        achievements.push('Master Player');\n    }\n    if (timeBonus > 30) {\n        achievements.push('Time Master');\n    }\n    return {\n        finalScore,\n        timeBonus,\n        xpGained,\n        achievements,\n        message,\n        celebrationType: type\n    };\n};\n// Game-specific completion checkers\nconst checkGameCompletion = {\n    'flag-matching': (matches, totalPairs)=>{\n        return matches >= totalPairs;\n    },\n    'country-name-scramble': (currentRound, totalRounds, roundComplete)=>{\n        return currentRound >= totalRounds - 1 && roundComplete;\n    },\n    'jigsaw-puzzle': (placedPieces, totalPieces)=>{\n        return placedPieces >= totalPieces;\n    },\n    'quiz': (answeredQuestions, totalQuestions)=>{\n        return answeredQuestions >= totalQuestions;\n    },\n    'memory-grid': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'matching': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'country-explorer': (visitedCountries, targetCountries)=>{\n        return visitedCountries >= targetCountries;\n    },\n    'speed-challenge': (answeredQuestions, targetQuestions)=>{\n        return answeredQuestions >= targetQuestions;\n    },\n    'mystery-land': (correctGuesses, totalRounds)=>{\n        return correctGuesses >= totalRounds;\n    },\n    'timeline-builder': (placedEvents, totalEvents)=>{\n        return placedEvents >= totalEvents;\n    },\n    'where-in-africa': (completedRounds, totalRounds)=>{\n        return completedRounds >= totalRounds;\n    },\n    'dress-character': (completedOutfits, targetOutfits)=>{\n        return completedOutfits >= targetOutfits;\n    }\n};\n// Celebration animation configurations\nconst celebrationConfigs = {\n    perfect: {\n        duration: 3000,\n        emoji: '🏆',\n        colors: [\n            '#FFD700',\n            '#FFA500',\n            '#FF6B6B'\n        ],\n        particles: 50\n    },\n    excellent: {\n        duration: 2500,\n        emoji: '🎉',\n        colors: [\n            '#4ECDC4',\n            '#45B7D1',\n            '#96CEB4'\n        ],\n        particles: 40\n    },\n    good: {\n        duration: 2000,\n        emoji: '👏',\n        colors: [\n            '#FECA57',\n            '#48CAE4',\n            '#A8E6CF'\n        ],\n        particles: 30\n    },\n    complete: {\n        duration: 1500,\n        emoji: '✅',\n        colors: [\n            '#6C5CE7',\n            '#A29BFE',\n            '#74B9FF'\n        ],\n        particles: 20\n    }\n};\n// Auto-progression logic\nconst getNextAction = (gameType, difficulty)=>{\n    // If completed on easy, progress to medium\n    if (difficulty === 'easy') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'medium'\n        };\n    }\n    // If completed on medium, progress to hard\n    if (difficulty === 'medium') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'hard'\n        };\n    }\n    // If completed on hard, return to menu\n    return {\n        action: 'menu'\n    };\n};\n// Process game completion with auto-progression\nconst processGameCompletionWithProgression = (data)=>{\n    const baseResult = processGameCompletion(data);\n    const progression = getNextAction(data.gameType, data.difficulty);\n    // Determine delay based on celebration type\n    const delay = {\n        perfect: 3000,\n        excellent: 2500,\n        good: 2000,\n        complete: 1500\n    }[baseResult.celebrationType];\n    return {\n        ...baseResult,\n        autoProgression: {\n            ...progression,\n            delay\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/gameCompletion.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = (newSeed = 1)=>{\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = (correct, total, timeBonus = 0, difficultyMultiplier = 1, streakBonus = 0)=>{\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = (countries, category, difficulty, count = 10)=>{\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the capital of ${country.name}?`,\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: `${country.capital} is the capital city of ${country.name}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the currency of ${country.name}?`,\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: `The currency of ${country.name} is ${country.currency}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `Which region is ${country.name} located in?`,\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: `${country.name} is located in ${country.region}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: `When did ${country.name} gain independence?`,\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: `${country.name} gained independence in ${new Date(country.independence).getFullYear()}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: `Which country is known for ${item}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${item} is a traditional ${aspect.slice(0, -1)} from ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: `Which country is home to ${animal}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${animal} can be found in ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: `${figure.name} is from which country?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${figure.name} is from ${country.name}. ${figure.achievement}`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n};\nconst formatScore = (score)=>{\n    return `${score}%`;\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/index.ts\n");

/***/ })

};
;