"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when pieces are placed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory && pieces.length > 0) {\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['jigsaw-puzzle'](placedPieces.length, pieces.length)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        placedPieces.length,\n        pieces.length,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const completionTime = timeElapsed;\n        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n        const moveBonus = Math.max(0, 50 - moves);\n        const finalScore = score + timeBonus + moveBonus;\n        // Determine difficulty based on region\n        let difficulty = 'medium';\n        if (selectedRegion === 'North Africa') difficulty = 'easy';\n        else if (selectedRegion === 'West Africa' || selectedRegion === 'All') difficulty = 'hard';\n        const gameData = {\n            gameType: 'jigsaw-puzzle',\n            score: finalScore,\n            timeRemaining: 0,\n            totalTime: timeElapsed,\n            perfectScore: moves <= pieces.length,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletion)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n        // Completion will be handled by useEffect watching placedPieces.length\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"F1WMg8rV4yTrAAU+RCIaeq7Sh7U=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});