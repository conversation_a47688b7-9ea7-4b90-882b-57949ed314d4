'use client';

import React, { useState, useEffect } from 'react';
import { Country, QuizQuestion } from '@/types';
import { generateQuizQuestions, shuffleArray } from '@/utils';

interface SpeedChallengeProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

const SpeedChallenge: React.FC<SpeedChallengeProps> = ({ countries, onComplete }) => {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(60);
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [correctAnswers, setCorrectAnswers] = useState(0);
  const [totalQuestions, setTotalQuestions] = useState(0);

  useEffect(() => {
    if (gameStarted && timeRemaining > 0 && !gameComplete) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0) {
      endGame();
    }
  }, [timeRemaining, gameStarted, gameComplete]);

  const startGame = () => {
    // Generate a large pool of questions
    const allQuestions = [
      ...generateQuizQuestions(countries, 'geography', 'easy', 20),
      ...generateQuizQuestions(countries, 'history', 'easy', 15),
      ...generateQuizQuestions(countries, 'culture', 'easy', 15),
    ];
    
    setQuestions(shuffleArray(allQuestions));
    setCurrentQuestion(0);
    setTimeRemaining(60);
    setScore(0);
    setStreak(0);
    setGameStarted(true);
    setGameComplete(false);
    setSelectedAnswer(null);
    setShowFeedback(false);
    setCorrectAnswers(0);
    setTotalQuestions(0);
  };

  const handleAnswerSelect = (answer: string) => {
    if (showFeedback) return;
    
    setSelectedAnswer(answer);
    setShowFeedback(true);
    setTotalQuestions(totalQuestions + 1);

    const isCorrect = answer === questions[currentQuestion]?.correctAnswer;
    
    if (isCorrect) {
      setCorrectAnswers(correctAnswers + 1);
      setStreak(streak + 1);
      
      // Calculate points with streak bonus
      let points = 10;
      if (streak >= 2) points += 5; // Streak bonus
      if (streak >= 5) points += 10; // Super streak bonus
      
      setScore(score + points);
    } else {
      setStreak(0);
    }

    // Move to next question after short delay
    setTimeout(() => {
      if (currentQuestion < questions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
        setSelectedAnswer(null);
        setShowFeedback(false);
      } else {
        // Generate more questions if we run out
        const moreQuestions = [
          ...generateQuizQuestions(countries, 'geography', 'medium', 10),
          ...generateQuizQuestions(countries, 'culture', 'medium', 10),
        ];
        setQuestions([...questions, ...shuffleArray(moreQuestions)]);
        setCurrentQuestion(currentQuestion + 1);
        setSelectedAnswer(null);
        setShowFeedback(false);
      }
    }, 1000);
  };

  const endGame = () => {
    setGameComplete(true);
    setGameStarted(false);
    
    // Calculate final score with accuracy bonus
    const accuracy = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
    const finalScore = Math.round(score + (accuracy * 0.5));
    
    setTimeout(() => onComplete(finalScore), 1000);
  };

  const currentQ = questions[currentQuestion];

  if (!gameStarted && !gameComplete) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">⚡ Speed Challenge</h2>
          <div className="text-6xl mb-6">🏃‍♂️💨</div>
          <p className="text-xl text-gray-300 mb-6">
            Answer as many questions as possible in 60 seconds!
          </p>
          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Quick-fire questions about African countries</p>
            <p>• Build streaks for bonus points</p>
            <p>• 10 points per correct answer</p>
            <p>• +5 bonus for 3+ streak, +10 for 5+ streak</p>
          </div>
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Challenge!
          </button>
        </div>
      </div>
    );
  }

  if (!currentQ) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header with Timer and Stats */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">⚡ Speed Challenge</h2>
          <div className={`text-3xl font-bold ${timeRemaining <= 10 ? 'text-red-400 animate-pulse' : 'text-yellow-400'}`}>
            {timeRemaining}s
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{correctAnswers}</div>
            <div className="text-gray-400 text-sm">Correct</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{streak}</div>
            <div className="text-gray-400 text-sm">Streak</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{totalQuestions}</div>
            <div className="text-gray-400 text-sm">Total</div>
          </div>
        </div>

        {/* Streak Indicator */}
        {streak >= 3 && (
          <div className="mt-4 text-center">
            <span className={`px-4 py-2 rounded-full text-sm font-bold ${
              streak >= 5 ? 'bg-red-500 text-white animate-pulse' : 'bg-orange-500 text-white'
            }`}>
              🔥 {streak >= 5 ? 'SUPER STREAK!' : 'ON FIRE!'} +{streak >= 5 ? 10 : 5} bonus points
            </span>
          </div>
        )}
      </div>

      {/* Question */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-6">
          {currentQ.question}
        </h3>

        {/* Options */}
        <div className="grid gap-3">
          {currentQ.options?.map((option, index) => {
            const isSelected = selectedAnswer === option;
            const isCorrect = option === currentQ.correctAnswer;
            const isIncorrect = isSelected && !isCorrect && showFeedback;
            
            let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';
            if (showFeedback) {
              if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';
              else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';
            } else if (isSelected) {
              optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';
            }

            return (
              <button
                key={index}
                className={optionClass}
                onClick={() => handleAnswerSelect(option)}
                disabled={showFeedback}
              >
                <div className="flex items-center justify-between">
                  <span className="text-left text-white">{option}</span>
                  {showFeedback && isCorrect && (
                    <span className="text-green-400">✓</span>
                  )}
                  {showFeedback && isIncorrect && (
                    <span className="text-red-400">✗</span>
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-yellow-400 h-2 rounded-full transition-all duration-1000"
              style={{ width: `${((60 - timeRemaining) / 60) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {score >= 200 ? '🏆' : score >= 100 ? '🎉' : '💪'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Time's Up!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                  <p>Correct Answers: <span className="text-green-400">{correctAnswers}</span>/{totalQuestions}</p>
                  <p>Accuracy: <span className="text-blue-400">{totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0}%</span></p>
                  <p>Best Streak: <span className="text-purple-400">{streak}</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SpeedChallenge;
