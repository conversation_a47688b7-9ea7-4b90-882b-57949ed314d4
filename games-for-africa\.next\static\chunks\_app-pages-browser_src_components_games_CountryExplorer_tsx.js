"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_CountryExplorer_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/CountryExplorer.tsx":
/*!**************************************************!*\
  !*** ./src/components/games/CountryExplorer.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CountryExplorer = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [selectedCountry, setSelectedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [visitedCountries, setVisitedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentRegion, setCurrentRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const filteredCountries = currentRegion === 'All' ? countries : countries.filter((country)=>country.region === currentRegion);\n    const handleCountryClick = (country)=>{\n        setSelectedCountry(country);\n        setVisitedCountries(new Set([\n            ...visitedCountries,\n            country.id\n        ]));\n    };\n    const getCountryCardStyle = (country)=>{\n        const isVisited = visitedCountries.has(country.id);\n        const isSelected = (selectedCountry === null || selectedCountry === void 0 ? void 0 : selectedCountry.id) === country.id;\n        let baseStyle = 'p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105';\n        if (isSelected) {\n            baseStyle += ' bg-yellow-400 bg-opacity-20 border-yellow-400 shadow-lg';\n        } else if (isVisited) {\n            baseStyle += ' bg-green-500 bg-opacity-20 border-green-400';\n        } else {\n            baseStyle += ' bg-gray-700 border-gray-600 hover:border-yellow-400';\n        }\n        return baseStyle;\n    };\n    const getRandomFact = (country)=>{\n        const facts = [\n            \"\".concat(country.name, \" has a population of \").concat(country.population.toLocaleString(), \" people.\"),\n            \"The capital of \".concat(country.name, \" is \").concat(country.capital, \".\"),\n            \"\".concat(country.name, \" gained independence on \").concat(new Date(country.independence).toLocaleDateString(), \".\"),\n            \"The currency used in \".concat(country.name, \" is \").concat(country.currency, \".\"),\n            \"Languages spoken include: \".concat(country.languages.join(', '), \".\"),\n            \"Major exports include: \".concat(country.exports.slice(0, 3).join(', '), \".\"),\n            \"Famous landmarks: \".concat(country.landmarks.slice(0, 2).join(', '), \".\"),\n            \"Wildlife includes: \".concat(country.wildlife.slice(0, 3).join(', '), \".\")\n        ];\n        return facts[Math.floor(Math.random() * facts.length)];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-4\",\n                        children: \"\\uD83C\\uDF0D Country Explorer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Click on any African country to discover amazing facts and learn about its culture!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-lg font-semibold\",\n                                children: [\n                                    \"Countries Explored: \",\n                                    visitedCountries.size,\n                                    \" / \",\n                                    countries.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-700 rounded-full h-3 mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-400 h-3 rounded-full transition-all duration-500\",\n                                    style: {\n                                        width: \"\".concat(visitedCountries.size / countries.length * 100, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mb-6\",\n                        children: regions.map((region)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentRegion(region),\n                                className: \"px-4 py-2 rounded-lg transition-colors \".concat(currentRegion === region ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                children: region\n                            }, region, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    currentRegion === 'All' ? 'All African Countries' : currentRegion,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            filteredCountries.length,\n                                            \" countries)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\",\n                                children: filteredCountries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: getCountryCardStyle(country),\n                                        onClick: ()=>handleCountryClick(country),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        countryId: country.id,\n                                                        size: \"large\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: country.capital\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                visitedCountries.has(country.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-xs mt-1\",\n                                                    children: \"✓ Explored\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, country.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: selectedCountry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 sticky top-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                countryId: selectedCountry.id,\n                                                size: \"xl\",\n                                                className: \"mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: selectedCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-400\",\n                                            children: selectedCountry.capital\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Quick Facts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Region:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Population:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.population.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Currency:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.currency\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Independence:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: new Date(selectedCountry.independence).getFullYear()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Languages\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: selectedCountry.languages.map((language, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-500 bg-opacity-20 text-blue-300 px-2 py-1 rounded text-xs\",\n                                                            children: language\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Culture\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Traditional Food:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.culturalElements.cuisine.slice(0, 2).join(', ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Music:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.culturalElements.music.slice(0, 2).join(', ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        selectedCountry.notableFigures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Notable Figures\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: selectedCountry.notableFigures.slice(0, 2).map((figure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: figure.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: figure.field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-yellow-400 font-semibold mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 Did You Know?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: getRandomFact(selectedCountry)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Famous Landmarks\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: selectedCountry.landmarks.slice(0, 3).map((landmark, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                \"• \",\n                                                                landmark\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDDFA️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"Select a Country\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300\",\n                                    children: \"Click on any country to explore its culture, history, and interesting facts!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            visitedCountries.size === countries.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white p-4 rounded-lg shadow-lg animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl mb-1\",\n                            children: \"\\uD83C\\uDFC6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"Explorer Achievement!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: \"You've visited all countries!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryExplorer, \"/EGKITqhGmwkSf2VHS2sw/gNTZM=\");\n_c = CountryExplorer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryExplorer);\nvar _c;\n$RefreshReg$(_c, \"CountryExplorer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryExplorer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(app-pages-browser)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagImage = (param)=>{\n    let { countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick } = param;\n    _s();\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = \"\".concat(flagImageUrl, \"?cache=1\");\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    object-cover \\n    rounded-sm \\n    border \\n    border-gray-300 \\n    shadow-sm\\n    \").concat(onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : '', \"\\n    \").concat(className, \"\\n  \");\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-200 animate-pulse flex items-center justify-center\"),\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          \".concat(sizeClasses[size], \" \\n          flex \\n          items-center \\n          justify-center \\n          bg-gray-100 \\n          rounded-sm \\n          border \\n          border-gray-300\\n          \").concat(onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : '', \"\\n          \").concat(className, \"\\n        \"),\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(baseClasses, \" bg-gray-100 flex items-center justify-center\"),\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagImage, \"mgIVjR2Tvb/QtAQzLx0hN0fqtHE=\");\n_c = FlagImage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\nvar _c;\n$RefreshReg$(_c, \"FlagImage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FlagImage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = function(countryId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'svg';\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : \"Flag of \".concat(countryId);\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/flagUtils.ts\n"));

/***/ })

}]);