'use client';

import React, { useEffect, useState } from 'react';
import { CompletionResult, celebrationConfigs } from '@/utils/gameCompletion';

interface VictoryAnimationProps {
  isVisible: boolean;
  completionData: CompletionResult;
  onComplete: () => void;
}

const VictoryAnimation: React.FC<VictoryAnimationProps> = ({
  isVisible,
  completionData,
  onComplete
}) => {
  const [showConfetti, setShowConfetti] = useState(false);
  const [animationPhase, setAnimationPhase] = useState<'enter' | 'display' | 'exit'>('enter');

  const config = celebrationConfigs[completionData.celebrationType];

  useEffect(() => {
    if (!isVisible) return;

    setShowConfetti(true);
    setAnimationPhase('enter');

    const timeline = [
      // Enter phase
      { delay: 0, action: () => setAnimationPhase('enter') },
      // Display phase
      { delay: 300, action: () => setAnimationPhase('display') },
      // Exit phase
      { delay: config.duration - 500, action: () => setAnimationPhase('exit') },
      // Complete
      { delay: config.duration, action: () => {
        setShowConfetti(false);
        onComplete();
      }}
    ];

    const timeouts = timeline.map(({ delay, action }) => 
      setTimeout(action, delay)
    );

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, [isVisible, config.duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      {/* Confetti Background */}
      {showConfetti && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: config.particles }).map((_, i) => (
            <div
              key={i}
              className="absolute animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${1 + Math.random() * 2}s`,
              }}
            >
              <div
                className="w-2 h-2 rounded-full"
                style={{
                  backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],
                  transform: `rotate(${Math.random() * 360}deg)`,
                }}
              />
            </div>
          ))}
        </div>
      )}

      {/* Main Victory Card */}
      <div 
        className={`
          bg-gradient-to-br from-yellow-400 to-orange-500 
          rounded-2xl p-8 text-center max-w-md mx-4 
          transform transition-all duration-500 ease-out
          ${animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : ''}
          ${animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : ''}
          ${animationPhase === 'exit' ? 'scale-110 opacity-90' : ''}
        `}
      >
        {/* Main Emoji */}
        <div className="text-8xl mb-4 animate-pulse">
          {config.emoji}
        </div>

        {/* Success Message */}
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          {completionData.message}
        </h2>

        {/* Score Summary */}
        <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
          <div className="grid grid-cols-2 gap-4 text-gray-900">
            <div>
              <div className="text-2xl font-bold">{completionData.finalScore}</div>
              <div className="text-sm">Final Score</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-700">+{completionData.timeBonus}</div>
              <div className="text-sm">Time Bonus</div>
            </div>
          </div>
        </div>

        {/* XP Gained */}
        <div className="bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4">
          <div className="text-blue-900 font-bold">
            <span className="text-2xl">+{completionData.xpGained}</span>
            <span className="text-sm ml-2">XP Gained!</span>
          </div>
        </div>

        {/* Achievements */}
        {completionData.achievements.length > 0 && (
          <div className="space-y-2">
            <div className="text-gray-900 font-semibold text-sm">Achievements Unlocked:</div>
            {completionData.achievements.map((achievement, index) => (
              <div 
                key={achievement}
                className="bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                🏆 {achievement}
              </div>
            ))}
          </div>
        )}

        {/* Progress Indicator */}
        <div className="mt-6">
          <div className="w-full bg-gray-300 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out"
              style={{ 
                width: animationPhase === 'display' ? '100%' : '0%',
                transitionDelay: '500ms'
              }}
            />
          </div>
          <div className="text-gray-700 text-xs mt-2">
            Auto-continuing in {Math.ceil(config.duration / 1000)} seconds...
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Floating Stars */}
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={`star-${i}`}
            className="absolute text-yellow-300 text-2xl animate-ping"
            style={{
              left: `${20 + Math.random() * 60}%`,
              top: `${20 + Math.random() * 60}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          >
            ⭐
          </div>
        ))}

        {/* Floating Sparkles */}
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={`sparkle-${i}`}
            className="absolute text-white text-lg animate-bounce"
            style={{
              left: `${10 + Math.random() * 80}%`,
              top: `${10 + Math.random() * 80}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${1 + Math.random() * 2}s`,
            }}
          >
            ✨
          </div>
        ))}
      </div>

      {/* Celebration Text Overlay */}
      {animationPhase === 'display' && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2">
          <div className="text-6xl font-bold text-white animate-bounce">
            {completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊'}
            {completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟'}
            {completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏'}
            {completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'}
          </div>
        </div>
      )}
    </div>
  );
};

export default VictoryAnimation;
