'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray, getRandomItems } from '@/utils';

interface MatchingGameProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface MatchingPair {
  id: string;
  leftItem: string;
  rightItem: string;
  type: 'country-flag' | 'country-capital' | 'country-currency';
  matched: boolean;
}

const MatchingGame: React.FC<MatchingGameProps> = ({ countries, onComplete }) => {
  const [pairs, setPairs] = useState<MatchingPair[]>([]);
  const [leftItems, setLeftItems] = useState<string[]>([]);
  const [rightItems, setRightItems] = useState<string[]>([]);
  const [selectedLeft, setSelectedLeft] = useState<string | null>(null);
  const [selectedRight, setSelectedRight] = useState<string | null>(null);
  const [matchedPairs, setMatchedPairs] = useState<string[]>([]);
  const [score, setScore] = useState(0);
  const [moves, setMoves] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [matchType, setMatchType] = useState<'country-flag' | 'country-capital' | 'country-currency'>('country-capital');

  useEffect(() => {
    initializeGame();
  }, [countries, matchType]);

  const initializeGame = () => {
    const selectedCountries = getRandomItems(countries, 8);
    const newPairs: MatchingPair[] = selectedCountries.map((country, index) => {
      let rightItem = '';
      switch (matchType) {
        case 'country-flag':
          rightItem = country.flagUrl;
          break;
        case 'country-capital':
          rightItem = country.capital;
          break;
        case 'country-currency':
          rightItem = country.currency;
          break;
      }

      return {
        id: country.id,
        leftItem: country.name,
        rightItem,
        type: matchType,
        matched: false,
      };
    });

    setPairs(newPairs);
    setLeftItems(shuffleArray(newPairs.map(p => p.leftItem)));
    setRightItems(shuffleArray(newPairs.map(p => p.rightItem)));
    setMatchedPairs([]);
    setSelectedLeft(null);
    setSelectedRight(null);
    setScore(0);
    setMoves(0);
    setGameComplete(false);
  };

  const handleLeftClick = (item: string) => {
    if (matchedPairs.includes(item)) return;
    setSelectedLeft(item === selectedLeft ? null : item);
  };

  const handleRightClick = (item: string) => {
    if (matchedPairs.includes(item)) return;
    setSelectedRight(item === selectedRight ? null : item);
  };

  useEffect(() => {
    if (selectedLeft && selectedRight) {
      setMoves(moves + 1);
      
      // Find the pair that matches
      const matchingPair = pairs.find(
        pair => pair.leftItem === selectedLeft && pair.rightItem === selectedRight
      );

      if (matchingPair) {
        // Correct match
        setMatchedPairs([...matchedPairs, selectedLeft, selectedRight]);
        setScore(score + 10);
        
        // Check if game is complete
        if (matchedPairs.length + 2 === pairs.length * 2) {
          setGameComplete(true);
          const finalScore = Math.max(0, 100 - (moves - pairs.length) * 5);
          setTimeout(() => onComplete(finalScore), 1000);
        }
      }

      // Reset selections after a short delay
      setTimeout(() => {
        setSelectedLeft(null);
        setSelectedRight(null);
      }, 1000);
    }
  }, [selectedLeft, selectedRight]);

  const getItemStyle = (item: string, isLeft: boolean) => {
    const isSelected = isLeft ? selectedLeft === item : selectedRight === item;
    const isMatched = matchedPairs.includes(item);
    
    let baseStyle = 'p-4 m-2 rounded-lg border-2 cursor-pointer transition-all duration-200 text-center min-h-[80px] flex items-center justify-center';
    
    if (isMatched) {
      baseStyle += ' bg-green-500 bg-opacity-20 border-green-400 text-green-300';
    } else if (isSelected) {
      baseStyle += ' bg-yellow-400 bg-opacity-20 border-yellow-400 text-yellow-300';
    } else {
      baseStyle += ' bg-gray-700 border-gray-600 text-white hover:border-yellow-400';
    }
    
    return baseStyle;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-4">Matching Game</h2>
        <div className="flex justify-center space-x-6 mb-4">
          <div className="text-yellow-400">Score: {score}</div>
          <div className="text-gray-300">Moves: {moves}</div>
          <div className="text-gray-300">Matched: {matchedPairs.length / 2}/{pairs.length}</div>
        </div>
        
        {/* Game Type Selector */}
        <div className="flex justify-center space-x-4 mb-6">
          <button
            onClick={() => setMatchType('country-capital')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              matchType === 'country-capital' 
                ? 'bg-yellow-400 text-gray-900' 
                : 'bg-gray-700 text-white hover:bg-gray-600'
            }`}
          >
            Countries → Capitals
          </button>
          <button
            onClick={() => setMatchType('country-flag')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              matchType === 'country-flag' 
                ? 'bg-yellow-400 text-gray-900' 
                : 'bg-gray-700 text-white hover:bg-gray-600'
            }`}
          >
            Countries → Flags
          </button>
          <button
            onClick={() => setMatchType('country-currency')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              matchType === 'country-currency' 
                ? 'bg-yellow-400 text-gray-900' 
                : 'bg-gray-700 text-white hover:bg-gray-600'
            }`}
          >
            Countries → Currencies
          </button>
        </div>
      </div>

      {/* Game Board */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left Column - Countries */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4 text-center">Countries</h3>
          <div className="space-y-2">
            {leftItems.map((item, index) => (
              <div
                key={index}
                className={getItemStyle(item, true)}
                onClick={() => handleLeftClick(item)}
              >
                {item}
              </div>
            ))}
          </div>
        </div>

        {/* Right Column - Capitals/Flags/Currencies */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4 text-center">
            {matchType === 'country-capital' ? 'Capitals' : 
             matchType === 'country-flag' ? 'Flags' : 'Currencies'}
          </h3>
          <div className="space-y-2">
            {rightItems.map((item, index) => (
              <div
                key={index}
                className={getItemStyle(item, false)}
                onClick={() => handleRightClick(item)}
              >
                {matchType === 'country-flag' ? (
                  <span className="text-4xl">{item}</span>
                ) : (
                  item
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-8 text-center text-gray-300">
        <p>Click on a country, then click on its matching {
          matchType === 'country-capital' ? 'capital' : 
          matchType === 'country-flag' ? 'flag' : 'currency'
        } to make a pair!</p>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">🎉</div>
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Congratulations!
                </h3>
                <p className="text-gray-300">
                  You matched all pairs in {moves} moves!
                </p>
                <p className="text-xl text-yellow-400 mt-2">
                  Final Score: {Math.max(0, 100 - (moves - pairs.length) * 5)}%
                </p>
              </div>
              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={initializeGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MatchingGame;
