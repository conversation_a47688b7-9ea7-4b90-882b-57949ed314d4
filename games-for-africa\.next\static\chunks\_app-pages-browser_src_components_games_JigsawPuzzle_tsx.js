"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_JigsawPuzzle_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when pieces are placed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory && pieces.length > 0) {\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['jigsaw-puzzle'](placedPieces.length, pieces.length)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        placedPieces.length,\n        pieces.length,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const completionTime = timeElapsed;\n        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n        const moveBonus = Math.max(0, 50 - moves);\n        const finalScore = score + timeBonus + moveBonus;\n        // Determine difficulty based on region\n        let difficulty = 'medium';\n        if (selectedRegion === 'North Africa') difficulty = 'easy';\n        else if (selectedRegion === 'West Africa' || selectedRegion === 'All') difficulty = 'hard';\n        const gameData = {\n            gameType: 'jigsaw-puzzle',\n            score: finalScore,\n            timeRemaining: 0,\n            totalTime: timeElapsed,\n            perfectScore: moves <= pieces.length,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletionWithProgression)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n        // Completion will be handled by useEffect watching placedPieces.length\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const handleAutoProgress = (action, nextDifficulty)=>{\n        if (action === 'next-difficulty' && nextDifficulty) {\n            // For jigsaw puzzle, we can progress through regions as difficulty levels\n            const regionProgression = {\n                'easy': 'North Africa',\n                'medium': 'East Africa',\n                'hard': 'West Africa'\n            };\n            const nextRegion = regionProgression[nextDifficulty];\n            if (nextRegion) {\n                setSelectedRegion(nextRegion);\n                startGame();\n            }\n        } else {\n            // Return to menu\n            if (completionData) {\n                onComplete(completionData.finalScore);\n            }\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete,\n                onAutoProgress: handleAutoProgress\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 565,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"F1WMg8rV4yTrAAU+RCIaeq7Sh7U=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(app-pages-browser)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagImage = (param)=>{\n    let { countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick } = param;\n    _s();\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = \"\".concat(flagImageUrl, \"?cache=1\");\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    object-cover \\n    rounded-sm \\n    border \\n    border-gray-300 \\n    shadow-sm\\n    \").concat(onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : '', \"\\n    \").concat(className, \"\\n  \");\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-200 animate-pulse flex items-center justify-center\"),\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          \".concat(sizeClasses[size], \" \\n          flex \\n          items-center \\n          justify-center \\n          bg-gray-100 \\n          rounded-sm \\n          border \\n          border-gray-300\\n          \").concat(onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : '', \"\\n          \").concat(className, \"\\n        \"),\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(baseClasses, \" bg-gray-100 flex items-center justify-center\"),\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagImage, \"mgIVjR2Tvb/QtAQzLx0hN0fqtHE=\");\n_c = FlagImage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\nvar _c;\n$RefreshReg$(_c, \"FlagImage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FlagImage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/VictoryAnimation.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VictoryAnimation = (param)=>{\n    let { isVisible, completionData, onComplete, onAutoProgress } = param;\n    _s();\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationPhase, setAnimationPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('enter');\n    const [progressCountdown, setProgressCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProgressing, setIsProgressing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__.celebrationConfigs[completionData.celebrationType];\n    const progressDelay = completionData.autoProgression.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VictoryAnimation.useEffect\": ()=>{\n            if (!isVisible) return;\n            setShowConfetti(true);\n            setAnimationPhase('enter');\n            setIsProgressing(false);\n            // Start countdown for auto-progression\n            const countdownStart = Math.ceil(progressDelay / 1000);\n            setProgressCountdown(countdownStart);\n            const timeline = [\n                // Enter phase\n                {\n                    delay: 0,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('enter')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Display phase\n                {\n                    delay: 300,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('display')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Start countdown\n                {\n                    delay: 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            const countdownInterval = setInterval({\n                                \"VictoryAnimation.useEffect.countdownInterval\": ()=>{\n                                    setProgressCountdown({\n                                        \"VictoryAnimation.useEffect.countdownInterval\": (prev)=>{\n                                            if (prev <= 1) {\n                                                clearInterval(countdownInterval);\n                                                return 0;\n                                            }\n                                            return prev - 1;\n                                        }\n                                    }[\"VictoryAnimation.useEffect.countdownInterval\"]);\n                                }\n                            }[\"VictoryAnimation.useEffect.countdownInterval\"], 1000);\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Exit phase\n                {\n                    delay: progressDelay - 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('exit')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Auto-progress\n                {\n                    delay: progressDelay,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            setShowConfetti(false);\n                            setIsProgressing(true);\n                            if (onAutoProgress) {\n                                onAutoProgress(completionData.autoProgression.action, completionData.autoProgression.nextDifficulty);\n                            } else {\n                                onComplete();\n                            }\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                }\n            ];\n            const timeouts = timeline.map({\n                \"VictoryAnimation.useEffect.timeouts\": (param)=>{\n                    let { delay, action } = param;\n                    return setTimeout(action, delay);\n                }\n            }[\"VictoryAnimation.useEffect.timeouts\"]);\n            return ({\n                \"VictoryAnimation.useEffect\": ()=>{\n                    timeouts.forEach(clearTimeout);\n                }\n            })[\"VictoryAnimation.useEffect\"];\n        }\n    }[\"VictoryAnimation.useEffect\"], [\n        isVisible,\n        progressDelay,\n        onComplete,\n        onAutoProgress,\n        completionData.autoProgression\n    ]);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: config.particles\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute animate-bounce\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                            animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full\",\n                            style: {\n                                backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],\n                                transform: \"rotate(\".concat(Math.random() * 360, \"deg)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          bg-gradient-to-br from-yellow-400 to-orange-500 \\n          rounded-2xl p-8 text-center max-w-md mx-4 \\n          transform transition-all duration-500 ease-out\\n          \".concat(animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : '', \"\\n          \").concat(animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : '', \"\\n          \").concat(animationPhase === 'exit' ? 'scale-110 opacity-90' : '', \"\\n        \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-8xl mb-4 animate-pulse\",\n                        children: config.emoji\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: completionData.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completionData.finalScore\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Final Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                \"+\",\n                                                completionData.timeBonus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Time Bonus\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-blue-900 font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: [\n                                        \"+\",\n                                        completionData.xpGained\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    children: \"XP Gained!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    completionData.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-900 font-semibold text-sm\",\n                                children: \"Achievements Unlocked:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            completionData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm\",\n                                    style: {\n                                        animationDelay: \"\".concat(index * 200, \"ms\")\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 \",\n                                        achievement\n                                    ]\n                                }, achievement, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-300 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out\",\n                                    style: {\n                                        width: animationPhase === 'display' ? \"\".concat(100 - progressCountdown / Math.ceil(progressDelay / 1000) * 100, \"%\") : '0%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-700 text-xs mt-2\",\n                                children: isProgressing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 font-semibold\",\n                                    children: \"Progressing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined) : progressCountdown > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: completionData.autoProgression.action === 'next-difficulty' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Starting \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold capitalize\",\n                                                children: completionData.autoProgression.nextDifficulty\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 30\n                                            }, undefined),\n                                            \" difficulty in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Returning to menu in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-semibold\",\n                                    children: \"Ready!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-yellow-300 text-2xl animate-ping\",\n                            style: {\n                                left: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                top: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                                animationDuration: \"\".concat(2 + Math.random() * 2, \"s\")\n                            },\n                            children: \"⭐\"\n                        }, \"star-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from({\n                        length: 12\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-white text-lg animate-bounce\",\n                            style: {\n                                left: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                top: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                            },\n                            children: \"✨\"\n                        }, \"sparkle-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            animationPhase === 'display' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-white animate-bounce\",\n                    children: [\n                        completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊',\n                        completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟',\n                        completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏',\n                        completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VictoryAnimation, \"Zn45cHR9HVHvnYlvCAY/3AjTlSY=\");\n_c = VictoryAnimation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VictoryAnimation);\nvar _c;\n$RefreshReg$(_c, \"VictoryAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = function(countryId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'svg';\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : \"Flag of \".concat(countryId);\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/flagUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/gameCompletion.ts":
/*!*************************************!*\
  !*** ./src/utils/gameCompletion.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   calculateXP: () => (/* binding */ calculateXP),\n/* harmony export */   celebrationConfigs: () => (/* binding */ celebrationConfigs),\n/* harmony export */   checkGameCompletion: () => (/* binding */ checkGameCompletion),\n/* harmony export */   getCompletionMessage: () => (/* binding */ getCompletionMessage),\n/* harmony export */   getNextAction: () => (/* binding */ getNextAction),\n/* harmony export */   processGameCompletion: () => (/* binding */ processGameCompletion),\n/* harmony export */   processGameCompletionWithProgression: () => (/* binding */ processGameCompletionWithProgression)\n/* harmony export */ });\n// Game completion utilities and animations\n// Calculate time bonus based on remaining time\nconst calculateTimeBonus = (timeRemaining, totalTime, difficulty)=>{\n    if (timeRemaining <= 0) return 0;\n    const timePercentage = timeRemaining / totalTime;\n    const difficultyMultiplier = {\n        easy: 1,\n        medium: 1.5,\n        hard: 2\n    }[difficulty];\n    // Base time bonus: up to 50 points for completing with full time remaining\n    const baseBonus = Math.floor(timePercentage * 50);\n    return Math.floor(baseBonus * difficultyMultiplier);\n};\n// Calculate XP based on performance\nconst calculateXP = (data)=>{\n    const baseXP = 20; // Base XP for completion\n    const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const difficultyBonus = {\n        easy: 0,\n        medium: 10,\n        hard: 20\n    }[data.difficulty];\n    const perfectBonus = data.perfectScore ? 30 : 0;\n    return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;\n};\n// Determine completion message and type\nconst getCompletionMessage = (data)=>{\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore && timePercentage > 0.7) {\n        return {\n            message: 'Perfect! Outstanding performance!',\n            type: 'perfect'\n        };\n    } else if (data.perfectScore && timePercentage > 0.4) {\n        return {\n            message: 'Excellent! Great job!',\n            type: 'excellent'\n        };\n    } else if (data.perfectScore) {\n        return {\n            message: 'Perfect Score! Well done!',\n            type: 'excellent'\n        };\n    } else if (timePercentage > 0.5) {\n        return {\n            message: 'Great work! Fast completion!',\n            type: 'good'\n        };\n    } else {\n        return {\n            message: 'Game Complete! Nice job!',\n            type: 'complete'\n        };\n    }\n};\n// Process game completion\nconst processGameCompletion = (data)=>{\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const finalScore = data.score + timeBonus;\n    const xpGained = calculateXP(data);\n    const { message, type } = getCompletionMessage(data);\n    // Determine achievements based on performance\n    const achievements = [];\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore) {\n        achievements.push('Perfect Score');\n    }\n    if (timePercentage > 0.8) {\n        achievements.push('Speed Demon');\n    }\n    if (data.difficulty === 'hard' && data.perfectScore) {\n        achievements.push('Master Player');\n    }\n    if (timeBonus > 30) {\n        achievements.push('Time Master');\n    }\n    return {\n        finalScore,\n        timeBonus,\n        xpGained,\n        achievements,\n        message,\n        celebrationType: type\n    };\n};\n// Game-specific completion checkers\nconst checkGameCompletion = {\n    'flag-matching': (matches, totalPairs)=>{\n        return matches >= totalPairs;\n    },\n    'country-name-scramble': (currentRound, totalRounds, roundComplete)=>{\n        return currentRound >= totalRounds - 1 && roundComplete;\n    },\n    'jigsaw-puzzle': (placedPieces, totalPieces)=>{\n        return placedPieces >= totalPieces;\n    },\n    'quiz': (answeredQuestions, totalQuestions)=>{\n        return answeredQuestions >= totalQuestions;\n    },\n    'memory-grid': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'matching': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'country-explorer': (visitedCountries, targetCountries)=>{\n        return visitedCountries >= targetCountries;\n    },\n    'speed-challenge': (answeredQuestions, targetQuestions)=>{\n        return answeredQuestions >= targetQuestions;\n    },\n    'mystery-land': (correctGuesses, totalRounds)=>{\n        return correctGuesses >= totalRounds;\n    },\n    'timeline-builder': (placedEvents, totalEvents)=>{\n        return placedEvents >= totalEvents;\n    },\n    'where-in-africa': (completedRounds, totalRounds)=>{\n        return completedRounds >= totalRounds;\n    },\n    'dress-character': (completedOutfits, targetOutfits)=>{\n        return completedOutfits >= targetOutfits;\n    }\n};\n// Celebration animation configurations\nconst celebrationConfigs = {\n    perfect: {\n        duration: 3000,\n        emoji: '🏆',\n        colors: [\n            '#FFD700',\n            '#FFA500',\n            '#FF6B6B'\n        ],\n        particles: 50\n    },\n    excellent: {\n        duration: 2500,\n        emoji: '🎉',\n        colors: [\n            '#4ECDC4',\n            '#45B7D1',\n            '#96CEB4'\n        ],\n        particles: 40\n    },\n    good: {\n        duration: 2000,\n        emoji: '👏',\n        colors: [\n            '#FECA57',\n            '#48CAE4',\n            '#A8E6CF'\n        ],\n        particles: 30\n    },\n    complete: {\n        duration: 1500,\n        emoji: '✅',\n        colors: [\n            '#6C5CE7',\n            '#A29BFE',\n            '#74B9FF'\n        ],\n        particles: 20\n    }\n};\n// Auto-progression logic\nconst getNextAction = (gameType, difficulty)=>{\n    // If completed on easy, progress to medium\n    if (difficulty === 'easy') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'medium'\n        };\n    }\n    // If completed on medium, progress to hard\n    if (difficulty === 'medium') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'hard'\n        };\n    }\n    // If completed on hard, return to menu\n    return {\n        action: 'menu'\n    };\n};\n// Process game completion with auto-progression\nconst processGameCompletionWithProgression = (data)=>{\n    const baseResult = processGameCompletion(data);\n    const progression = getNextAction(data.gameType, data.difficulty);\n    // Determine delay based on celebration type\n    const delay = {\n        perfect: 3000,\n        excellent: 2500,\n        good: 2000,\n        complete: 1500\n    }[baseResult.celebrationType];\n    return {\n        ...baseResult,\n        autoProgression: {\n            ...progression,\n            delay\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9nYW1lQ29tcGxldGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLDJDQUEyQztBQXNCM0MsK0NBQStDO0FBQ3hDLE1BQU1BLHFCQUFxQixDQUFDQyxlQUF1QkMsV0FBbUJDO0lBQzNFLElBQUlGLGlCQUFpQixHQUFHLE9BQU87SUFFL0IsTUFBTUcsaUJBQWlCSCxnQkFBZ0JDO0lBQ3ZDLE1BQU1HLHVCQUF1QjtRQUMzQkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLE1BQU07SUFDUixDQUFDLENBQUNMLFdBQVc7SUFFYiwyRUFBMkU7SUFDM0UsTUFBTU0sWUFBWUMsS0FBS0MsS0FBSyxDQUFDUCxpQkFBaUI7SUFDOUMsT0FBT00sS0FBS0MsS0FBSyxDQUFDRixZQUFZSjtBQUNoQyxFQUFFO0FBRUYsb0NBQW9DO0FBQzdCLE1BQU1PLGNBQWMsQ0FBQ0M7SUFDMUIsTUFBTUMsU0FBUyxJQUFJLHlCQUF5QjtJQUM1QyxNQUFNQyxrQkFBa0JMLEtBQUtDLEtBQUssQ0FBQ0UsS0FBS0csS0FBSyxHQUFHLEtBQUsscUJBQXFCO0lBQzFFLE1BQU1DLFlBQVlqQixtQkFBbUJhLEtBQUtaLGFBQWEsRUFBRVksS0FBS1gsU0FBUyxFQUFFVyxLQUFLVixVQUFVO0lBQ3hGLE1BQU1lLGtCQUFrQjtRQUN0QlosTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLE1BQU07SUFDUixDQUFDLENBQUNLLEtBQUtWLFVBQVUsQ0FBQztJQUVsQixNQUFNZ0IsZUFBZU4sS0FBS08sWUFBWSxHQUFHLEtBQUs7SUFFOUMsT0FBT04sU0FBU0Msa0JBQWtCTCxLQUFLQyxLQUFLLENBQUNNLFlBQVksS0FBS0Msa0JBQWtCQztBQUNsRixFQUFFO0FBRUYsd0NBQXdDO0FBQ2pDLE1BQU1FLHVCQUF1QixDQUFDUjtJQUNuQyxNQUFNVCxpQkFBaUJTLEtBQUtaLGFBQWEsR0FBR1ksS0FBS1gsU0FBUztJQUUxRCxJQUFJVyxLQUFLTyxZQUFZLElBQUloQixpQkFBaUIsS0FBSztRQUM3QyxPQUFPO1lBQUVrQixTQUFTO1lBQXFDQyxNQUFNO1FBQVU7SUFDekUsT0FBTyxJQUFJVixLQUFLTyxZQUFZLElBQUloQixpQkFBaUIsS0FBSztRQUNwRCxPQUFPO1lBQUVrQixTQUFTO1lBQXlCQyxNQUFNO1FBQVk7SUFDL0QsT0FBTyxJQUFJVixLQUFLTyxZQUFZLEVBQUU7UUFDNUIsT0FBTztZQUFFRSxTQUFTO1lBQTZCQyxNQUFNO1FBQVk7SUFDbkUsT0FBTyxJQUFJbkIsaUJBQWlCLEtBQUs7UUFDL0IsT0FBTztZQUFFa0IsU0FBUztZQUFnQ0MsTUFBTTtRQUFPO0lBQ2pFLE9BQU87UUFDTCxPQUFPO1lBQUVELFNBQVM7WUFBNEJDLE1BQU07UUFBVztJQUNqRTtBQUNGLEVBQUU7QUFFRiwwQkFBMEI7QUFDbkIsTUFBTUMsd0JBQXdCLENBQUNYO0lBQ3BDLE1BQU1JLFlBQVlqQixtQkFBbUJhLEtBQUtaLGFBQWEsRUFBRVksS0FBS1gsU0FBUyxFQUFFVyxLQUFLVixVQUFVO0lBQ3hGLE1BQU1zQixhQUFhWixLQUFLRyxLQUFLLEdBQUdDO0lBQ2hDLE1BQU1TLFdBQVdkLFlBQVlDO0lBQzdCLE1BQU0sRUFBRVMsT0FBTyxFQUFFQyxJQUFJLEVBQUUsR0FBR0YscUJBQXFCUjtJQUUvQyw4Q0FBOEM7SUFDOUMsTUFBTWMsZUFBeUIsRUFBRTtJQUNqQyxNQUFNdkIsaUJBQWlCUyxLQUFLWixhQUFhLEdBQUdZLEtBQUtYLFNBQVM7SUFFMUQsSUFBSVcsS0FBS08sWUFBWSxFQUFFO1FBQ3JCTyxhQUFhQyxJQUFJLENBQUM7SUFDcEI7SUFFQSxJQUFJeEIsaUJBQWlCLEtBQUs7UUFDeEJ1QixhQUFhQyxJQUFJLENBQUM7SUFDcEI7SUFFQSxJQUFJZixLQUFLVixVQUFVLEtBQUssVUFBVVUsS0FBS08sWUFBWSxFQUFFO1FBQ25ETyxhQUFhQyxJQUFJLENBQUM7SUFDcEI7SUFFQSxJQUFJWCxZQUFZLElBQUk7UUFDbEJVLGFBQWFDLElBQUksQ0FBQztJQUNwQjtJQUVBLE9BQU87UUFDTEg7UUFDQVI7UUFDQVM7UUFDQUM7UUFDQUw7UUFDQU8saUJBQWlCTjtJQUNuQjtBQUNGLEVBQUU7QUFFRixvQ0FBb0M7QUFDN0IsTUFBTU8sc0JBQXNCO0lBQ2pDLGlCQUFpQixDQUFDQyxTQUFpQkM7UUFDakMsT0FBT0QsV0FBV0M7SUFDcEI7SUFFQSx5QkFBeUIsQ0FBQ0MsY0FBc0JDLGFBQXFCQztRQUNuRSxPQUFPRixnQkFBZ0JDLGNBQWMsS0FBS0M7SUFDNUM7SUFFQSxpQkFBaUIsQ0FBQ0MsY0FBc0JDO1FBQ3RDLE9BQU9ELGdCQUFnQkM7SUFDekI7SUFFQSxRQUFRLENBQUNDLG1CQUEyQkM7UUFDbEMsT0FBT0QscUJBQXFCQztJQUM5QjtJQUVBLGVBQWUsQ0FBQ0MsY0FBc0JSO1FBQ3BDLE9BQU9RLGdCQUFnQlI7SUFDekI7SUFFQSxZQUFZLENBQUNRLGNBQXNCUjtRQUNqQyxPQUFPUSxnQkFBZ0JSO0lBQ3pCO0lBRUEsb0JBQW9CLENBQUNTLGtCQUEwQkM7UUFDN0MsT0FBT0Qsb0JBQW9CQztJQUM3QjtJQUVBLG1CQUFtQixDQUFDSixtQkFBMkJLO1FBQzdDLE9BQU9MLHFCQUFxQks7SUFDOUI7SUFFQSxnQkFBZ0IsQ0FBQ0MsZ0JBQXdCVjtRQUN2QyxPQUFPVSxrQkFBa0JWO0lBQzNCO0lBRUEsb0JBQW9CLENBQUNXLGNBQXNCQztRQUN6QyxPQUFPRCxnQkFBZ0JDO0lBQ3pCO0lBRUEsbUJBQW1CLENBQUNDLGlCQUF5QmI7UUFDM0MsT0FBT2EsbUJBQW1CYjtJQUM1QjtJQUVBLG1CQUFtQixDQUFDYyxrQkFBMEJDO1FBQzVDLE9BQU9ELG9CQUFvQkM7SUFDN0I7QUFDRixFQUFFO0FBRUYsdUNBQXVDO0FBQ2hDLE1BQU1DLHFCQUFxQjtJQUNoQ0MsU0FBUztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUFDO1lBQVc7WUFBVztTQUFVO1FBQ3pDQyxXQUFXO0lBQ2I7SUFDQUMsV0FBVztRQUNUSixVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUFDO1lBQVc7WUFBVztTQUFVO1FBQ3pDQyxXQUFXO0lBQ2I7SUFDQUUsTUFBTTtRQUNKTCxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUFDO1lBQVc7WUFBVztTQUFVO1FBQ3pDQyxXQUFXO0lBQ2I7SUFDQUcsVUFBVTtRQUNSTixVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUFDO1lBQVc7WUFBVztTQUFVO1FBQ3pDQyxXQUFXO0lBQ2I7QUFDRixFQUFFO0FBRUYseUJBQXlCO0FBQ2xCLE1BQU1JLGdCQUFnQixDQUFDQyxVQUFvQnpEO0lBQ2hELDJDQUEyQztJQUMzQyxJQUFJQSxlQUFlLFFBQVE7UUFDekIsT0FBTztZQUFFMEQsUUFBUTtZQUFtQkMsZ0JBQWdCO1FBQVM7SUFDL0Q7SUFFQSwyQ0FBMkM7SUFDM0MsSUFBSTNELGVBQWUsVUFBVTtRQUMzQixPQUFPO1lBQUUwRCxRQUFRO1lBQW1CQyxnQkFBZ0I7UUFBTztJQUM3RDtJQUVBLHVDQUF1QztJQUN2QyxPQUFPO1FBQUVELFFBQVE7SUFBTztBQUMxQixFQUFFO0FBV0YsZ0RBQWdEO0FBQ3pDLE1BQU1FLHVDQUF1QyxDQUFDbEQ7SUFDbkQsTUFBTW1ELGFBQWF4QyxzQkFBc0JYO0lBQ3pDLE1BQU1vRCxjQUFjTixjQUFjOUMsS0FBSytDLFFBQVEsRUFBRS9DLEtBQUtWLFVBQVU7SUFFaEUsNENBQTRDO0lBQzVDLE1BQU0rRCxRQUFRO1FBQ1pmLFNBQVM7UUFDVEssV0FBVztRQUNYQyxNQUFNO1FBQ05DLFVBQVU7SUFDWixDQUFDLENBQUNNLFdBQVduQyxlQUFlLENBQUM7SUFFN0IsT0FBTztRQUNMLEdBQUdtQyxVQUFVO1FBQ2JHLGlCQUFpQjtZQUNmLEdBQUdGLFdBQVc7WUFDZEM7UUFDRjtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNRUVLIEVERU5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcR0FNRVMgRk9SIEFSSUNBXFxnYW1lcy1mb3ItYWZyaWNhXFxzcmNcXHV0aWxzXFxnYW1lQ29tcGxldGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHYW1lIGNvbXBsZXRpb24gdXRpbGl0aWVzIGFuZCBhbmltYXRpb25zXG5pbXBvcnQgeyBHYW1lVHlwZSB9IGZyb20gJ0AvdHlwZXMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEdhbWVDb21wbGV0aW9uRGF0YSB7XG4gIGdhbWVUeXBlOiBHYW1lVHlwZTtcbiAgc2NvcmU6IG51bWJlcjtcbiAgdGltZVJlbWFpbmluZzogbnVtYmVyO1xuICB0b3RhbFRpbWU6IG51bWJlcjtcbiAgcGVyZmVjdFNjb3JlOiBib29sZWFuO1xuICBkaWZmaWN1bHR5OiAnZWFzeScgfCAnbWVkaXVtJyB8ICdoYXJkJztcbiAgY29tcGxldGlvblRpbWU6IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb21wbGV0aW9uUmVzdWx0IHtcbiAgZmluYWxTY29yZTogbnVtYmVyO1xuICB0aW1lQm9udXM6IG51bWJlcjtcbiAgeHBHYWluZWQ6IG51bWJlcjtcbiAgYWNoaWV2ZW1lbnRzOiBzdHJpbmdbXTtcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBjZWxlYnJhdGlvblR5cGU6ICdwZXJmZWN0JyB8ICdleGNlbGxlbnQnIHwgJ2dvb2QnIHwgJ2NvbXBsZXRlJztcbn1cblxuLy8gQ2FsY3VsYXRlIHRpbWUgYm9udXMgYmFzZWQgb24gcmVtYWluaW5nIHRpbWVcbmV4cG9ydCBjb25zdCBjYWxjdWxhdGVUaW1lQm9udXMgPSAodGltZVJlbWFpbmluZzogbnVtYmVyLCB0b3RhbFRpbWU6IG51bWJlciwgZGlmZmljdWx0eTogJ2Vhc3knIHwgJ21lZGl1bScgfCAnaGFyZCcpOiBudW1iZXIgPT4ge1xuICBpZiAodGltZVJlbWFpbmluZyA8PSAwKSByZXR1cm4gMDtcbiAgXG4gIGNvbnN0IHRpbWVQZXJjZW50YWdlID0gdGltZVJlbWFpbmluZyAvIHRvdGFsVGltZTtcbiAgY29uc3QgZGlmZmljdWx0eU11bHRpcGxpZXIgPSB7XG4gICAgZWFzeTogMSxcbiAgICBtZWRpdW06IDEuNSxcbiAgICBoYXJkOiAyXG4gIH1bZGlmZmljdWx0eV07XG4gIFxuICAvLyBCYXNlIHRpbWUgYm9udXM6IHVwIHRvIDUwIHBvaW50cyBmb3IgY29tcGxldGluZyB3aXRoIGZ1bGwgdGltZSByZW1haW5pbmdcbiAgY29uc3QgYmFzZUJvbnVzID0gTWF0aC5mbG9vcih0aW1lUGVyY2VudGFnZSAqIDUwKTtcbiAgcmV0dXJuIE1hdGguZmxvb3IoYmFzZUJvbnVzICogZGlmZmljdWx0eU11bHRpcGxpZXIpO1xufTtcblxuLy8gQ2FsY3VsYXRlIFhQIGJhc2VkIG9uIHBlcmZvcm1hbmNlXG5leHBvcnQgY29uc3QgY2FsY3VsYXRlWFAgPSAoZGF0YTogR2FtZUNvbXBsZXRpb25EYXRhKTogbnVtYmVyID0+IHtcbiAgY29uc3QgYmFzZVhQID0gMjA7IC8vIEJhc2UgWFAgZm9yIGNvbXBsZXRpb25cbiAgY29uc3Qgc2NvcmVNdWx0aXBsaWVyID0gTWF0aC5mbG9vcihkYXRhLnNjb3JlIC8gMTApOyAvLyAxIFhQIHBlciAxMCBwb2ludHNcbiAgY29uc3QgdGltZUJvbnVzID0gY2FsY3VsYXRlVGltZUJvbnVzKGRhdGEudGltZVJlbWFpbmluZywgZGF0YS50b3RhbFRpbWUsIGRhdGEuZGlmZmljdWx0eSk7XG4gIGNvbnN0IGRpZmZpY3VsdHlCb251cyA9IHtcbiAgICBlYXN5OiAwLFxuICAgIG1lZGl1bTogMTAsXG4gICAgaGFyZDogMjBcbiAgfVtkYXRhLmRpZmZpY3VsdHldO1xuICBcbiAgY29uc3QgcGVyZmVjdEJvbnVzID0gZGF0YS5wZXJmZWN0U2NvcmUgPyAzMCA6IDA7XG4gIFxuICByZXR1cm4gYmFzZVhQICsgc2NvcmVNdWx0aXBsaWVyICsgTWF0aC5mbG9vcih0aW1lQm9udXMgLyAyKSArIGRpZmZpY3VsdHlCb251cyArIHBlcmZlY3RCb251cztcbn07XG5cbi8vIERldGVybWluZSBjb21wbGV0aW9uIG1lc3NhZ2UgYW5kIHR5cGVcbmV4cG9ydCBjb25zdCBnZXRDb21wbGV0aW9uTWVzc2FnZSA9IChkYXRhOiBHYW1lQ29tcGxldGlvbkRhdGEpOiB7IG1lc3NhZ2U6IHN0cmluZzsgdHlwZTogJ3BlcmZlY3QnIHwgJ2V4Y2VsbGVudCcgfCAnZ29vZCcgfCAnY29tcGxldGUnIH0gPT4ge1xuICBjb25zdCB0aW1lUGVyY2VudGFnZSA9IGRhdGEudGltZVJlbWFpbmluZyAvIGRhdGEudG90YWxUaW1lO1xuICBcbiAgaWYgKGRhdGEucGVyZmVjdFNjb3JlICYmIHRpbWVQZXJjZW50YWdlID4gMC43KSB7XG4gICAgcmV0dXJuIHsgbWVzc2FnZTogJ1BlcmZlY3QhIE91dHN0YW5kaW5nIHBlcmZvcm1hbmNlIScsIHR5cGU6ICdwZXJmZWN0JyB9O1xuICB9IGVsc2UgaWYgKGRhdGEucGVyZmVjdFNjb3JlICYmIHRpbWVQZXJjZW50YWdlID4gMC40KSB7XG4gICAgcmV0dXJuIHsgbWVzc2FnZTogJ0V4Y2VsbGVudCEgR3JlYXQgam9iIScsIHR5cGU6ICdleGNlbGxlbnQnIH07XG4gIH0gZWxzZSBpZiAoZGF0YS5wZXJmZWN0U2NvcmUpIHtcbiAgICByZXR1cm4geyBtZXNzYWdlOiAnUGVyZmVjdCBTY29yZSEgV2VsbCBkb25lIScsIHR5cGU6ICdleGNlbGxlbnQnIH07XG4gIH0gZWxzZSBpZiAodGltZVBlcmNlbnRhZ2UgPiAwLjUpIHtcbiAgICByZXR1cm4geyBtZXNzYWdlOiAnR3JlYXQgd29yayEgRmFzdCBjb21wbGV0aW9uIScsIHR5cGU6ICdnb29kJyB9O1xuICB9IGVsc2Uge1xuICAgIHJldHVybiB7IG1lc3NhZ2U6ICdHYW1lIENvbXBsZXRlISBOaWNlIGpvYiEnLCB0eXBlOiAnY29tcGxldGUnIH07XG4gIH1cbn07XG5cbi8vIFByb2Nlc3MgZ2FtZSBjb21wbGV0aW9uXG5leHBvcnQgY29uc3QgcHJvY2Vzc0dhbWVDb21wbGV0aW9uID0gKGRhdGE6IEdhbWVDb21wbGV0aW9uRGF0YSk6IENvbXBsZXRpb25SZXN1bHQgPT4ge1xuICBjb25zdCB0aW1lQm9udXMgPSBjYWxjdWxhdGVUaW1lQm9udXMoZGF0YS50aW1lUmVtYWluaW5nLCBkYXRhLnRvdGFsVGltZSwgZGF0YS5kaWZmaWN1bHR5KTtcbiAgY29uc3QgZmluYWxTY29yZSA9IGRhdGEuc2NvcmUgKyB0aW1lQm9udXM7XG4gIGNvbnN0IHhwR2FpbmVkID0gY2FsY3VsYXRlWFAoZGF0YSk7XG4gIGNvbnN0IHsgbWVzc2FnZSwgdHlwZSB9ID0gZ2V0Q29tcGxldGlvbk1lc3NhZ2UoZGF0YSk7XG4gIFxuICAvLyBEZXRlcm1pbmUgYWNoaWV2ZW1lbnRzIGJhc2VkIG9uIHBlcmZvcm1hbmNlXG4gIGNvbnN0IGFjaGlldmVtZW50czogc3RyaW5nW10gPSBbXTtcbiAgY29uc3QgdGltZVBlcmNlbnRhZ2UgPSBkYXRhLnRpbWVSZW1haW5pbmcgLyBkYXRhLnRvdGFsVGltZTtcbiAgXG4gIGlmIChkYXRhLnBlcmZlY3RTY29yZSkge1xuICAgIGFjaGlldmVtZW50cy5wdXNoKCdQZXJmZWN0IFNjb3JlJyk7XG4gIH1cbiAgXG4gIGlmICh0aW1lUGVyY2VudGFnZSA+IDAuOCkge1xuICAgIGFjaGlldmVtZW50cy5wdXNoKCdTcGVlZCBEZW1vbicpO1xuICB9XG4gIFxuICBpZiAoZGF0YS5kaWZmaWN1bHR5ID09PSAnaGFyZCcgJiYgZGF0YS5wZXJmZWN0U2NvcmUpIHtcbiAgICBhY2hpZXZlbWVudHMucHVzaCgnTWFzdGVyIFBsYXllcicpO1xuICB9XG4gIFxuICBpZiAodGltZUJvbnVzID4gMzApIHtcbiAgICBhY2hpZXZlbWVudHMucHVzaCgnVGltZSBNYXN0ZXInKTtcbiAgfVxuICBcbiAgcmV0dXJuIHtcbiAgICBmaW5hbFNjb3JlLFxuICAgIHRpbWVCb251cyxcbiAgICB4cEdhaW5lZCxcbiAgICBhY2hpZXZlbWVudHMsXG4gICAgbWVzc2FnZSxcbiAgICBjZWxlYnJhdGlvblR5cGU6IHR5cGVcbiAgfTtcbn07XG5cbi8vIEdhbWUtc3BlY2lmaWMgY29tcGxldGlvbiBjaGVja2Vyc1xuZXhwb3J0IGNvbnN0IGNoZWNrR2FtZUNvbXBsZXRpb24gPSB7XG4gICdmbGFnLW1hdGNoaW5nJzogKG1hdGNoZXM6IG51bWJlciwgdG90YWxQYWlyczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIG1hdGNoZXMgPj0gdG90YWxQYWlycztcbiAgfSxcbiAgXG4gICdjb3VudHJ5LW5hbWUtc2NyYW1ibGUnOiAoY3VycmVudFJvdW5kOiBudW1iZXIsIHRvdGFsUm91bmRzOiBudW1iZXIsIHJvdW5kQ29tcGxldGU6IGJvb2xlYW4pOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gY3VycmVudFJvdW5kID49IHRvdGFsUm91bmRzIC0gMSAmJiByb3VuZENvbXBsZXRlO1xuICB9LFxuICBcbiAgJ2ppZ3Nhdy1wdXp6bGUnOiAocGxhY2VkUGllY2VzOiBudW1iZXIsIHRvdGFsUGllY2VzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gcGxhY2VkUGllY2VzID49IHRvdGFsUGllY2VzO1xuICB9LFxuICBcbiAgJ3F1aXonOiAoYW5zd2VyZWRRdWVzdGlvbnM6IG51bWJlciwgdG90YWxRdWVzdGlvbnM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBhbnN3ZXJlZFF1ZXN0aW9ucyA+PSB0b3RhbFF1ZXN0aW9ucztcbiAgfSxcbiAgXG4gICdtZW1vcnktZ3JpZCc6IChtYXRjaGVkUGFpcnM6IG51bWJlciwgdG90YWxQYWlyczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIG1hdGNoZWRQYWlycyA+PSB0b3RhbFBhaXJzO1xuICB9LFxuICBcbiAgJ21hdGNoaW5nJzogKG1hdGNoZWRQYWlyczogbnVtYmVyLCB0b3RhbFBhaXJzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gbWF0Y2hlZFBhaXJzID49IHRvdGFsUGFpcnM7XG4gIH0sXG4gIFxuICAnY291bnRyeS1leHBsb3Jlcic6ICh2aXNpdGVkQ291bnRyaWVzOiBudW1iZXIsIHRhcmdldENvdW50cmllczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIHZpc2l0ZWRDb3VudHJpZXMgPj0gdGFyZ2V0Q291bnRyaWVzO1xuICB9LFxuICBcbiAgJ3NwZWVkLWNoYWxsZW5nZSc6IChhbnN3ZXJlZFF1ZXN0aW9uczogbnVtYmVyLCB0YXJnZXRRdWVzdGlvbnM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBhbnN3ZXJlZFF1ZXN0aW9ucyA+PSB0YXJnZXRRdWVzdGlvbnM7XG4gIH0sXG4gIFxuICAnbXlzdGVyeS1sYW5kJzogKGNvcnJlY3RHdWVzc2VzOiBudW1iZXIsIHRvdGFsUm91bmRzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gY29ycmVjdEd1ZXNzZXMgPj0gdG90YWxSb3VuZHM7XG4gIH0sXG4gIFxuICAndGltZWxpbmUtYnVpbGRlcic6IChwbGFjZWRFdmVudHM6IG51bWJlciwgdG90YWxFdmVudHM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBwbGFjZWRFdmVudHMgPj0gdG90YWxFdmVudHM7XG4gIH0sXG4gIFxuICAnd2hlcmUtaW4tYWZyaWNhJzogKGNvbXBsZXRlZFJvdW5kczogbnVtYmVyLCB0b3RhbFJvdW5kczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGNvbXBsZXRlZFJvdW5kcyA+PSB0b3RhbFJvdW5kcztcbiAgfSxcbiAgXG4gICdkcmVzcy1jaGFyYWN0ZXInOiAoY29tcGxldGVkT3V0Zml0czogbnVtYmVyLCB0YXJnZXRPdXRmaXRzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gY29tcGxldGVkT3V0Zml0cyA+PSB0YXJnZXRPdXRmaXRzO1xuICB9XG59O1xuXG4vLyBDZWxlYnJhdGlvbiBhbmltYXRpb24gY29uZmlndXJhdGlvbnNcbmV4cG9ydCBjb25zdCBjZWxlYnJhdGlvbkNvbmZpZ3MgPSB7XG4gIHBlcmZlY3Q6IHtcbiAgICBkdXJhdGlvbjogMzAwMCxcbiAgICBlbW9qaTogJ/Cfj4YnLFxuICAgIGNvbG9yczogWycjRkZENzAwJywgJyNGRkE1MDAnLCAnI0ZGNkI2QiddLFxuICAgIHBhcnRpY2xlczogNTBcbiAgfSxcbiAgZXhjZWxsZW50OiB7XG4gICAgZHVyYXRpb246IDI1MDAsXG4gICAgZW1vamk6ICfwn46JJyxcbiAgICBjb2xvcnM6IFsnIzRFQ0RDNCcsICcjNDVCN0QxJywgJyM5NkNFQjQnXSxcbiAgICBwYXJ0aWNsZXM6IDQwXG4gIH0sXG4gIGdvb2Q6IHtcbiAgICBkdXJhdGlvbjogMjAwMCxcbiAgICBlbW9qaTogJ/CfkY8nLFxuICAgIGNvbG9yczogWycjRkVDQTU3JywgJyM0OENBRTQnLCAnI0E4RTZDRiddLFxuICAgIHBhcnRpY2xlczogMzBcbiAgfSxcbiAgY29tcGxldGU6IHtcbiAgICBkdXJhdGlvbjogMTUwMCxcbiAgICBlbW9qaTogJ+KchScsXG4gICAgY29sb3JzOiBbJyM2QzVDRTcnLCAnI0EyOUJGRScsICcjNzRCOUZGJ10sXG4gICAgcGFydGljbGVzOiAyMFxuICB9XG59O1xuXG4vLyBBdXRvLXByb2dyZXNzaW9uIGxvZ2ljXG5leHBvcnQgY29uc3QgZ2V0TmV4dEFjdGlvbiA9IChnYW1lVHlwZTogR2FtZVR5cGUsIGRpZmZpY3VsdHk6ICdlYXN5JyB8ICdtZWRpdW0nIHwgJ2hhcmQnKTogeyBhY3Rpb246ICduZXh0LWRpZmZpY3VsdHknIHwgJ21lbnUnIHwgJ3JlcGxheSc7IG5leHREaWZmaWN1bHR5PzogJ2Vhc3knIHwgJ21lZGl1bScgfCAnaGFyZCcgfSA9PiB7XG4gIC8vIElmIGNvbXBsZXRlZCBvbiBlYXN5LCBwcm9ncmVzcyB0byBtZWRpdW1cbiAgaWYgKGRpZmZpY3VsdHkgPT09ICdlYXN5Jykge1xuICAgIHJldHVybiB7IGFjdGlvbjogJ25leHQtZGlmZmljdWx0eScsIG5leHREaWZmaWN1bHR5OiAnbWVkaXVtJyB9O1xuICB9XG5cbiAgLy8gSWYgY29tcGxldGVkIG9uIG1lZGl1bSwgcHJvZ3Jlc3MgdG8gaGFyZFxuICBpZiAoZGlmZmljdWx0eSA9PT0gJ21lZGl1bScpIHtcbiAgICByZXR1cm4geyBhY3Rpb246ICduZXh0LWRpZmZpY3VsdHknLCBuZXh0RGlmZmljdWx0eTogJ2hhcmQnIH07XG4gIH1cblxuICAvLyBJZiBjb21wbGV0ZWQgb24gaGFyZCwgcmV0dXJuIHRvIG1lbnVcbiAgcmV0dXJuIHsgYWN0aW9uOiAnbWVudScgfTtcbn07XG5cbi8vIEVuaGFuY2VkIGNvbXBsZXRpb24gcmVzdWx0IHdpdGggYXV0by1wcm9ncmVzc2lvbiBkYXRhXG5leHBvcnQgaW50ZXJmYWNlIEVuaGFuY2VkQ29tcGxldGlvblJlc3VsdCBleHRlbmRzIENvbXBsZXRpb25SZXN1bHQge1xuICBhdXRvUHJvZ3Jlc3Npb246IHtcbiAgICBhY3Rpb246ICduZXh0LWRpZmZpY3VsdHknIHwgJ21lbnUnIHwgJ3JlcGxheSc7XG4gICAgbmV4dERpZmZpY3VsdHk/OiAnZWFzeScgfCAnbWVkaXVtJyB8ICdoYXJkJztcbiAgICBkZWxheTogbnVtYmVyOyAvLyBtaWxsaXNlY29uZHMgYmVmb3JlIGF1dG8tcHJvZ3Jlc3Npb25cbiAgfTtcbn1cblxuLy8gUHJvY2VzcyBnYW1lIGNvbXBsZXRpb24gd2l0aCBhdXRvLXByb2dyZXNzaW9uXG5leHBvcnQgY29uc3QgcHJvY2Vzc0dhbWVDb21wbGV0aW9uV2l0aFByb2dyZXNzaW9uID0gKGRhdGE6IEdhbWVDb21wbGV0aW9uRGF0YSk6IEVuaGFuY2VkQ29tcGxldGlvblJlc3VsdCA9PiB7XG4gIGNvbnN0IGJhc2VSZXN1bHQgPSBwcm9jZXNzR2FtZUNvbXBsZXRpb24oZGF0YSk7XG4gIGNvbnN0IHByb2dyZXNzaW9uID0gZ2V0TmV4dEFjdGlvbihkYXRhLmdhbWVUeXBlLCBkYXRhLmRpZmZpY3VsdHkpO1xuXG4gIC8vIERldGVybWluZSBkZWxheSBiYXNlZCBvbiBjZWxlYnJhdGlvbiB0eXBlXG4gIGNvbnN0IGRlbGF5ID0ge1xuICAgIHBlcmZlY3Q6IDMwMDAsXG4gICAgZXhjZWxsZW50OiAyNTAwLFxuICAgIGdvb2Q6IDIwMDAsXG4gICAgY29tcGxldGU6IDE1MDBcbiAgfVtiYXNlUmVzdWx0LmNlbGVicmF0aW9uVHlwZV07XG5cbiAgcmV0dXJuIHtcbiAgICAuLi5iYXNlUmVzdWx0LFxuICAgIGF1dG9Qcm9ncmVzc2lvbjoge1xuICAgICAgLi4ucHJvZ3Jlc3Npb24sXG4gICAgICBkZWxheVxuICAgIH1cbiAgfTtcbn07XG4iXSwibmFtZXMiOlsiY2FsY3VsYXRlVGltZUJvbnVzIiwidGltZVJlbWFpbmluZyIsInRvdGFsVGltZSIsImRpZmZpY3VsdHkiLCJ0aW1lUGVyY2VudGFnZSIsImRpZmZpY3VsdHlNdWx0aXBsaWVyIiwiZWFzeSIsIm1lZGl1bSIsImhhcmQiLCJiYXNlQm9udXMiLCJNYXRoIiwiZmxvb3IiLCJjYWxjdWxhdGVYUCIsImRhdGEiLCJiYXNlWFAiLCJzY29yZU11bHRpcGxpZXIiLCJzY29yZSIsInRpbWVCb251cyIsImRpZmZpY3VsdHlCb251cyIsInBlcmZlY3RCb251cyIsInBlcmZlY3RTY29yZSIsImdldENvbXBsZXRpb25NZXNzYWdlIiwibWVzc2FnZSIsInR5cGUiLCJwcm9jZXNzR2FtZUNvbXBsZXRpb24iLCJmaW5hbFNjb3JlIiwieHBHYWluZWQiLCJhY2hpZXZlbWVudHMiLCJwdXNoIiwiY2VsZWJyYXRpb25UeXBlIiwiY2hlY2tHYW1lQ29tcGxldGlvbiIsIm1hdGNoZXMiLCJ0b3RhbFBhaXJzIiwiY3VycmVudFJvdW5kIiwidG90YWxSb3VuZHMiLCJyb3VuZENvbXBsZXRlIiwicGxhY2VkUGllY2VzIiwidG90YWxQaWVjZXMiLCJhbnN3ZXJlZFF1ZXN0aW9ucyIsInRvdGFsUXVlc3Rpb25zIiwibWF0Y2hlZFBhaXJzIiwidmlzaXRlZENvdW50cmllcyIsInRhcmdldENvdW50cmllcyIsInRhcmdldFF1ZXN0aW9ucyIsImNvcnJlY3RHdWVzc2VzIiwicGxhY2VkRXZlbnRzIiwidG90YWxFdmVudHMiLCJjb21wbGV0ZWRSb3VuZHMiLCJjb21wbGV0ZWRPdXRmaXRzIiwidGFyZ2V0T3V0Zml0cyIsImNlbGVicmF0aW9uQ29uZmlncyIsInBlcmZlY3QiLCJkdXJhdGlvbiIsImVtb2ppIiwiY29sb3JzIiwicGFydGljbGVzIiwiZXhjZWxsZW50IiwiZ29vZCIsImNvbXBsZXRlIiwiZ2V0TmV4dEFjdGlvbiIsImdhbWVUeXBlIiwiYWN0aW9uIiwibmV4dERpZmZpY3VsdHkiLCJwcm9jZXNzR2FtZUNvbXBsZXRpb25XaXRoUHJvZ3Jlc3Npb24iLCJiYXNlUmVzdWx0IiwicHJvZ3Jlc3Npb24iLCJkZWxheSIsImF1dG9Qcm9ncmVzc2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/gameCompletion.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ })

}]);