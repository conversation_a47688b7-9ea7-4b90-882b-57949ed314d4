'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Menu, X, Trophy, User, Settings } from 'lucide-react';
import { useGameStore } from '@/hooks/useGameStore';
import Button from '@/components/ui/Button';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { userProgress, currentGame } = useGameStore();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const menuVariants = {
    closed: { opacity: 0, x: '100%' },
    open: { opacity: 1, x: 0 },
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-40 bg-primary-dark border-b border-gray-700 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <motion.div
            className="flex items-center space-x-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-2xl">🌍</div>
            <div>
              <h1 className="text-xl font-bold text-accent-gold font-display">
                Games for Africa
              </h1>
              <p className="text-xs text-text-secondary hidden sm:block">
                Learn about African countries and cultures
              </p>
            </div>
          </motion.div>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-6">
            <a 
              href="#games" 
              className="text-text-secondary hover:text-accent-gold transition-colors"
            >
              Games
            </a>
            <a 
              href="#leaderboard" 
              className="text-text-secondary hover:text-accent-gold transition-colors"
            >
              Leaderboard
            </a>
            <a 
              href="#about" 
              className="text-text-secondary hover:text-accent-gold transition-colors"
            >
              About
            </a>
          </nav>

          {/* User Progress & Actions */}
          <div className="flex items-center space-x-4">
            {/* Score Display */}
            <motion.div
              className="hidden sm:flex items-center space-x-2 bg-primary-light px-3 py-1 rounded-lg"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Trophy className="w-4 h-4 text-accent-gold" />
              <span className="text-sm text-text-primary font-medium">
                {userProgress.totalScore}
              </span>
            </motion.div>

            {/* Current Game Indicator */}
            {currentGame && (
              <motion.div
                className="hidden sm:flex items-center space-x-2 bg-accent-green bg-opacity-20 px-3 py-1 rounded-lg"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <div className="w-2 h-2 bg-accent-green rounded-full animate-pulse" />
                <span className="text-sm text-accent-green font-medium">
                  Playing {currentGame.replace('-', ' ')}
                </span>
              </motion.div>
            )}

            {/* Profile Button */}
            <Button
              variant="secondary"
              size="sm"
              className="hidden sm:flex"
              onClick={() => {/* TODO: Open profile modal */}}
            >
              <User className="w-4 h-4 mr-2" />
              Profile
            </Button>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 text-text-secondary hover:text-text-primary transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <motion.div
        className="md:hidden fixed top-16 right-0 bottom-0 w-64 bg-primary-light border-l border-gray-700 shadow-xl"
        variants={menuVariants}
        initial="closed"
        animate={isMenuOpen ? "open" : "closed"}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <div className="p-6 space-y-6">
          {/* User Stats */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Total Score</span>
              <span className="text-accent-gold font-medium">
                {userProgress.totalScore}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Games Played</span>
              <span className="text-text-primary font-medium">
                {userProgress.gamesPlayed}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Achievements</span>
              <span className="text-text-primary font-medium">
                {userProgress.achievements.filter(a => a.unlocked).length}
              </span>
            </div>
          </div>

          <hr className="border-gray-700" />

          {/* Navigation Links */}
          <nav className="space-y-4">
            <a 
              href="#games" 
              className="block text-text-primary hover:text-accent-gold transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Games
            </a>
            <a 
              href="#leaderboard" 
              className="block text-text-primary hover:text-accent-gold transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Leaderboard
            </a>
            <a 
              href="#about" 
              className="block text-text-primary hover:text-accent-gold transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              About
            </a>
          </nav>

          <hr className="border-gray-700" />

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              variant="primary"
              size="sm"
              className="w-full"
              onClick={() => {/* TODO: Open profile modal */}}
            >
              <User className="w-4 h-4 mr-2" />
              View Profile
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="w-full"
              onClick={() => {/* TODO: Open settings modal */}}
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <motion.div
          className="md:hidden fixed inset-0 top-16 bg-black bg-opacity-50 z-30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;
