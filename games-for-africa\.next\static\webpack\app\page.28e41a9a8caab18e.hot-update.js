"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 || currentRound && currentRound.matches === getDifficultySettings(difficulty).pairs) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        currentRound\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        countryId: flag.country.id,\n                                                        size: \"xl\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                countryId: lastMatchedCountry.id,\n                                size: \"large\",\n                                className: \"mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\\'s Up!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound.matches\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    settings.pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            Math.round(currentRound.matches / Math.max(currentRound.attempts, 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 32\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"ejPtyWkpaRtsv6OixdL5L0k3xTQ=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ })

});