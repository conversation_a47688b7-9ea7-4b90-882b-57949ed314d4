"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when pieces are placed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory && pieces.length > 0) {\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.checkGameCompletion['jigsaw-puzzle'](placedPieces.length, pieces.length)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        placedPieces.length,\n        pieces.length,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const completionTime = timeElapsed;\n        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n        const moveBonus = Math.max(0, 50 - moves);\n        const finalScore = score + timeBonus + moveBonus;\n        // Determine difficulty based on region\n        let difficulty = 'medium';\n        if (selectedRegion === 'North Africa') difficulty = 'easy';\n        else if (selectedRegion === 'West Africa' || selectedRegion === 'All') difficulty = 'hard';\n        const gameData = {\n            gameType: 'jigsaw-puzzle',\n            score: finalScore,\n            timeRemaining: 0,\n            totalTime: timeElapsed,\n            perfectScore: moves <= pieces.length,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.processGameCompletion)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n            // Check if puzzle is complete\n            if (placedPieces.length + 1 === pieces.length) {\n                setGameComplete(true);\n                const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n                const moveBonus = Math.max(0, 50 - moves);\n                const finalScore = score + 10 + timeBonus + moveBonus;\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"\\uD83C\\uDFC6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Puzzle Master!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Time: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 544,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"F1WMg8rV4yTrAAU+RCIaeq7Sh7U=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});