'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray, getRandomItems } from '@/utils';

interface MemoryGridProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface MemoryCard {
  id: string;
  content: string;
  type: 'flag' | 'country' | 'capital' | 'landmark' | 'animal' | 'food';
  matched: boolean;
  flipped: boolean;
}

const MemoryGrid: React.FC<MemoryGridProps> = ({ countries, onComplete }) => {
  const [cards, setCards] = useState<MemoryCard[]>([]);
  const [flippedCards, setFlippedCards] = useState<string[]>([]);
  const [matchedPairs, setMatchedPairs] = useState<string[]>([]);
  const [moves, setMoves] = useState(0);
  const [score, setScore] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  useEffect(() => {
    if (gameStarted && !gameComplete) {
      const timer = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [gameStarted, gameComplete]);

  const generateCards = () => {
    const selectedCountries = getRandomItems(countries, difficulty === 'easy' ? 4 : difficulty === 'medium' ? 6 : 8);
    const cardPairs: MemoryCard[] = [];

    selectedCountries.forEach((country, index) => {
      // Create pairs of different types
      const pairs = [
        { content: country.name, type: 'country' as const },
        { content: country.flagUrl, type: 'flag' as const },
      ];

      if (difficulty !== 'easy') {
        pairs.push({ content: country.capital, type: 'capital' as const });
        pairs.push({ content: country.landmarks[0] || 'Unknown', type: 'landmark' as const });
      }

      if (difficulty === 'hard') {
        pairs.push({ content: country.wildlife[0] || 'Unknown', type: 'animal' as const });
        pairs.push({ content: country.culturalElements.cuisine[0] || 'Unknown', type: 'food' as const });
      }

      pairs.forEach((pair, pairIndex) => {
        cardPairs.push({
          id: `${country.id}-${pairIndex}`,
          content: pair.content,
          type: pair.type,
          matched: false,
          flipped: false,
        });
      });
    });

    return shuffleArray(cardPairs);
  };

  const startGame = () => {
    const newCards = generateCards();
    setCards(newCards);
    setFlippedCards([]);
    setMatchedPairs([]);
    setMoves(0);
    setScore(0);
    setTimeElapsed(0);
    setGameComplete(false);
    setGameStarted(true);
  };

  const handleCardClick = (cardId: string) => {
    if (flippedCards.length >= 2 || flippedCards.includes(cardId) || matchedPairs.includes(cardId)) {
      return;
    }

    const newFlippedCards = [...flippedCards, cardId];
    setFlippedCards(newFlippedCards);

    // Update card state to show as flipped
    setCards(prev => prev.map(card => 
      card.id === cardId ? { ...card, flipped: true } : card
    ));

    if (newFlippedCards.length === 2) {
      setMoves(moves + 1);
      
      const [firstCardId, secondCardId] = newFlippedCards;
      const firstCard = cards.find(c => c.id === firstCardId);
      const secondCard = cards.find(c => c.id === secondCardId);

      // Check if cards belong to the same country
      const firstCountryId = firstCardId.split('-')[0];
      const secondCountryId = secondCardId.split('-')[0];

      if (firstCountryId === secondCountryId && firstCard?.type !== secondCard?.type) {
        // Match found!
        setTimeout(() => {
          setMatchedPairs(prev => [...prev, firstCardId, secondCardId]);
          setScore(score + 10);
          setFlippedCards([]);
          
          // Check if game is complete
          if (matchedPairs.length + 2 === cards.length) {
            setGameComplete(true);
            const finalScore = Math.max(0, 100 - Math.floor(timeElapsed / 10) - (moves - cards.length / 2) * 2);
            setTimeout(() => onComplete(finalScore), 1000);
          }
        }, 1000);
      } else {
        // No match
        setTimeout(() => {
          setCards(prev => prev.map(card => 
            newFlippedCards.includes(card.id) ? { ...card, flipped: false } : card
          ));
          setFlippedCards([]);
        }, 1500);
      }
    }
  };

  const getCardIcon = (type: MemoryCard['type']) => {
    switch (type) {
      case 'flag': return '🏳️';
      case 'country': return '🌍';
      case 'capital': return '🏛️';
      case 'landmark': return '🗿';
      case 'animal': return '🦁';
      case 'food': return '🍽️';
      default: return '❓';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🧠 Memory Grid</h2>
          <div className="text-6xl mb-6">🃏</div>
          <p className="text-xl text-gray-300 mb-6">
            Match pairs of cards that belong to the same African country!
          </p>
          
          {/* Difficulty Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Choose Difficulty:</h3>
            <div className="flex justify-center space-x-4">
              {(['easy', 'medium', 'hard'] as const).map((level) => (
                <button
                  key={level}
                  onClick={() => setDifficulty(level)}
                  className={`px-6 py-3 rounded-lg transition-colors ${
                    difficulty === level
                      ? 'bg-yellow-400 text-gray-900'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  <div className="font-semibold capitalize">{level}</div>
                  <div className="text-sm">
                    {level === 'easy' ? '4×2 Grid' : level === 'medium' ? '6×2 Grid' : '8×3 Grid'}
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Flip cards to reveal their content</p>
            <p>• Match country names with flags, capitals, and landmarks</p>
            <p>• Complete the grid in the fewest moves possible</p>
            <p>• Faster completion = higher score!</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Game
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🧠 Memory Grid</h2>
          <div className="text-yellow-400 text-xl font-bold">
            {formatTime(timeElapsed)}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{moves}</div>
            <div className="text-gray-400 text-sm">Moves</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{matchedPairs.length / 2}</div>
            <div className="text-gray-400 text-sm">Pairs Found</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{cards.length / 2}</div>
            <div className="text-gray-400 text-sm">Total Pairs</div>
          </div>
        </div>
      </div>

      {/* Game Grid */}
      <div className={`grid gap-4 ${
        difficulty === 'easy' ? 'grid-cols-4' : 
        difficulty === 'medium' ? 'grid-cols-6' : 
        'grid-cols-8'
      }`}>
        {cards.map((card) => {
          const isFlipped = card.flipped || flippedCards.includes(card.id) || matchedPairs.includes(card.id);
          const isMatched = matchedPairs.includes(card.id);
          
          return (
            <div
              key={card.id}
              className={`aspect-square rounded-lg border-2 cursor-pointer transition-all duration-300 flex items-center justify-center text-center p-2 ${
                isMatched
                  ? 'bg-green-500 bg-opacity-20 border-green-400'
                  : isFlipped
                  ? 'bg-blue-500 bg-opacity-20 border-blue-400'
                  : 'bg-gray-700 border-gray-600 hover:border-yellow-400'
              }`}
              onClick={() => handleCardClick(card.id)}
            >
              {isFlipped ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="text-lg mb-1">{getCardIcon(card.type)}</div>
                  <div className={`text-xs font-medium text-center leading-tight ${
                    card.type === 'flag' ? 'text-2xl' : 'text-white'
                  }`}>
                    {card.type === 'flag' ? card.content : card.content}
                  </div>
                </div>
              ) : (
                <div className="text-4xl">❓</div>
              )}
            </div>
          );
        })}
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">🎉</div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Memory Master!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Time: <span className="text-blue-400">{formatTime(timeElapsed)}</span></p>
                  <p>Moves: <span className="text-purple-400">{moves}</span></p>
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemoryGrid;
