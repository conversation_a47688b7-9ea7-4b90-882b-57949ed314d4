"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n            // Check if puzzle is complete\n            if (placedPieces.length + 1 === pieces.length) {\n                setGameComplete(true);\n                const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n                const moveBonus = Math.max(0, 50 - moves);\n                const finalScore = score + 10 + timeBonus + moveBonus;\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Drag and drop African countries to their correct positions on the map!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag country pieces to their correct positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Countries are grouped by African regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete the map as quickly as possible\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Fewer moves = higher score!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl mb-1\",\n                                                            children: placedPiece.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl\",\n                                                            children: piece.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"\\uD83C\\uDFC6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Puzzle Master!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Time: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 487,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"akLakEg/BK2nvyLbrgu3pV4cVRk=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});