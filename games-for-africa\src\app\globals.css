@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-primary-dark text-text-primary font-sans min-h-screen;
  }
}

@layer components {
  .game-card {
    @apply bg-primary-light border border-gray-700 rounded-lg p-6 cursor-pointer transition-all duration-300;
  }

  .game-card:hover {
    @apply border-accent-gold shadow-xl -translate-y-1;
  }

  .quiz-option {
    @apply bg-primary-light border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200;
  }

  .quiz-option:hover {
    @apply border-accent-gold;
  }

  .quiz-option.selected {
    @apply border-accent-gold bg-accent-gold bg-opacity-10;
  }

  .quiz-option.correct {
    @apply border-accent-green bg-accent-green bg-opacity-20;
  }

  .quiz-option.incorrect {
    @apply border-accent-crimson bg-accent-crimson bg-opacity-20;
  }

  .progress-bar {
    @apply w-full bg-gray-700 rounded-full h-2;
  }

  .progress-fill {
    @apply bg-accent-gold h-2 rounded-full transition-all duration-300;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px var(--accent-gold), 0 0 10px var(--accent-gold), 0 0 15px var(--accent-gold); }
  to { box-shadow: 0 0 10px var(--accent-gold), 0 0 20px var(--accent-gold), 0 0 30px var(--accent-gold); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Focus styles for accessibility */
.focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-gold);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
