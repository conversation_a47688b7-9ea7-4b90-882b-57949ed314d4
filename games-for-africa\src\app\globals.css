@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

:root {
  --background: #1a1a1a;
  --foreground: #ffffff;
  --primary-dark: #1a1a1a;
  --primary-light: #2d2d2d;
  --accent-gold: #FFD700;
  --accent-green: #228B22;
  --accent-crimson: #DC143C;
  --accent-blue: #4169E1;
  --text-primary: #FFFFFF;
  --text-secondary: #E0E0E0;
}

@theme inline {
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-accent-gold: var(--accent-gold);
  --color-accent-green: var(--accent-green);
  --color-accent-crimson: var(--accent-crimson);
  --color-accent-blue: var(--accent-blue);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Poppins', 'Inter', system-ui, sans-serif;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
}

/* Game-specific styles */
.game-card {
  background: var(--primary-light);
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-card:hover {
  border-color: var(--accent-gold);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-4px);
}

.quiz-option {
  background: var(--primary-light);
  border: 1px solid #4b5563;
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quiz-option:hover {
  border-color: var(--accent-gold);
}

.quiz-option.selected {
  border-color: var(--accent-gold);
  background: rgba(255, 215, 0, 0.1);
}

.quiz-option.correct {
  border-color: var(--accent-green);
  background: rgba(34, 139, 34, 0.2);
}

.quiz-option.incorrect {
  border-color: var(--accent-crimson);
  background: rgba(220, 20, 60, 0.2);
}

/* Progress bar */
.progress-bar {
  width: 100%;
  background: #374151;
  border-radius: 9999px;
  height: 0.5rem;
}

.progress-fill {
  background: var(--accent-gold);
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px var(--accent-gold), 0 0 10px var(--accent-gold), 0 0 15px var(--accent-gold); }
  to { box-shadow: 0 0 10px var(--accent-gold), 0 0 20px var(--accent-gold), 0 0 30px var(--accent-gold); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Focus styles for accessibility */
.focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-gold);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
