'use client';

import React, { useState, useEffect } from 'react';
import { getFlagImage, getFlagEmoji, getFlagAlt } from '@/utils/flagUtils';

interface FlagImageProps {
  countryId: string;
  size?: 'small' | 'medium' | 'large' | 'xl';
  format?: 'svg' | 'png';
  className?: string;
  showFallback?: boolean;
  onClick?: () => void;
}

const FlagImage: React.FC<FlagImageProps> = ({
  countryId,
  size = 'medium',
  format = 'svg',
  className = '',
  showFallback = true,
  onClick,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Try to get flag image URL, fallback to emoji if not available
  const flagImageUrl = getFlagImage(countryId, format);
  const flagEmoji = getFlagEmoji(countryId);
  const flagAlt = getFlagAlt(countryId);

  // Size configurations
  const sizeClasses = {
    small: 'w-8 h-6',
    medium: 'w-12 h-9',
    large: 'w-16 h-12',
    xl: 'w-24 h-18',
  };

  const emojiSizes = {
    small: 'text-lg',
    medium: 'text-2xl',
    large: 'text-3xl',
    xl: 'text-5xl',
  };

  useEffect(() => {
    if (!flagImageUrl) {
      setImageError(true);
      setIsLoading(false);
      return;
    }

    setImageLoaded(false);
    setImageError(false);
    setIsLoading(true);

    // Preload the image with caching optimization
    const img = new Image();
    img.crossOrigin = 'anonymous'; // Enable CORS for better caching
    img.loading = 'eager'; // Prioritize loading for visible flags

    img.onload = () => {
      setImageLoaded(true);
      setImageError(false);
      setIsLoading(false);
    };

    img.onerror = () => {
      setImageLoaded(false);
      setImageError(true);
      setIsLoading(false);
    };

    // Add cache-busting prevention and optimization
    const cachedUrl = `${flagImageUrl}?cache=1`;
    img.src = cachedUrl;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [flagImageUrl]);

  const baseClasses = `
    ${sizeClasses[size]} 
    object-cover 
    rounded-sm 
    border 
    border-gray-300 
    shadow-sm
    ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}
    ${className}
  `;

  // Show loading state
  if (isLoading && flagImageUrl) {
    return (
      <div 
        className={`${baseClasses} bg-gray-200 animate-pulse flex items-center justify-center`}
        onClick={onClick}
      >
        <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Show flag image if loaded successfully
  if (imageLoaded && flagImageUrl && !imageError) {
    return (
      <img
        src={flagImageUrl}
        alt={flagAlt}
        className={baseClasses}
        onClick={onClick}
        onError={() => setImageError(true)}
      />
    );
  }

  // Show emoji fallback if image failed to load or showFallback is true
  if (showFallback || imageError) {
    return (
      <div 
        className={`
          ${sizeClasses[size]} 
          flex 
          items-center 
          justify-center 
          bg-gray-100 
          rounded-sm 
          border 
          border-gray-300
          ${onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : ''}
          ${className}
        `}
        onClick={onClick}
        title={flagAlt}
      >
        <span className={emojiSizes[size]}>{flagEmoji}</span>
      </div>
    );
  }

  // Fallback to empty state
  return (
    <div 
      className={`${baseClasses} bg-gray-100 flex items-center justify-center`}
      onClick={onClick}
    >
      <span className="text-gray-400 text-xs">🏳️</span>
    </div>
  );
};

export default FlagImage;
