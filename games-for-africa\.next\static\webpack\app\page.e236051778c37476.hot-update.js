"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryExplorer.tsx":
/*!**************************************************!*\
  !*** ./src/components/games/CountryExplorer.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CountryExplorer = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [selectedCountry, setSelectedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [visitedCountries, setVisitedCountries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentRegion, setCurrentRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const filteredCountries = currentRegion === 'All' ? countries : countries.filter((country)=>country.region === currentRegion);\n    const handleCountryClick = (country)=>{\n        setSelectedCountry(country);\n        setVisitedCountries(new Set([\n            ...visitedCountries,\n            country.id\n        ]));\n    };\n    const getCountryCardStyle = (country)=>{\n        const isVisited = visitedCountries.has(country.id);\n        const isSelected = (selectedCountry === null || selectedCountry === void 0 ? void 0 : selectedCountry.id) === country.id;\n        let baseStyle = 'p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105';\n        if (isSelected) {\n            baseStyle += ' bg-yellow-400 bg-opacity-20 border-yellow-400 shadow-lg';\n        } else if (isVisited) {\n            baseStyle += ' bg-green-500 bg-opacity-20 border-green-400';\n        } else {\n            baseStyle += ' bg-gray-700 border-gray-600 hover:border-yellow-400';\n        }\n        return baseStyle;\n    };\n    const getRandomFact = (country)=>{\n        const facts = [\n            \"\".concat(country.name, \" has a population of \").concat(country.population.toLocaleString(), \" people.\"),\n            \"The capital of \".concat(country.name, \" is \").concat(country.capital, \".\"),\n            \"\".concat(country.name, \" gained independence on \").concat(new Date(country.independence).toLocaleDateString(), \".\"),\n            \"The currency used in \".concat(country.name, \" is \").concat(country.currency, \".\"),\n            \"Languages spoken include: \".concat(country.languages.join(', '), \".\"),\n            \"Major exports include: \".concat(country.exports.slice(0, 3).join(', '), \".\"),\n            \"Famous landmarks: \".concat(country.landmarks.slice(0, 2).join(', '), \".\"),\n            \"Wildlife includes: \".concat(country.wildlife.slice(0, 3).join(', '), \".\")\n        ];\n        return facts[Math.floor(Math.random() * facts.length)];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-4\",\n                        children: \"\\uD83C\\uDF0D Country Explorer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Click on any African country to discover amazing facts and learn about its culture!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-lg font-semibold\",\n                                children: [\n                                    \"Countries Explored: \",\n                                    visitedCountries.size,\n                                    \" / \",\n                                    countries.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-700 rounded-full h-3 mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-400 h-3 rounded-full transition-all duration-500\",\n                                    style: {\n                                        width: \"\".concat(visitedCountries.size / countries.length * 100, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-2 mb-6\",\n                        children: regions.map((region)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentRegion(region),\n                                className: \"px-4 py-2 rounded-lg transition-colors \".concat(currentRegion === region ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                children: region\n                            }, region, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    currentRegion === 'All' ? 'All African Countries' : currentRegion,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            filteredCountries.length,\n                                            \" countries)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\",\n                                children: filteredCountries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: getCountryCardStyle(country),\n                                        onClick: ()=>handleCountryClick(country),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        countryId: country.id,\n                                                        size: \"large\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: country.capital\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                visitedCountries.has(country.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-xs mt-1\",\n                                                    children: \"✓ Explored\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, country.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: selectedCountry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 sticky top-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-3\",\n                                            children: selectedCountry.flagUrl\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: selectedCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-400\",\n                                            children: selectedCountry.capital\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Quick Facts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Region:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Population:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.population.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Currency:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.currency\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Independence:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: new Date(selectedCountry.independence).getFullYear()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Languages\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: selectedCountry.languages.map((language, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-500 bg-opacity-20 text-blue-300 px-2 py-1 rounded text-xs\",\n                                                            children: language\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Culture\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Traditional Food:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.culturalElements.cuisine.slice(0, 2).join(', ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Music:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white\",\n                                                                    children: selectedCountry.culturalElements.music.slice(0, 2).join(', ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        selectedCountry.notableFigures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Notable Figures\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: selectedCountry.notableFigures.slice(0, 2).map((figure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: figure.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: figure.field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-yellow-400 font-semibold mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 Did You Know?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: getRandomFact(selectedCountry)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                    children: \"Famous Landmarks\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: selectedCountry.landmarks.slice(0, 3).map((landmark, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                \"• \",\n                                                                landmark\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDDFA️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"Select a Country\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300\",\n                                    children: \"Click on any country to explore its culture, history, and interesting facts!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            visitedCountries.size === countries.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white p-4 rounded-lg shadow-lg animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl mb-1\",\n                            children: \"\\uD83C\\uDFC6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"Explorer Achievement!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: \"You've visited all countries!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryExplorer.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryExplorer, \"/EGKITqhGmwkSf2VHS2sw/gNTZM=\");\n_c = CountryExplorer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryExplorer);\nvar _c;\n$RefreshReg$(_c, \"CountryExplorer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2dhbWVzL0NvdW50cnlFeHBsb3Jlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV3QztBQUVVO0FBT2xELE1BQU1HLGtCQUFrRDtRQUFDLEVBQUVDLFNBQVMsRUFBRUMsVUFBVSxFQUFFOztJQUNoRixNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFpQjtJQUN2RSxNQUFNLENBQUNPLGtCQUFrQkMsb0JBQW9CLEdBQUdSLCtDQUFRQSxDQUFjLElBQUlTO0lBQzFFLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdYLCtDQUFRQSxDQUFTO0lBRTNELE1BQU1ZLFVBQVU7UUFBQztRQUFPO1FBQWdCO1FBQWU7UUFBZTtRQUFrQjtLQUFrQjtJQUUxRyxNQUFNQyxvQkFBb0JILGtCQUFrQixRQUN4Q1AsWUFDQUEsVUFBVVcsTUFBTSxDQUFDQyxDQUFBQSxVQUFXQSxRQUFRQyxNQUFNLEtBQUtOO0lBRW5ELE1BQU1PLHFCQUFxQixDQUFDRjtRQUMxQlQsbUJBQW1CUztRQUNuQlAsb0JBQW9CLElBQUlDLElBQUk7ZUFBSUY7WUFBa0JRLFFBQVFHLEVBQUU7U0FBQztJQUMvRDtJQUVBLE1BQU1DLHNCQUFzQixDQUFDSjtRQUMzQixNQUFNSyxZQUFZYixpQkFBaUJjLEdBQUcsQ0FBQ04sUUFBUUcsRUFBRTtRQUNqRCxNQUFNSSxhQUFhakIsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJhLEVBQUUsTUFBS0gsUUFBUUcsRUFBRTtRQUVyRCxJQUFJSyxZQUFZO1FBRWhCLElBQUlELFlBQVk7WUFDZEMsYUFBYTtRQUNmLE9BQU8sSUFBSUgsV0FBVztZQUNwQkcsYUFBYTtRQUNmLE9BQU87WUFDTEEsYUFBYTtRQUNmO1FBRUEsT0FBT0E7SUFDVDtJQUVBLE1BQU1DLGdCQUFnQixDQUFDVDtRQUNyQixNQUFNVSxRQUFRO1lBQ1gsR0FBc0NWLE9BQXBDQSxRQUFRVyxJQUFJLEVBQUMseUJBQTJELE9BQXBDWCxRQUFRWSxVQUFVLENBQUNDLGNBQWMsSUFBRztZQUMxRSxrQkFBb0NiLE9BQW5CQSxRQUFRVyxJQUFJLEVBQUMsUUFBc0IsT0FBaEJYLFFBQVFjLE9BQU8sRUFBQztZQUNwRCxHQUF5QyxPQUF2Q2QsUUFBUVcsSUFBSSxFQUFDLDRCQUE4RSxPQUFwRCxJQUFJSSxLQUFLZixRQUFRZ0IsWUFBWSxFQUFFQyxrQkFBa0IsSUFBRztZQUM3Rix3QkFBMENqQixPQUFuQkEsUUFBUVcsSUFBSSxFQUFDLFFBQXVCLE9BQWpCWCxRQUFRa0IsUUFBUSxFQUFDO1lBQzNELDZCQUF5RCxPQUE3QmxCLFFBQVFtQixTQUFTLENBQUNDLElBQUksQ0FBQyxPQUFNO1lBQ3pELDBCQUFnRSxPQUF2Q3BCLFFBQVFxQixPQUFPLENBQUNDLEtBQUssQ0FBQyxHQUFHLEdBQUdGLElBQUksQ0FBQyxPQUFNO1lBQ2hFLHFCQUE2RCxPQUF6Q3BCLFFBQVF1QixTQUFTLENBQUNELEtBQUssQ0FBQyxHQUFHLEdBQUdGLElBQUksQ0FBQyxPQUFNO1lBQzdELHNCQUE2RCxPQUF4Q3BCLFFBQVF3QixRQUFRLENBQUNGLEtBQUssQ0FBQyxHQUFHLEdBQUdGLElBQUksQ0FBQyxPQUFNO1NBQy9EO1FBRUQsT0FBT1YsS0FBSyxDQUFDZSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS2pCLE1BQU1rQixNQUFNLEVBQUU7SUFDeEQ7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXFDOzs7Ozs7a0NBQ25ELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBNkI7Ozs7OztrQ0FLMUMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O29DQUF3QztvQ0FDaEN0QyxpQkFBaUJ5QyxJQUFJO29DQUFDO29DQUFJN0MsVUFBVXdDLE1BQU07Ozs7Ozs7MENBRWpFLDhEQUFDQztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQ0NDLFdBQVU7b0NBQ1ZJLE9BQU87d0NBQUVDLE9BQU8sR0FBb0QsT0FBakQsaUJBQWtCRixJQUFJLEdBQUc3QyxVQUFVd0MsTUFBTSxHQUFJLEtBQUk7b0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU03RSw4REFBQ0M7d0JBQUlDLFdBQVU7a0NBQ1pqQyxRQUFRdUMsR0FBRyxDQUFDLENBQUNuQyx1QkFDWiw4REFBQ29DO2dDQUVDQyxTQUFTLElBQU0xQyxpQkFBaUJLO2dDQUNoQzZCLFdBQVcsMENBSVYsT0FIQ25DLGtCQUFrQk0sU0FDZCxnQ0FDQTswQ0FHTEE7K0JBUklBOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWNiLDhEQUFDNEI7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNTO2dDQUFHVCxXQUFVOztvQ0FDWG5DLGtCQUFrQixRQUFRLDBCQUEwQkE7a0RBQ3JELDhEQUFDNkM7d0NBQUtWLFdBQVU7OzRDQUFxQjs0Q0FBRWhDLGtCQUFrQjhCLE1BQU07NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBR2xFLDhEQUFDQztnQ0FBSUMsV0FBVTswQ0FDWmhDLGtCQUFrQnNDLEdBQUcsQ0FBQyxDQUFDcEMsd0JBQ3RCLDhEQUFDNkI7d0NBRUNDLFdBQVcxQixvQkFBb0JKO3dDQUMvQnNDLFNBQVMsSUFBTXBDLG1CQUFtQkY7a0RBRWxDLDRFQUFDNkI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzVDLGdFQUFTQTt3REFDUnVELFdBQVd6QyxRQUFRRyxFQUFFO3dEQUNyQjhCLE1BQUs7d0RBQ0xILFdBQVU7Ozs7Ozs7Ozs7OzhEQUdkLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBNEI5QixRQUFRVyxJQUFJOzs7Ozs7OERBQ3ZELDhEQUFDa0I7b0RBQUlDLFdBQVU7OERBQXlCOUIsUUFBUWMsT0FBTzs7Ozs7O2dEQUN0RHRCLGlCQUFpQmMsR0FBRyxDQUFDTixRQUFRRyxFQUFFLG1CQUM5Qiw4REFBQzBCO29EQUFJQyxXQUFVOzhEQUE4Qjs7Ozs7Ozs7Ozs7O3VDQWY1QzlCLFFBQVFHLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBd0J2Qiw4REFBQzBCO3dCQUFJQyxXQUFVO2tDQUNaeEMsZ0NBQ0MsOERBQUN1Qzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQWlCeEMsZ0JBQWdCb0QsT0FBTzs7Ozs7O3NEQUN2RCw4REFBQ0g7NENBQUdULFdBQVU7c0RBQWlDeEMsZ0JBQWdCcUIsSUFBSTs7Ozs7O3NEQUNuRSw4REFBQ3FCOzRDQUFFRixXQUFVO3NEQUFtQnhDLGdCQUFnQndCLE9BQU87Ozs7Ozs7Ozs7Ozs4Q0FHekQsOERBQUNlO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7OzhEQUNDLDhEQUFDYztvREFBR2IsV0FBVTs4REFBd0M7Ozs7Ozs4REFDdEQsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDVTtvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNVO29FQUFLVixXQUFVOzhFQUFjeEMsZ0JBQWdCVyxNQUFNOzs7Ozs7Ozs7Ozs7c0VBRXRELDhEQUFDNEI7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDVTtvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNVO29FQUFLVixXQUFVOzhFQUFjeEMsZ0JBQWdCc0IsVUFBVSxDQUFDQyxjQUFjOzs7Ozs7Ozs7Ozs7c0VBRXpFLDhEQUFDZ0I7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDVTtvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNVO29FQUFLVixXQUFVOzhFQUFjeEMsZ0JBQWdCNEIsUUFBUTs7Ozs7Ozs7Ozs7O3NFQUV4RCw4REFBQ1c7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDVTtvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNVO29FQUFLVixXQUFVOzhFQUFjLElBQUlmLEtBQUt6QixnQkFBZ0IwQixZQUFZLEVBQUU0QixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTXRGLDhEQUFDZjs7OERBQ0MsOERBQUNjO29EQUFHYixXQUFVOzhEQUF3Qzs7Ozs7OzhEQUN0RCw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1p4QyxnQkFBZ0I2QixTQUFTLENBQUNpQixHQUFHLENBQUMsQ0FBQ1MsVUFBVUMsc0JBQ3hDLDhEQUFDTjs0REFFQ1YsV0FBVTtzRUFFVGU7MkRBSElDOzs7Ozs7Ozs7Ozs7Ozs7O3NEQVViLDhEQUFDakI7OzhEQUNDLDhEQUFDYztvREFBR2IsV0FBVTs4REFBd0M7Ozs7Ozs4REFDdEQsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7OzhFQUNDLDhEQUFDVztvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNFO29FQUFFRixXQUFVOzhFQUFjeEMsZ0JBQWdCeUQsZ0JBQWdCLENBQUNDLE9BQU8sQ0FBQzFCLEtBQUssQ0FBQyxHQUFHLEdBQUdGLElBQUksQ0FBQzs7Ozs7Ozs7Ozs7O3NFQUV2Riw4REFBQ1M7OzhFQUNDLDhEQUFDVztvRUFBS1YsV0FBVTs4RUFBZ0I7Ozs7Ozs4RUFDaEMsOERBQUNFO29FQUFFRixXQUFVOzhFQUFjeEMsZ0JBQWdCeUQsZ0JBQWdCLENBQUNFLEtBQUssQ0FBQzNCLEtBQUssQ0FBQyxHQUFHLEdBQUdGLElBQUksQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQU14RjlCLGdCQUFnQjRELGNBQWMsQ0FBQ3RCLE1BQU0sR0FBRyxtQkFDdkMsOERBQUNDOzs4REFDQyw4REFBQ2M7b0RBQUdiLFdBQVU7OERBQXdDOzs7Ozs7OERBQ3RELDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWnhDLGdCQUFnQjRELGNBQWMsQ0FBQzVCLEtBQUssQ0FBQyxHQUFHLEdBQUdjLEdBQUcsQ0FBQyxDQUFDZSxRQUFRTCxzQkFDdkQsOERBQUNqQjs0REFBZ0JDLFdBQVU7OzhFQUN6Qiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQTBCcUIsT0FBT3hDLElBQUk7Ozs7Ozs4RUFDcEQsOERBQUNrQjtvRUFBSUMsV0FBVTs4RUFBeUJxQixPQUFPQyxLQUFLOzs7Ozs7OzJEQUY1Q047Ozs7Ozs7Ozs7Ozs7Ozs7c0RBVWxCLDhEQUFDakI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDYTtvREFBR2IsV0FBVTs4REFBcUM7Ozs7Ozs4REFDbkQsOERBQUNFO29EQUFFRixXQUFVOzhEQUFzQnJCLGNBQWNuQjs7Ozs7Ozs7Ozs7O3NEQUluRCw4REFBQ3VDOzs4REFDQyw4REFBQ2M7b0RBQUdiLFdBQVU7OERBQXdDOzs7Ozs7OERBQ3RELDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWnhDLGdCQUFnQmlDLFNBQVMsQ0FBQ0QsS0FBSyxDQUFDLEdBQUcsR0FBR2MsR0FBRyxDQUFDLENBQUNpQixVQUFVUCxzQkFDcEQsOERBQUNqQjs0REFBZ0JDLFdBQVU7O2dFQUF3QjtnRUFDOUN1Qjs7MkRBREtQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBU3BCLDhEQUFDakI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBZ0I7Ozs7Ozs4Q0FDL0IsOERBQUNTO29DQUFHVCxXQUFVOzhDQUF3Qzs7Ozs7OzhDQUN0RCw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNwQ3RDLGlCQUFpQnlDLElBQUksS0FBSzdDLFVBQVV3QyxNQUFNLGtCQUN6Qyw4REFBQ0M7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQWdCOzs7Ozs7c0NBQy9CLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FBWTs7Ozs7O3NDQUMzQiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJDO0dBcFBNM0M7S0FBQUE7QUFzUE4saUVBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFxjb21wb25lbnRzXFxnYW1lc1xcQ291bnRyeUV4cGxvcmVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENvdW50cnkgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCBGbGFnSW1hZ2UgZnJvbSAnQC9jb21wb25lbnRzL3VpL0ZsYWdJbWFnZSc7XG5cbmludGVyZmFjZSBDb3VudHJ5RXhwbG9yZXJQcm9wcyB7XG4gIGNvdW50cmllczogQ291bnRyeVtdO1xuICBvbkNvbXBsZXRlOiAoc2NvcmU6IG51bWJlcikgPT4gdm9pZDtcbn1cblxuY29uc3QgQ291bnRyeUV4cGxvcmVyOiBSZWFjdC5GQzxDb3VudHJ5RXhwbG9yZXJQcm9wcz4gPSAoeyBjb3VudHJpZXMsIG9uQ29tcGxldGUgfSkgPT4ge1xuICBjb25zdCBbc2VsZWN0ZWRDb3VudHJ5LCBzZXRTZWxlY3RlZENvdW50cnldID0gdXNlU3RhdGU8Q291bnRyeSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbdmlzaXRlZENvdW50cmllcywgc2V0VmlzaXRlZENvdW50cmllc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW2N1cnJlbnRSZWdpb24sIHNldEN1cnJlbnRSZWdpb25dID0gdXNlU3RhdGU8c3RyaW5nPignQWxsJyk7XG5cbiAgY29uc3QgcmVnaW9ucyA9IFsnQWxsJywgJ05vcnRoIEFmcmljYScsICdXZXN0IEFmcmljYScsICdFYXN0IEFmcmljYScsICdDZW50cmFsIEFmcmljYScsICdTb3V0aGVybiBBZnJpY2EnXTtcblxuICBjb25zdCBmaWx0ZXJlZENvdW50cmllcyA9IGN1cnJlbnRSZWdpb24gPT09ICdBbGwnIFxuICAgID8gY291bnRyaWVzIFxuICAgIDogY291bnRyaWVzLmZpbHRlcihjb3VudHJ5ID0+IGNvdW50cnkucmVnaW9uID09PSBjdXJyZW50UmVnaW9uKTtcblxuICBjb25zdCBoYW5kbGVDb3VudHJ5Q2xpY2sgPSAoY291bnRyeTogQ291bnRyeSkgPT4ge1xuICAgIHNldFNlbGVjdGVkQ291bnRyeShjb3VudHJ5KTtcbiAgICBzZXRWaXNpdGVkQ291bnRyaWVzKG5ldyBTZXQoWy4uLnZpc2l0ZWRDb3VudHJpZXMsIGNvdW50cnkuaWRdKSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q291bnRyeUNhcmRTdHlsZSA9IChjb3VudHJ5OiBDb3VudHJ5KSA9PiB7XG4gICAgY29uc3QgaXNWaXNpdGVkID0gdmlzaXRlZENvdW50cmllcy5oYXMoY291bnRyeS5pZCk7XG4gICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkQ291bnRyeT8uaWQgPT09IGNvdW50cnkuaWQ7XG4gICAgXG4gICAgbGV0IGJhc2VTdHlsZSA9ICdwLTQgcm91bmRlZC1sZyBib3JkZXItMiBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1JztcbiAgICBcbiAgICBpZiAoaXNTZWxlY3RlZCkge1xuICAgICAgYmFzZVN0eWxlICs9ICcgYmcteWVsbG93LTQwMCBiZy1vcGFjaXR5LTIwIGJvcmRlci15ZWxsb3ctNDAwIHNoYWRvdy1sZyc7XG4gICAgfSBlbHNlIGlmIChpc1Zpc2l0ZWQpIHtcbiAgICAgIGJhc2VTdHlsZSArPSAnIGJnLWdyZWVuLTUwMCBiZy1vcGFjaXR5LTIwIGJvcmRlci1ncmVlbi00MDAnO1xuICAgIH0gZWxzZSB7XG4gICAgICBiYXNlU3R5bGUgKz0gJyBiZy1ncmF5LTcwMCBib3JkZXItZ3JheS02MDAgaG92ZXI6Ym9yZGVyLXllbGxvdy00MDAnO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYmFzZVN0eWxlO1xuICB9O1xuXG4gIGNvbnN0IGdldFJhbmRvbUZhY3QgPSAoY291bnRyeTogQ291bnRyeSkgPT4ge1xuICAgIGNvbnN0IGZhY3RzID0gW1xuICAgICAgYCR7Y291bnRyeS5uYW1lfSBoYXMgYSBwb3B1bGF0aW9uIG9mICR7Y291bnRyeS5wb3B1bGF0aW9uLnRvTG9jYWxlU3RyaW5nKCl9IHBlb3BsZS5gLFxuICAgICAgYFRoZSBjYXBpdGFsIG9mICR7Y291bnRyeS5uYW1lfSBpcyAke2NvdW50cnkuY2FwaXRhbH0uYCxcbiAgICAgIGAke2NvdW50cnkubmFtZX0gZ2FpbmVkIGluZGVwZW5kZW5jZSBvbiAke25ldyBEYXRlKGNvdW50cnkuaW5kZXBlbmRlbmNlKS50b0xvY2FsZURhdGVTdHJpbmcoKX0uYCxcbiAgICAgIGBUaGUgY3VycmVuY3kgdXNlZCBpbiAke2NvdW50cnkubmFtZX0gaXMgJHtjb3VudHJ5LmN1cnJlbmN5fS5gLFxuICAgICAgYExhbmd1YWdlcyBzcG9rZW4gaW5jbHVkZTogJHtjb3VudHJ5Lmxhbmd1YWdlcy5qb2luKCcsICcpfS5gLFxuICAgICAgYE1ham9yIGV4cG9ydHMgaW5jbHVkZTogJHtjb3VudHJ5LmV4cG9ydHMuc2xpY2UoMCwgMykuam9pbignLCAnKX0uYCxcbiAgICAgIGBGYW1vdXMgbGFuZG1hcmtzOiAke2NvdW50cnkubGFuZG1hcmtzLnNsaWNlKDAsIDIpLmpvaW4oJywgJyl9LmAsXG4gICAgICBgV2lsZGxpZmUgaW5jbHVkZXM6ICR7Y291bnRyeS53aWxkbGlmZS5zbGljZSgwLCAzKS5qb2luKCcsICcpfS5gLFxuICAgIF07XG4gICAgXG4gICAgcmV0dXJuIGZhY3RzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGZhY3RzLmxlbmd0aCldO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBwLTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj7wn4yNIENvdW50cnkgRXhwbG9yZXI8L2gyPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0zMDAgbWItNlwiPlxuICAgICAgICAgIENsaWNrIG9uIGFueSBBZnJpY2FuIGNvdW50cnkgdG8gZGlzY292ZXIgYW1hemluZyBmYWN0cyBhbmQgbGVhcm4gYWJvdXQgaXRzIGN1bHR1cmUhXG4gICAgICAgIDwvcD5cbiAgICAgICAgXG4gICAgICAgIHsvKiBQcm9ncmVzcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC1sZyBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICBDb3VudHJpZXMgRXhwbG9yZWQ6IHt2aXNpdGVkQ291bnRyaWVzLnNpemV9IC8ge2NvdW50cmllcy5sZW5ndGh9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAgcm91bmRlZC1mdWxsIGgtMyBtdC0yXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXllbGxvdy00MDAgaC0zIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDBcIlxuICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7KHZpc2l0ZWRDb3VudHJpZXMuc2l6ZSAvIGNvdW50cmllcy5sZW5ndGgpICogMTAwfSVgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmVnaW9uIEZpbHRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBnYXAtMiBtYi02XCI+XG4gICAgICAgICAge3JlZ2lvbnMubWFwKChyZWdpb24pID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtyZWdpb259XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRSZWdpb24ocmVnaW9uKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNCBweS0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBjdXJyZW50UmVnaW9uID09PSByZWdpb25cbiAgICAgICAgICAgICAgICAgID8gJ2JnLXllbGxvdy00MDAgdGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktNzAwIHRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS02MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7cmVnaW9ufVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICB7LyogQ291bnRyaWVzIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICB7Y3VycmVudFJlZ2lvbiA9PT0gJ0FsbCcgPyAnQWxsIEFmcmljYW4gQ291bnRyaWVzJyA6IGN1cnJlbnRSZWdpb259XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1sLTJcIj4oe2ZpbHRlcmVkQ291bnRyaWVzLmxlbmd0aH0gY291bnRyaWVzKTwvc3Bhbj5cbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAge2ZpbHRlcmVkQ291bnRyaWVzLm1hcCgoY291bnRyeSkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtjb3VudHJ5LmlkfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0Q291bnRyeUNhcmRTdHlsZShjb3VudHJ5KX1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDb3VudHJ5Q2xpY2soY291bnRyeSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTIgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8RmxhZ0ltYWdlXG4gICAgICAgICAgICAgICAgICAgICAgY291bnRyeUlkPXtjb3VudHJ5LmlkfVxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXgtYXV0b1wiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCI+e2NvdW50cnkubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtXCI+e2NvdW50cnkuY2FwaXRhbH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHt2aXNpdGVkQ291bnRyaWVzLmhhcyhjb3VudHJ5LmlkKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgdGV4dC14cyBtdC0xXCI+4pyTIEV4cGxvcmVkPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ291bnRyeSBEZXRhaWxzIFBhbmVsICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICB7c2VsZWN0ZWRDb3VudHJ5ID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02IHN0aWNreSB0b3AtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTNcIj57c2VsZWN0ZWRDb3VudHJ5LmZsYWdVcmx9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+e3NlbGVjdGVkQ291bnRyeS5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+e3NlbGVjdGVkQ291bnRyeS5jYXBpdGFsfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7LyogUXVpY2sgRmFjdHMgKi99XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+UXVpY2sgRmFjdHM8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlJlZ2lvbjo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntzZWxlY3RlZENvdW50cnkucmVnaW9ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+UG9wdWxhdGlvbjo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntzZWxlY3RlZENvdW50cnkucG9wdWxhdGlvbi50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+Q3VycmVuY3k6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57c2VsZWN0ZWRDb3VudHJ5LmN1cnJlbmN5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+SW5kZXBlbmRlbmNlOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+e25ldyBEYXRlKHNlbGVjdGVkQ291bnRyeS5pbmRlcGVuZGVuY2UpLmdldEZ1bGxZZWFyKCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIExhbmd1YWdlcyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5MYW5ndWFnZXM8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRDb3VudHJ5Lmxhbmd1YWdlcy5tYXAoKGxhbmd1YWdlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgYmctb3BhY2l0eS0yMCB0ZXh0LWJsdWUtMzAwIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtsYW5ndWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ3VsdHVyYWwgRWxlbWVudHMgKi99XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+Q3VsdHVyZTwvaDQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlRyYWRpdGlvbmFsIEZvb2Q6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57c2VsZWN0ZWRDb3VudHJ5LmN1bHR1cmFsRWxlbWVudHMuY3Vpc2luZS5zbGljZSgwLCAyKS5qb2luKCcsICcpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPk11c2ljOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+e3NlbGVjdGVkQ291bnRyeS5jdWx0dXJhbEVsZW1lbnRzLm11c2ljLnNsaWNlKDAsIDIpLmpvaW4oJywgJyl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIE5vdGFibGUgRmlndXJlcyAqL31cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRDb3VudHJ5Lm5vdGFibGVGaWd1cmVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5Ob3RhYmxlIEZpZ3VyZXM8L2g0PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZENvdW50cnkubm90YWJsZUZpZ3VyZXMuc2xpY2UoMCwgMikubWFwKChmaWd1cmUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e2ZpZ3VyZS5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPntmaWd1cmUuZmllbGR9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgey8qIFJhbmRvbSBGYWN0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTQwMCBiZy1vcGFjaXR5LTEwIGJvcmRlciBib3JkZXIteWVsbG93LTQwMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCBmb250LXNlbWlib2xkIG1iLTJcIj7wn5KhIERpZCBZb3UgS25vdz88L2g0PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtXCI+e2dldFJhbmRvbUZhY3Qoc2VsZWN0ZWRDb3VudHJ5KX08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogTGFuZG1hcmtzICovfVxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPkZhbW91cyBMYW5kbWFya3M8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQ291bnRyeS5sYW5kbWFya3Muc2xpY2UoMCwgMykubWFwKChsYW5kbWFyaywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg4oCiIHtsYW5kbWFya31cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPvCfl7rvuI88L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5TZWxlY3QgYSBDb3VudHJ5PC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIENsaWNrIG9uIGFueSBjb3VudHJ5IHRvIGV4cGxvcmUgaXRzIGN1bHR1cmUsIGhpc3RvcnksIGFuZCBpbnRlcmVzdGluZyBmYWN0cyFcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFjaGlldmVtZW50IE5vdGlmaWNhdGlvbiAqL31cbiAgICAgIHt2aXNpdGVkQ291bnRyaWVzLnNpemUgPT09IGNvdW50cmllcy5sZW5ndGggJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS02IHJpZ2h0LTYgYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgcC00IHJvdW5kZWQtbGcgc2hhZG93LWxnIGFuaW1hdGUtYm91bmNlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtYi0xXCI+8J+PhjwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LWJvbGRcIj5FeHBsb3JlciBBY2hpZXZlbWVudCE8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPllvdSd2ZSB2aXNpdGVkIGFsbCBjb3VudHJpZXMhPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENvdW50cnlFeHBsb3JlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiRmxhZ0ltYWdlIiwiQ291bnRyeUV4cGxvcmVyIiwiY291bnRyaWVzIiwib25Db21wbGV0ZSIsInNlbGVjdGVkQ291bnRyeSIsInNldFNlbGVjdGVkQ291bnRyeSIsInZpc2l0ZWRDb3VudHJpZXMiLCJzZXRWaXNpdGVkQ291bnRyaWVzIiwiU2V0IiwiY3VycmVudFJlZ2lvbiIsInNldEN1cnJlbnRSZWdpb24iLCJyZWdpb25zIiwiZmlsdGVyZWRDb3VudHJpZXMiLCJmaWx0ZXIiLCJjb3VudHJ5IiwicmVnaW9uIiwiaGFuZGxlQ291bnRyeUNsaWNrIiwiaWQiLCJnZXRDb3VudHJ5Q2FyZFN0eWxlIiwiaXNWaXNpdGVkIiwiaGFzIiwiaXNTZWxlY3RlZCIsImJhc2VTdHlsZSIsImdldFJhbmRvbUZhY3QiLCJmYWN0cyIsIm5hbWUiLCJwb3B1bGF0aW9uIiwidG9Mb2NhbGVTdHJpbmciLCJjYXBpdGFsIiwiRGF0ZSIsImluZGVwZW5kZW5jZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImN1cnJlbmN5IiwibGFuZ3VhZ2VzIiwiam9pbiIsImV4cG9ydHMiLCJzbGljZSIsImxhbmRtYXJrcyIsIndpbGRsaWZlIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwic2l6ZSIsInN0eWxlIiwid2lkdGgiLCJtYXAiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJzcGFuIiwiY291bnRyeUlkIiwiZmxhZ1VybCIsImg0IiwiZ2V0RnVsbFllYXIiLCJsYW5ndWFnZSIsImluZGV4IiwiY3VsdHVyYWxFbGVtZW50cyIsImN1aXNpbmUiLCJtdXNpYyIsIm5vdGFibGVGaWd1cmVzIiwiZmlndXJlIiwiZmllbGQiLCJsYW5kbWFyayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryExplorer.tsx\n"));

/***/ })

});