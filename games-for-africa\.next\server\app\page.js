/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01FRUslMjBFREVOJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0dBTUVTJTIwRk9SJTIwQVJJQ0ElNUMlNUNnYW1lcy1mb3ItYWZyaWNhJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"86919bd855d8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1FRUsgRURFTlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxHQU1FUyBGT1IgQVJJQ0FcXGdhbWVzLWZvci1hZnJpY2FcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg2OTE5YmQ4NTVkOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Games for Africa - Educational Gaming Platform\",\n    description: \"Learn about African countries, cultures, and achievements through interactive games and quizzes.\",\n    keywords: \"Africa, education, games, countries, culture, geography, quiz\",\n    authors: [\n        {\n            name: \"Games for Africa Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"viewport\",\n                    content: \"width=device-width, initial-scale=1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n                suppressHydrationWarning: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\GAMES FOR ARICA\\games-for-africa\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01FRUslMjBFREVOJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0dBTUVTJTIwRk9SJTIwQVJJQ0ElNUMlNUNnYW1lcy1mb3ItYWZyaWNhJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTUVFSyUyMEVERU4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDR0FNRVMlMjBGT1IlMjBBUklDQSU1QyU1Q2dhbWVzLWZvci1hZnJpY2ElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNNRUVLJTIwRURFTiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNHQU1FUyUyMEZPUiUyMEFSSUNBJTVDJTVDZ2FtZXMtZm9yLWFmcmljYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01FRUslMjBFREVOJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0dBTUVTJTIwRk9SJTIwQVJJQ0ElNUMlNUNnYW1lcy1mb3ItYWZyaWNhJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNNRUVLJTIwRURFTiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNHQU1FUyUyMEZPUiUyMEFSSUNBJTVDJTVDZ2FtZXMtZm9yLWFmcmljYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTUVFSyUyMEVERU4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDR0FNRVMlMjBGT1IlMjBBUklDQSU1QyU1Q2dhbWVzLWZvci1hZnJpY2ElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01FRUslMjBFREVOJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0dBTUVTJTIwRk9SJTIwQVJJQ0ElNUMlNUNnYW1lcy1mb3ItYWZyaWNhJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNNRUVLJTIwRURFTiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNHQU1FUyUyMEZPUiUyMEFSSUNBJTVDJTVDZ2FtZXMtZm9yLWFmcmljYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF1TDtBQUN2TDtBQUNBLDBPQUEwTDtBQUMxTDtBQUNBLDBPQUEwTDtBQUMxTDtBQUNBLG9SQUFnTjtBQUNoTjtBQUNBLHdPQUF5TDtBQUN6TDtBQUNBLDRQQUFvTTtBQUNwTTtBQUNBLGtRQUF1TTtBQUN2TTtBQUNBLHNRQUF3TSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE1FRUsgRURFTlxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxHQU1FUyBGT1IgQVJJQ0FcXFxcZ2FtZXMtZm9yLWFmcmljYVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxNRUVLIEVERU5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcR0FNRVMgRk9SIEFSSUNBXFxcXGdhbWVzLWZvci1hZnJpY2FcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE1FRUsgRURFTlxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxHQU1FUyBGT1IgQVJJQ0FcXFxcZ2FtZXMtZm9yLWFmcmljYVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE1FRUsgRURFTlxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxHQU1FUyBGT1IgQVJJQ0FcXFxcZ2FtZXMtZm9yLWFmcmljYVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxNRUVLIEVERU5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcR0FNRVMgRk9SIEFSSUNBXFxcXGdhbWVzLWZvci1hZnJpY2FcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01FRUslMjBFREVOJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0dBTUVTJTIwRk9SJTIwQVJJQ0ElNUMlNUNnYW1lcy1mb3ItYWZyaWNhJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useProgressStore */ \"(ssr)/./src/stores/useProgressStore.ts\");\n/* harmony import */ var _components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserDashboard */ \"(ssr)/./src/components/UserDashboard.tsx\");\n/* harmony import */ var _components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CelebrationModal */ \"(ssr)/./src/components/CelebrationModal.tsx\");\n/* harmony import */ var _components_ui_LazyGameLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LazyGameLoader */ \"(ssr)/./src/components/ui/LazyGameLoader.tsx\");\n/* harmony import */ var _components_ui_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSkeleton */ \"(ssr)/./src/components/ui/LoadingSkeleton.tsx\");\n/* harmony import */ var _utils_dataLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/dataLoader */ \"(ssr)/./src/utils/dataLoader.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Countries data will be loaded dynamically\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    },\n    {\n        type: 'country-name-scramble',\n        name: 'Country Name Scramble',\n        description: 'Unscramble letters to form African country names',\n        icon: '🔤',\n        difficulty: 'medium',\n        estimatedTime: 10,\n        maxScore: 300\n    },\n    {\n        type: 'flag-matching',\n        name: 'Flag Matching',\n        description: 'Match African country flags with their names',\n        icon: '🏁',\n        difficulty: 'easy',\n        estimatedTime: 6,\n        maxScore: 200\n    }\n];\nfunction Home() {\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [celebrationData, setCelebrationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: 'gameComplete',\n        data: {}\n    });\n    const { profile, loadProfile, recordGameCompletion } = (0,_stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsClient(true);\n            loadProfile();\n        }\n    }[\"Home.useEffect\"], [\n        loadProfile\n    ]);\n    const [countries, setCountries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [countriesLoading, setCountriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Load countries data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadData = {\n                \"Home.useEffect.loadData\": async ()=>{\n                    try {\n                        setCountriesLoading(true);\n                        await (0,_utils_dataLoader__WEBPACK_IMPORTED_MODULE_7__.initializeDataLoading)();\n                        const countriesData = await (0,_utils_dataLoader__WEBPACK_IMPORTED_MODULE_7__.loadCountriesData)();\n                        setCountries(countriesData);\n                    } catch (error) {\n                        console.error('Failed to load countries data:', error);\n                    } finally{\n                        setCountriesLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = async (score)=>{\n        if (!currentGame) return;\n        const gameTitle = gameConfigs.find((g)=>g.type === currentGame)?.name || currentGame;\n        const completionTime = 300; // This should be passed from the game component\n        const difficulty = profile?.preferences.difficulty || 'intermediate';\n        const isPerfectScore = score >= 100; // This should be determined by the game\n        try {\n            const result = await recordGameCompletion(currentGame, score, completionTime, difficulty, isPerfectScore);\n            // Show game completion celebration\n            setCelebrationData({\n                isOpen: true,\n                type: 'gameComplete',\n                data: {\n                    score,\n                    gameTitle,\n                    xpGained: result.xpGained\n                }\n            });\n            // Show level up celebration if applicable\n            if (result.levelUp) {\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'levelUp',\n                        data: {\n                            newLevel: profile?.level,\n                            xpGained: result.xpGained\n                        }\n                    });\n                }, 2000);\n            }\n            // Show achievement celebrations\n            result.newAchievements.forEach((achievement, index)=>{\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'achievement',\n                        data: {\n                            achievement\n                        }\n                    });\n                }, 3000 + index * 2000);\n            });\n        } catch (error) {\n            console.error('Error recording game completion:', error);\n        }\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete,\n            category: selectedCategory,\n            difficulty: selectedDifficulty\n        };\n        if (!currentGame) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Game Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"This game is being developed.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LazyGameLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            gameType: currentGame,\n            gameProps: gameProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 247,\n            columnNumber: 12\n        }, this);\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl text-accent-gold\",\n                        children: \"Loading Games for Africa...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-gold font-medium\",\n                                                    children: profile.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"Level \",\n                                                        profile.level,\n                                                        \" • \",\n                                                        profile.totalXP,\n                                                        \" XP\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowDashboard(true),\n                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-gray-900 font-bold hover:scale-105 transition-transform\",\n                                            children: profile.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: !profile ? // Show loading skeletons while profile is loading\n                            Array.from({\n                                length: 12\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    type: \"game-card\"\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this)) : gameConfigs.map((game)=>{\n                                const gameProgress = profile?.gamesProgress[game.type];\n                                const isCompleted = gameProgress?.completed || false;\n                                const bestScore = gameProgress?.bestScore || 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `bg-primary-light border rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer relative ${isCompleted ? 'border-green-400' : 'border-gray-700'}`,\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: [\n                                        isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl\",\n                                                            children: game.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"⏱️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        game.estimatedTime,\n                                                                        \"min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                    children: game.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4 flex-grow\",\n                                                    children: game.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-2 py-1 rounded text-xs font-medium ${game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'}`,\n                                                            children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                bestScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-400 text-sm font-medium\",\n                                                                    children: [\n                                                                        \"Best: \",\n                                                                        bestScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: [\n                                                                        \"Max: \",\n                                                                        game.maxScore,\n                                                                        \" pts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                    children: \"▶️ Play Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, game.type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            showDashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onClose: ()=>setShowDashboard(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: celebrationData.isOpen,\n                onClose: ()=>setCelebrationData((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        })),\n                type: celebrationData.type,\n                data: celebrationData.data\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 280,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CelebrationModal.tsx":
/*!*********************************************!*\
  !*** ./src/components/CelebrationModal.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CelebrationModal = ({ isOpen, onClose, type, data })=>{\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"CelebrationModal.useEffect\": ()=>{\n            if (isOpen) {\n                setShowConfetti(true);\n                const timer = setTimeout({\n                    \"CelebrationModal.useEffect.timer\": ()=>{\n                        setShowConfetti(false);\n                    }\n                }[\"CelebrationModal.useEffect.timer\"], 3000);\n                return ({\n                    \"CelebrationModal.useEffect\": ()=>clearTimeout(timer)\n                })[\"CelebrationModal.useEffect\"];\n            }\n        }\n    }[\"CelebrationModal.useEffect\"], [\n        isOpen\n    ]);\n    if (!isOpen) return null;\n    const renderContent = ()=>{\n        switch(type){\n            case 'achievement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-8xl animate-bounce\",\n                            children: data.achievement?.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-yellow-400 mb-2\",\n                                    children: \"Achievement Unlocked!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-semibold text-white mb-2\",\n                                    children: data.achievement?.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: data.achievement?.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-400 font-semibold\",\n                                children: \"+100 XP Bonus for Achievement!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            case 'levelUp':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-8xl animate-pulse\",\n                            children: \"\\uD83C\\uDF89\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-yellow-400 mb-2\",\n                                    children: \"Level Up!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-semibold text-white mb-2\",\n                                    children: [\n                                        \"You reached Level \",\n                                        data.newLevel,\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"Keep exploring Africa to unlock more achievements!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-900 font-semibold\",\n                                children: [\n                                    \"+\",\n                                    data.xpGained,\n                                    \" XP Earned!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, undefined);\n            case 'gameComplete':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-8xl animate-spin-slow\",\n                            children: \"⭐\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-yellow-400 mb-2\",\n                                    children: \"Game Complete!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-semibold text-white mb-2\",\n                                    children: data.gameTitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: [\n                                        \"Final Score: \",\n                                        data.score\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-400 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400 font-semibold\",\n                                children: [\n                                    \"+\",\n                                    data.xpGained,\n                                    \" XP Earned!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"absolute inset-0 pointer-events-none overflow-hidden\",\n                children: Array.from({\n                    length: 50\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            animationDelay: `${Math.random() * 3}s`,\n                            animationDuration: `${3 + Math.random() * 2}s`\n                        },\n                        className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"absolute animate-confetti\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: [\n                                    '#FFD700',\n                                    '#FF6B6B',\n                                    '#4ECDC4',\n                                    '#45B7D1',\n                                    '#96CEB4',\n                                    '#FFEAA7'\n                                ][Math.floor(Math.random() * 6)]\n                            },\n                            className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"w-2 h-2 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"bg-gray-800 rounded-lg p-8 max-w-md w-full border border-gray-700 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"absolute top-4 right-4 text-gray-400 hover:text-white text-xl\",\n                        children: \"✕\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    renderContent(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"mt-8 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"jsx-b3e3af61dbd60cf7\" + \" \" + \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg font-bold hover:bg-yellow-300 transition-colors\",\n                            children: \"Continue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b3e3af61dbd60cf7\",\n                children: \"@-webkit-keyframes confetti{0%{-webkit-transform:translatey(-100vh)rotate(0deg);transform:translatey(-100vh)rotate(0deg);opacity:1}100%{-webkit-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg);opacity:0}}@-moz-keyframes confetti{0%{-moz-transform:translatey(-100vh)rotate(0deg);transform:translatey(-100vh)rotate(0deg);opacity:1}100%{-moz-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg);opacity:0}}@-o-keyframes confetti{0%{-o-transform:translatey(-100vh)rotate(0deg);transform:translatey(-100vh)rotate(0deg);opacity:1}100%{-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg);opacity:0}}@keyframes confetti{0%{-webkit-transform:translatey(-100vh)rotate(0deg);-moz-transform:translatey(-100vh)rotate(0deg);-o-transform:translatey(-100vh)rotate(0deg);transform:translatey(-100vh)rotate(0deg);opacity:1}100%{-webkit-transform:translatey(100vh)rotate(720deg);-moz-transform:translatey(100vh)rotate(720deg);-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg);opacity:0}}@-webkit-keyframes spin-slow{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin-slow{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin-slow{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin-slow{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.animate-confetti.jsx-b3e3af61dbd60cf7{-webkit-animation:confetti linear infinite;-moz-animation:confetti linear infinite;-o-animation:confetti linear infinite;animation:confetti linear infinite}.animate-spin-slow.jsx-b3e3af61dbd60cf7{-webkit-animation:spin-slow 2s linear infinite;-moz-animation:spin-slow 2s linear infinite;-o-animation:spin-slow 2s linear infinite;animation:spin-slow 2s linear infinite}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\CelebrationModal.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CelebrationModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CelebrationModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UserDashboard.tsx":
/*!******************************************!*\
  !*** ./src/components/UserDashboard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useProgressStore */ \"(ssr)/./src/stores/useProgressStore.ts\");\n/* harmony import */ var _types_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/progress */ \"(ssr)/./src/types/progress.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst UserDashboard = ({ onClose })=>{\n    const { profile, updateUsername } = (0,_stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore)();\n    const [isEditingUsername, setIsEditingUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newUsername, setNewUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(profile?.username || '');\n    if (!profile) return null;\n    const currentLevel = _types_progress__WEBPACK_IMPORTED_MODULE_3__.LEVEL_SYSTEM.find((l)=>l.level === profile.level);\n    const nextLevel = _types_progress__WEBPACK_IMPORTED_MODULE_3__.LEVEL_SYSTEM.find((l)=>l.level === profile.level + 1);\n    const progressToNext = nextLevel ? (profile.totalXP - currentLevel.xpRequired) / (nextLevel.xpRequired - currentLevel.xpRequired) * 100 : 100;\n    const unlockedAchievements = profile.achievements.filter((a)=>a.unlockedAt);\n    const completedGames = Object.values(profile.gamesProgress).filter((g)=>g.completed).length;\n    const handleUsernameSubmit = ()=>{\n        if (newUsername.trim()) {\n            updateUsername(newUsername.trim());\n            setIsEditingUsername(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-2xl font-bold text-gray-900\",\n                                    children: profile.username.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        isEditingUsername ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newUsername,\n                                                    onChange: (e)=>setNewUsername(e.target.value),\n                                                    className: \"bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-yellow-400 outline-none\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && handleUsernameSubmit(),\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleUsernameSubmit,\n                                                    className: \"text-green-400 hover:text-green-300\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setIsEditingUsername(false);\n                                                        setNewUsername(profile.username);\n                                                    },\n                                                    className: \"text-red-400 hover:text-red-300\",\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: profile.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsEditingUsername(true),\n                                                    className: \"text-gray-400 hover:text-yellow-400\",\n                                                    children: \"✏️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-400 font-medium\",\n                                            children: [\n                                                \"Level \",\n                                                profile.level,\n                                                \" • \",\n                                                currentLevel?.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-white text-2xl\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"Experience Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-yellow-400 font-bold\",\n                                            children: [\n                                                profile.totalXP,\n                                                \" XP\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-600 rounded-full h-4 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-yellow-400 to-orange-500 h-4 rounded-full transition-all duration-500\",\n                                        style: {\n                                            width: `${Math.min(progressToNext, 100)}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Level \",\n                                                profile.level\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        nextLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                nextLevel.xpRequired - profile.totalXP,\n                                                \" XP to Level \",\n                                                nextLevel.level\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-700 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-green-400\",\n                                            children: completedGames\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Games Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-700 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-400\",\n                                            children: profile.statistics.totalGamesPlayed\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Total Games Played\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-700 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-purple-400\",\n                                            children: Math.round(profile.statistics.averageScore || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Average Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-700 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-orange-400\",\n                                            children: unlockedAchievements.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: \"Recent Achievements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined),\n                                unlockedAchievements.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: unlockedAchievements.slice(-6).map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-gray-600 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: achievement.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: achievement.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: achievement.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, achievement.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-400 py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83C\\uDFC6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Complete your first game to earn achievements!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: \"Game Progress\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: Object.entries(profile.gamesProgress).map(([gameId, progress])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between bg-gray-600 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-3 h-3 rounded-full ${progress.completed ? 'bg-green-400' : 'bg-gray-500'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium capitalize\",\n                                                            children: gameId.replace('-', ' ')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400\",\n                                                            children: [\n                                                                \"Best: \",\n                                                                progress.bestScore\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: [\n                                                                \"Attempts: \",\n                                                                progress.totalAttempts\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-2 py-1 rounded text-xs ${progress.difficulty === 'beginner' ? 'bg-green-500 bg-opacity-20 text-green-400' : progress.difficulty === 'intermediate' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'}`,\n                                                            children: progress.difficulty\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, gameId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        profile.statistics.streakDays > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl\",\n                                        children: \"\\uD83D\\uDD25\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Daily Streak\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-orange-100\",\n                                                children: [\n                                                    \"You've played for \",\n                                                    profile.statistics.streakDays,\n                                                    \" consecutive days!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\UserDashboard.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserDashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Vc2VyRGFzaGJvYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV3QztBQUNxQjtBQUNiO0FBTWhELE1BQU1JLGdCQUE4QyxDQUFDLEVBQUVDLE9BQU8sRUFBRTtJQUM5RCxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsY0FBYyxFQUFFLEdBQUdMLDBFQUFnQkE7SUFDcEQsTUFBTSxDQUFDTSxtQkFBbUJDLHFCQUFxQixHQUFHUiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNTLGFBQWFDLGVBQWUsR0FBR1YsK0NBQVFBLENBQUNLLFNBQVNNLFlBQVk7SUFFcEUsSUFBSSxDQUFDTixTQUFTLE9BQU87SUFFckIsTUFBTU8sZUFBZVYseURBQVlBLENBQUNXLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsS0FBSyxLQUFLVixRQUFRVSxLQUFLO0lBQ3JFLE1BQU1DLFlBQVlkLHlEQUFZQSxDQUFDVyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEtBQUssS0FBS1YsUUFBUVUsS0FBSyxHQUFHO0lBQ3JFLE1BQU1FLGlCQUFpQkQsWUFDbkIsQ0FBRVgsUUFBUWEsT0FBTyxHQUFHTixhQUFjTyxVQUFVLElBQUtILENBQUFBLFVBQVVHLFVBQVUsR0FBR1AsYUFBY08sVUFBVSxJQUFLLE1BQ3JHO0lBRUosTUFBTUMsdUJBQXVCZixRQUFRZ0IsWUFBWSxDQUFDQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFVBQVU7SUFDMUUsTUFBTUMsaUJBQWlCQyxPQUFPQyxNQUFNLENBQUN0QixRQUFRdUIsYUFBYSxFQUFFTixNQUFNLENBQUNPLENBQUFBLElBQUtBLEVBQUVDLFNBQVMsRUFBRUMsTUFBTTtJQUUzRixNQUFNQyx1QkFBdUI7UUFDM0IsSUFBSXZCLFlBQVl3QixJQUFJLElBQUk7WUFDdEIzQixlQUFlRyxZQUFZd0IsSUFBSTtZQUMvQnpCLHFCQUFxQjtRQUN2QjtJQUNGO0lBRUEscUJBQ0UsOERBQUMwQjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaOUIsUUFBUU0sUUFBUSxDQUFDeUIsTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs4Q0FFekMsOERBQUNIOzt3Q0FDRTNCLGtDQUNDLDhEQUFDMkI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRztvREFDQ0MsTUFBSztvREFDTEMsT0FBTy9CO29EQUNQZ0MsVUFBVSxDQUFDQyxJQUFNaEMsZUFBZWdDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvREFDOUNMLFdBQVU7b0RBQ1ZTLFlBQVksQ0FBQ0YsSUFBTUEsRUFBRUcsR0FBRyxLQUFLLFdBQVdiO29EQUN4Q2MsU0FBUzs7Ozs7OzhEQUVYLDhEQUFDQztvREFDQ0MsU0FBU2hCO29EQUNURyxXQUFVOzhEQUNYOzs7Ozs7OERBR0QsOERBQUNZO29EQUNDQyxTQUFTO3dEQUNQeEMscUJBQXFCO3dEQUNyQkUsZUFBZUwsUUFBUU0sUUFBUTtvREFDakM7b0RBQ0F3QixXQUFVOzhEQUNYOzs7Ozs7Ozs7OztzRUFLSCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDYztvREFBR2QsV0FBVTs4REFBaUM5QixRQUFRTSxRQUFROzs7Ozs7OERBQy9ELDhEQUFDb0M7b0RBQ0NDLFNBQVMsSUFBTXhDLHFCQUFxQjtvREFDcEMyQixXQUFVOzhEQUNYOzs7Ozs7Ozs7Ozs7c0RBS0wsOERBQUNlOzRDQUFFZixXQUFVOztnREFBOEI7Z0RBQ2xDOUIsUUFBUVUsS0FBSztnREFBQztnREFBSUgsY0FBY3VDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk3Qyw4REFBQ0o7NEJBQ0NDLFNBQVM1Qzs0QkFDVCtCLFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7Ozs4QkFLSCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2lCOzRDQUFHakIsV0FBVTtzREFBbUM7Ozs7OztzREFDakQsOERBQUNEOzRDQUFJQyxXQUFVOztnREFDWjlCLFFBQVFhLE9BQU87Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBR3JCLDhEQUFDZ0I7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUNDQyxXQUFVO3dDQUNWa0IsT0FBTzs0Q0FBRUMsT0FBTyxHQUFHQyxLQUFLQyxHQUFHLENBQUN2QyxnQkFBZ0IsS0FBSyxDQUFDLENBQUM7d0NBQUM7Ozs7Ozs7Ozs7OzhDQUd4RCw4REFBQ2lCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3NCOztnREFBSztnREFBT3BELFFBQVFVLEtBQUs7Ozs7Ozs7d0NBQ3pCQywyQkFDQyw4REFBQ3lDOztnREFDRXpDLFVBQVVHLFVBQVUsR0FBR2QsUUFBUWEsT0FBTztnREFBQztnREFBY0YsVUFBVUQsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPN0UsOERBQUNtQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQXFDVjs7Ozs7O3NEQUNwRCw4REFBQ1M7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7OENBRXpDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFvQzlCLFFBQVFxRCxVQUFVLENBQUNDLGdCQUFnQjs7Ozs7O3NEQUN0Riw4REFBQ3pCOzRDQUFJQyxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUV6Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWm9CLEtBQUtLLEtBQUssQ0FBQ3ZELFFBQVFxRCxVQUFVLENBQUNHLFlBQVksSUFBSTs7Ozs7O3NEQUVqRCw4REFBQzNCOzRDQUFJQyxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUV6Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBc0NmLHFCQUFxQlcsTUFBTTs7Ozs7O3NEQUNoRiw4REFBQ0c7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzNDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNpQjtvQ0FBR2pCLFdBQVU7OENBQXdDOzs7Ozs7Z0NBQ3JEZixxQkFBcUJXLE1BQU0sR0FBRyxrQkFDN0IsOERBQUNHO29DQUFJQyxXQUFVOzhDQUNaZixxQkFBcUIwQyxLQUFLLENBQUMsQ0FBQyxHQUFHQyxHQUFHLENBQUMsQ0FBQ0MsNEJBQ25DLDhEQUFDOUI7NENBRUNDLFdBQVU7OzhEQUVWLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBWTZCLFlBQVlDLElBQUk7Ozs7Ozs4REFDM0MsOERBQUMvQjs7c0VBQ0MsOERBQUNBOzREQUFJQyxXQUFVO3NFQUEwQjZCLFlBQVlFLElBQUk7Ozs7OztzRUFDekQsOERBQUNoQzs0REFBSUMsV0FBVTtzRUFBeUI2QixZQUFZRyxXQUFXOzs7Ozs7Ozs7Ozs7OzJDQU41REgsWUFBWUksRUFBRTs7Ozs7Ozs7OzhEQVl6Qiw4REFBQ2xDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQWdCOzs7Ozs7c0RBQy9CLDhEQUFDZTtzREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1ULDhEQUFDaEI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDaUI7b0NBQUdqQixXQUFVOzhDQUF3Qzs7Ozs7OzhDQUN0RCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1pULE9BQU8yQyxPQUFPLENBQUNoRSxRQUFRdUIsYUFBYSxFQUFFbUMsR0FBRyxDQUFDLENBQUMsQ0FBQ08sUUFBUUMsU0FBUyxpQkFDNUQsOERBQUNyQzs0Q0FBaUJDLFdBQVU7OzhEQUMxQiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVyxDQUFDLHFCQUFxQixFQUFFb0MsU0FBU3pDLFNBQVMsR0FBRyxpQkFBaUIsZUFBZTs7Ozs7O3NFQUM3Riw4REFBQzJCOzREQUFLdEIsV0FBVTtzRUFBcUNtQyxPQUFPRSxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7OzhEQUUzRSw4REFBQ3RDO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3NCOzREQUFLdEIsV0FBVTs7Z0VBQWtCO2dFQUFPb0MsU0FBU0UsU0FBUzs7Ozs7OztzRUFDM0QsOERBQUNoQjs0REFBS3RCLFdBQVU7O2dFQUFnQjtnRUFBV29DLFNBQVNHLGFBQWE7Ozs7Ozs7c0VBQ2pFLDhEQUFDakI7NERBQUt0QixXQUFXLENBQUMsMEJBQTBCLEVBQzFDb0MsU0FBU0ksVUFBVSxLQUFLLGFBQWEsOENBQ3JDSixTQUFTSSxVQUFVLEtBQUssaUJBQWlCLGdEQUN6Qyx5Q0FDQTtzRUFDQ0osU0FBU0ksVUFBVTs7Ozs7Ozs7Ozs7OzsyQ0FiaEJMOzs7Ozs7Ozs7Ozs7Ozs7O3dCQXNCZmpFLFFBQVFxRCxVQUFVLENBQUNrQixVQUFVLEdBQUcsbUJBQy9CLDhEQUFDMUM7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQVc7Ozs7OztrREFDMUIsOERBQUNEOzswREFDQyw4REFBQ2tCO2dEQUFHakIsV0FBVTswREFBd0I7Ozs7OzswREFDdEMsOERBQUNlO2dEQUFFZixXQUFVOztvREFBa0I7b0RBQ1Y5QixRQUFRcUQsVUFBVSxDQUFDa0IsVUFBVTtvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVckU7QUFFQSxpRUFBZXpFLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFxjb21wb25lbnRzXFxVc2VyRGFzaGJvYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVByb2dyZXNzU3RvcmUgfSBmcm9tICdAL3N0b3Jlcy91c2VQcm9ncmVzc1N0b3JlJztcbmltcG9ydCB7IExFVkVMX1NZU1RFTSB9IGZyb20gJ0AvdHlwZXMvcHJvZ3Jlc3MnO1xuXG5pbnRlcmZhY2UgVXNlckRhc2hib2FyZFByb3BzIHtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgVXNlckRhc2hib2FyZDogUmVhY3QuRkM8VXNlckRhc2hib2FyZFByb3BzPiA9ICh7IG9uQ2xvc2UgfSkgPT4ge1xuICBjb25zdCB7IHByb2ZpbGUsIHVwZGF0ZVVzZXJuYW1lIH0gPSB1c2VQcm9ncmVzc1N0b3JlKCk7XG4gIGNvbnN0IFtpc0VkaXRpbmdVc2VybmFtZSwgc2V0SXNFZGl0aW5nVXNlcm5hbWVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbmV3VXNlcm5hbWUsIHNldE5ld1VzZXJuYW1lXSA9IHVzZVN0YXRlKHByb2ZpbGU/LnVzZXJuYW1lIHx8ICcnKTtcblxuICBpZiAoIXByb2ZpbGUpIHJldHVybiBudWxsO1xuXG4gIGNvbnN0IGN1cnJlbnRMZXZlbCA9IExFVkVMX1NZU1RFTS5maW5kKGwgPT4gbC5sZXZlbCA9PT0gcHJvZmlsZS5sZXZlbCk7XG4gIGNvbnN0IG5leHRMZXZlbCA9IExFVkVMX1NZU1RFTS5maW5kKGwgPT4gbC5sZXZlbCA9PT0gcHJvZmlsZS5sZXZlbCArIDEpO1xuICBjb25zdCBwcm9ncmVzc1RvTmV4dCA9IG5leHRMZXZlbCBcbiAgICA/ICgocHJvZmlsZS50b3RhbFhQIC0gY3VycmVudExldmVsIS54cFJlcXVpcmVkKSAvIChuZXh0TGV2ZWwueHBSZXF1aXJlZCAtIGN1cnJlbnRMZXZlbCEueHBSZXF1aXJlZCkpICogMTAwXG4gICAgOiAxMDA7XG5cbiAgY29uc3QgdW5sb2NrZWRBY2hpZXZlbWVudHMgPSBwcm9maWxlLmFjaGlldmVtZW50cy5maWx0ZXIoYSA9PiBhLnVubG9ja2VkQXQpO1xuICBjb25zdCBjb21wbGV0ZWRHYW1lcyA9IE9iamVjdC52YWx1ZXMocHJvZmlsZS5nYW1lc1Byb2dyZXNzKS5maWx0ZXIoZyA9PiBnLmNvbXBsZXRlZCkubGVuZ3RoO1xuXG4gIGNvbnN0IGhhbmRsZVVzZXJuYW1lU3VibWl0ID0gKCkgPT4ge1xuICAgIGlmIChuZXdVc2VybmFtZS50cmltKCkpIHtcbiAgICAgIHVwZGF0ZVVzZXJuYW1lKG5ld1VzZXJuYW1lLnRyaW0oKSk7XG4gICAgICBzZXRJc0VkaXRpbmdVc2VybmFtZShmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00IGJnLWJsYWNrIGJnLW9wYWNpdHktNzVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcm91bmRlZC1sZyBtYXgtdy00eGwgdy1mdWxsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG8gYm9yZGVyIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLWJyIGZyb20teWVsbG93LTQwMCB0by1vcmFuZ2UtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICB7cHJvZmlsZS51c2VybmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAge2lzRWRpdGluZ1VzZXJuYW1lID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3VXNlcm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VXNlcm5hbWUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkIGJvcmRlciBib3JkZXItZ3JheS02MDAgZm9jdXM6Ym9yZGVyLXllbGxvdy00MDAgb3V0bGluZS1ub25lXCJcbiAgICAgICAgICAgICAgICAgICAgb25LZXlQcmVzcz17KGUpID0+IGUua2V5ID09PSAnRW50ZXInICYmIGhhbmRsZVVzZXJuYW1lU3VibWl0KCl9XG4gICAgICAgICAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVXNlcm5hbWVTdWJtaXR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIGhvdmVyOnRleHQtZ3JlZW4tMzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg4pyTXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIHNldElzRWRpdGluZ1VzZXJuYW1lKGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgICAgICBzZXROZXdVc2VybmFtZShwcm9maWxlLnVzZXJuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTMwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOKcl1xuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPntwcm9maWxlLnVzZXJuYW1lfTwvaDI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzRWRpdGluZ1VzZXJuYW1lKHRydWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQteWVsbG93LTQwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOKcj++4j1xuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgIExldmVsIHtwcm9maWxlLmxldmVsfSDigKIge2N1cnJlbnRMZXZlbD8udGl0bGV9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdGV4dC0yeGxcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIOKclVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBzcGFjZS15LThcIj5cbiAgICAgICAgICB7LyogTGV2ZWwgUHJvZ3Jlc3MgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5FeHBlcmllbmNlIFByb2dyZXNzPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAge3Byb2ZpbGUudG90YWxYUH0gWFBcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNjAwIHJvdW5kZWQtZnVsbCBoLTQgbWItMlwiPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTUwMCBoLTQgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKHByb2dyZXNzVG9OZXh0LCAxMDApfSVgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuPkxldmVsIHtwcm9maWxlLmxldmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAge25leHRMZXZlbCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgICAgICB7bmV4dExldmVsLnhwUmVxdWlyZWQgLSBwcm9maWxlLnRvdGFsWFB9IFhQIHRvIExldmVsIHtuZXh0TGV2ZWwubGV2ZWx9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhdGlzdGljcyBHcmlkICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTQwMFwiPntjb21wbGV0ZWRHYW1lc308L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5HYW1lcyBDb21wbGV0ZWQ8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+e3Byb2ZpbGUuc3RhdGlzdGljcy50b3RhbEdhbWVzUGxheWVkfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlRvdGFsIEdhbWVzIFBsYXllZDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIHJvdW5kZWQtbGcgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTQwMFwiPlxuICAgICAgICAgICAgICAgIHtNYXRoLnJvdW5kKHByb2ZpbGUuc3RhdGlzdGljcy5hdmVyYWdlU2NvcmUgfHwgMCl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkF2ZXJhZ2UgU2NvcmU8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LW9yYW5nZS00MDBcIj57dW5sb2NrZWRBY2hpZXZlbWVudHMubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkFjaGlldmVtZW50czwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmVjZW50IEFjaGlldmVtZW50cyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPlJlY2VudCBBY2hpZXZlbWVudHM8L2gzPlxuICAgICAgICAgICAge3VubG9ja2VkQWNoaWV2ZW1lbnRzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHt1bmxvY2tlZEFjaGlldmVtZW50cy5zbGljZSgtNikubWFwKChhY2hpZXZlbWVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e2FjaGlldmVtZW50LmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgYmctZ3JheS02MDAgcm91bmRlZC1sZyBwLTNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e2FjaGlldmVtZW50Lmljb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e2FjaGlldmVtZW50Lm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj57YWNoaWV2ZW1lbnQuZGVzY3JpcHRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNDAwIHB5LThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1iLTJcIj7wn4+GPC9kaXY+XG4gICAgICAgICAgICAgICAgPHA+Q29tcGxldGUgeW91ciBmaXJzdCBnYW1lIHRvIGVhcm4gYWNoaWV2ZW1lbnRzITwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEdhbWUgUHJvZ3Jlc3MgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5HYW1lIFByb2dyZXNzPC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhwcm9maWxlLmdhbWVzUHJvZ3Jlc3MpLm1hcCgoW2dhbWVJZCwgcHJvZ3Jlc3NdKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2dhbWVJZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGJnLWdyYXktNjAwIHJvdW5kZWQtbGcgcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7cHJvZ3Jlc3MuY29tcGxldGVkID8gJ2JnLWdyZWVuLTQwMCcgOiAnYmctZ3JheS01MDAnfWB9IC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW0gY2FwaXRhbGl6ZVwiPntnYW1lSWQucmVwbGFjZSgnLScsICcgJyl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMFwiPkJlc3Q6IHtwcm9ncmVzcy5iZXN0U2NvcmV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+QXR0ZW1wdHM6IHtwcm9ncmVzcy50b3RhbEF0dGVtcHRzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyAke1xuICAgICAgICAgICAgICAgICAgICAgIHByb2dyZXNzLmRpZmZpY3VsdHkgPT09ICdiZWdpbm5lcicgPyAnYmctZ3JlZW4tNTAwIGJnLW9wYWNpdHktMjAgdGV4dC1ncmVlbi00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICBwcm9ncmVzcy5kaWZmaWN1bHR5ID09PSAnaW50ZXJtZWRpYXRlJyA/ICdiZy15ZWxsb3ctNTAwIGJnLW9wYWNpdHktMjAgdGV4dC15ZWxsb3ctNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgJ2JnLXJlZC01MDAgYmctb3BhY2l0eS0yMCB0ZXh0LXJlZC00MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICB7cHJvZ3Jlc3MuZGlmZmljdWx0eX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RyZWFrIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgIHtwcm9maWxlLnN0YXRpc3RpY3Muc3RyZWFrRGF5cyA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tb3JhbmdlLTUwMCB0by1yZWQtNTAwIHJvdW5kZWQtbGcgcC02IHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsXCI+8J+UpTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkXCI+RGFpbHkgU3RyZWFrPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICBZb3UndmUgcGxheWVkIGZvciB7cHJvZmlsZS5zdGF0aXN0aWNzLnN0cmVha0RheXN9IGNvbnNlY3V0aXZlIGRheXMhXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFVzZXJEYXNoYm9hcmQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVByb2dyZXNzU3RvcmUiLCJMRVZFTF9TWVNURU0iLCJVc2VyRGFzaGJvYXJkIiwib25DbG9zZSIsInByb2ZpbGUiLCJ1cGRhdGVVc2VybmFtZSIsImlzRWRpdGluZ1VzZXJuYW1lIiwic2V0SXNFZGl0aW5nVXNlcm5hbWUiLCJuZXdVc2VybmFtZSIsInNldE5ld1VzZXJuYW1lIiwidXNlcm5hbWUiLCJjdXJyZW50TGV2ZWwiLCJmaW5kIiwibCIsImxldmVsIiwibmV4dExldmVsIiwicHJvZ3Jlc3NUb05leHQiLCJ0b3RhbFhQIiwieHBSZXF1aXJlZCIsInVubG9ja2VkQWNoaWV2ZW1lbnRzIiwiYWNoaWV2ZW1lbnRzIiwiZmlsdGVyIiwiYSIsInVubG9ja2VkQXQiLCJjb21wbGV0ZWRHYW1lcyIsIk9iamVjdCIsInZhbHVlcyIsImdhbWVzUHJvZ3Jlc3MiLCJnIiwiY29tcGxldGVkIiwibGVuZ3RoIiwiaGFuZGxlVXNlcm5hbWVTdWJtaXQiLCJ0cmltIiwiZGl2IiwiY2xhc3NOYW1lIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9uS2V5UHJlc3MiLCJrZXkiLCJhdXRvRm9jdXMiLCJidXR0b24iLCJvbkNsaWNrIiwiaDIiLCJwIiwidGl0bGUiLCJoMyIsInN0eWxlIiwid2lkdGgiLCJNYXRoIiwibWluIiwic3BhbiIsInN0YXRpc3RpY3MiLCJ0b3RhbEdhbWVzUGxheWVkIiwicm91bmQiLCJhdmVyYWdlU2NvcmUiLCJzbGljZSIsIm1hcCIsImFjaGlldmVtZW50IiwiaWNvbiIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImlkIiwiZW50cmllcyIsImdhbWVJZCIsInByb2dyZXNzIiwicmVwbGFjZSIsImJlc3RTY29yZSIsInRvdGFsQXR0ZW1wdHMiLCJkaWZmaWN1bHR5Iiwic3RyZWFrRGF5cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UserDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LazyGameLoader.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LazyGameLoader.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSkeleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSkeleton */ \"(ssr)/./src/components/ui/LoadingSkeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Lazy load all game components\nconst QuizGame = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_QuizGame_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/QuizGame */ \"(ssr)/./src/components/games/QuizGame.tsx\")));\nconst MatchingGame = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_MatchingGame_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/MatchingGame */ \"(ssr)/./src/components/games/MatchingGame.tsx\")));\nconst JigsawPuzzle = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_JigsawPuzzle_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/JigsawPuzzle */ \"(ssr)/./src/components/games/JigsawPuzzle.tsx\")));\nconst MemoryGrid = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_MemoryGrid_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/MemoryGrid */ \"(ssr)/./src/components/games/MemoryGrid.tsx\")));\nconst SpeedChallenge = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_SpeedChallenge_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/SpeedChallenge */ \"(ssr)/./src/components/games/SpeedChallenge.tsx\")));\nconst CountryExplorer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_CountryExplorer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/CountryExplorer */ \"(ssr)/./src/components/games/CountryExplorer.tsx\")));\nconst MysteryLand = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_MysteryLand_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/MysteryLand */ \"(ssr)/./src/components/games/MysteryLand.tsx\")));\nconst TimelineBuilder = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_TimelineBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/TimelineBuilder */ \"(ssr)/./src/components/games/TimelineBuilder.tsx\")));\nconst WhereInAfrica = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_WhereInAfrica_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/WhereInAfrica */ \"(ssr)/./src/components/games/WhereInAfrica.tsx\")));\nconst DressTheCharacter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_DressTheCharacter_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/DressTheCharacter */ \"(ssr)/./src/components/games/DressTheCharacter.tsx\")));\nconst CountryNameScramble = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_CountryNameScramble_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/CountryNameScramble */ \"(ssr)/./src/components/games/CountryNameScramble.tsx\")));\nconst FlagMatching = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_ssr_src_components_games_FlagMatching_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/games/FlagMatching */ \"(ssr)/./src/components/games/FlagMatching.tsx\")));\nconst LazyGameLoader = ({ gameType, gameProps })=>{\n    const renderGame = ()=>{\n        switch(gameType){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuizGame, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, undefined);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MatchingGame, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, undefined);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(JigsawPuzzle, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, undefined);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MemoryGrid, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, undefined);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SpeedChallenge, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, undefined);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CountryExplorer, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, undefined);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MysteryLand, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, undefined);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimelineBuilder, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, undefined);\n            case 'where-in-africa':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhereInAfrica, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, undefined);\n            case 'dress-character':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DressTheCharacter, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, undefined);\n            case 'country-name-scramble':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CountryNameScramble, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, undefined);\n            case 'flag-matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlagMatching, {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Game not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                type: \"game-content\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n                lineNumber: 62,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n            lineNumber: 61,\n            columnNumber: 9\n        }, void 0),\n        children: renderGame()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LazyGameLoader.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LazyGameLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LazyGameLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSkeleton.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/LoadingSkeleton.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst LoadingSkeleton = ({ type = 'game-card', count = 1, className = '' })=>{\n    const renderSkeleton = ()=>{\n        switch(type){\n            case 'game-card':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse ${className}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-700 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-600 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-700 rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-700 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-600 rounded w-1/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-700 rounded w-20\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, undefined);\n            case 'flag':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-12 h-9 bg-gray-700 rounded-sm animate-pulse ${className}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, undefined);\n            case 'country-list':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `space-y-3 ${className}`,\n                    children: Array.from({\n                        length: 5\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 p-3 bg-gray-800 rounded-lg animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-6 bg-gray-700 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-700 rounded w-3/4 mb-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-600 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, undefined);\n            case 'game-content':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `space-y-6 ${className}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-gray-700 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-gray-700 rounded w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-4\",\n                                    children: Array.from({\n                                        length: 5\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 bg-gray-700 rounded w-full mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-3 bg-gray-600 rounded w-3/4 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 bg-gray-700 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-3\",\n                                            children: Array.from({\n                                                length: 9\n                                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-700 rounded-lg\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 bg-gray-700 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Array.from({\n                                                length: 6\n                                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 bg-gray-700 rounded-lg\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `h-4 bg-gray-700 rounded animate-pulse ${className}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: Array.from({\n            length: count\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: renderSkeleton()\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\LoadingSkeleton.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSkeleton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSkeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/progressService.ts":
/*!*****************************************!*\
  !*** ./src/services/progressService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progressService: () => (/* binding */ progressService)\n/* harmony export */ });\n/* harmony import */ var _types_progress__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/progress */ \"(ssr)/./src/types/progress.ts\");\n\nconst STORAGE_KEY = 'games_for_africa_profile';\nclass ProgressService {\n    constructor(){\n        this.profile = null;\n        // Only load profile on client side\n        if (false) {} else {\n            this.profile = this.createDefaultProfile();\n        }\n    }\n    generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    createDefaultProfile() {\n        return {\n            id: this.generateId(),\n            username: 'Explorer',\n            createdAt: new Date().toISOString(),\n            lastActive: new Date().toISOString(),\n            totalXP: 0,\n            level: 1,\n            gamesProgress: {},\n            achievements: _types_progress__WEBPACK_IMPORTED_MODULE_0__.ACHIEVEMENTS_CATALOG.map((achievement)=>({\n                    ...achievement,\n                    progress: 0\n                })),\n            preferences: {\n                theme: 'dark',\n                difficulty: 'intermediate',\n                soundEnabled: true,\n                hintsEnabled: true,\n                accessibilityMode: false\n            },\n            statistics: {\n                totalGamesPlayed: 0,\n                totalGamesCompleted: 0,\n                averageScore: 0,\n                totalPlayTime: 0,\n                favoriteGames: [],\n                streakDays: 0,\n                lastStreakDate: ''\n            }\n        };\n    }\n    loadProfile() {\n        // Check if we're in a browser environment\n        if (true) {\n            // Return default profile for SSR\n            this.profile = this.createDefaultProfile();\n            return this.profile;\n        }\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                this.profile = JSON.parse(stored);\n                // Ensure all new achievements are present\n                this.migrateAchievements();\n            } else {\n                this.profile = this.createDefaultProfile();\n                this.saveProfile();\n            }\n        } catch (error) {\n            console.error('Error loading profile:', error);\n            this.profile = this.createDefaultProfile();\n        }\n        return this.profile;\n    }\n    migrateAchievements() {\n        if (!this.profile) return;\n        const existingIds = new Set(this.profile.achievements.map((a)=>a.id));\n        const newAchievements = _types_progress__WEBPACK_IMPORTED_MODULE_0__.ACHIEVEMENTS_CATALOG.filter((a)=>!existingIds.has(a.id));\n        newAchievements.forEach((achievement)=>{\n            this.profile.achievements.push({\n                ...achievement,\n                progress: 0\n            });\n        });\n    }\n    saveProfile() {\n        if (!this.profile) return;\n        // Check if we're in a browser environment\n        if (true) {\n            return; // Skip saving during SSR\n        }\n        this.profile.lastActive = new Date().toISOString();\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(this.profile));\n        } catch (error) {\n            console.error('Error saving profile:', error);\n        }\n    }\n    getProfile() {\n        return this.profile || this.loadProfile();\n    }\n    updateUsername(username) {\n        if (this.profile) {\n            this.profile.username = username;\n            this.saveProfile();\n        }\n    }\n    recordGameCompletion(gameId, score, completionTime, difficulty, isPerfectScore = false) {\n        if (!this.profile) this.loadProfile();\n        const existingProgress = this.profile.gamesProgress[gameId];\n        const isFirstCompletion = !existingProgress?.completed;\n        const isNewBestScore = !existingProgress || score > existingProgress.bestScore;\n        // Update game progress\n        this.profile.gamesProgress[gameId] = {\n            gameId,\n            completed: true,\n            bestScore: Math.max(score, existingProgress?.bestScore || 0),\n            totalAttempts: (existingProgress?.totalAttempts || 0) + 1,\n            completionTime: isNewBestScore ? completionTime : existingProgress?.completionTime,\n            difficulty,\n            lastPlayed: new Date().toISOString(),\n            perfectScore: isPerfectScore || existingProgress?.perfectScore || false\n        };\n        // Update statistics\n        this.profile.statistics.totalGamesPlayed++;\n        if (isFirstCompletion) {\n            this.profile.statistics.totalGamesCompleted++;\n        }\n        // Calculate XP gained\n        let xpGained = 0;\n        if (isFirstCompletion) {\n            xpGained += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.GAME_COMPLETION;\n        }\n        if (isPerfectScore) {\n            xpGained += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.PERFECT_SCORE;\n        }\n        if (completionTime < 120) {\n            xpGained += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.SPEED_BONUS;\n        }\n        xpGained += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.DIFFICULTY_BONUS[difficulty];\n        const oldLevel = this.profile.level;\n        this.profile.totalXP += xpGained;\n        // Check for level up\n        const newLevel = this.calculateLevel(this.profile.totalXP);\n        const levelUp = newLevel > oldLevel;\n        this.profile.level = newLevel;\n        // Check for new achievements\n        const newAchievements = this.checkAchievements(gameId, score, completionTime, difficulty, isPerfectScore);\n        // Update average score\n        const allScores = Object.values(this.profile.gamesProgress).map((p)=>p.bestScore);\n        this.profile.statistics.averageScore = allScores.reduce((a, b)=>a + b, 0) / allScores.length;\n        // Update streak\n        this.updateStreak();\n        this.saveProfile();\n        return {\n            xpGained,\n            levelUp,\n            newAchievements\n        };\n    }\n    calculateLevel(totalXP) {\n        for(let i = _types_progress__WEBPACK_IMPORTED_MODULE_0__.LEVEL_SYSTEM.length - 1; i >= 0; i--){\n            if (totalXP >= _types_progress__WEBPACK_IMPORTED_MODULE_0__.LEVEL_SYSTEM[i].xpRequired) {\n                return _types_progress__WEBPACK_IMPORTED_MODULE_0__.LEVEL_SYSTEM[i].level;\n            }\n        }\n        return 1;\n    }\n    checkAchievements(gameId, score, completionTime, difficulty, isPerfectScore) {\n        if (!this.profile) return [];\n        const newAchievements = [];\n        const now = new Date().toISOString();\n        this.profile.achievements.forEach((achievement)=>{\n            if (achievement.unlockedAt) return; // Already unlocked\n            let shouldUnlock = false;\n            let progressIncrement = 0;\n            switch(achievement.id){\n                case 'first_game':\n                    shouldUnlock = true;\n                    break;\n                case 'perfect_score':\n                    shouldUnlock = isPerfectScore;\n                    break;\n                case 'speed_demon':\n                    shouldUnlock = completionTime < 120;\n                    break;\n                case 'all_games_completed':\n                    progressIncrement = 1;\n                    shouldUnlock = this.profile.statistics.totalGamesCompleted >= 10;\n                    break;\n                case 'advanced_difficulty':\n                    shouldUnlock = difficulty === 'advanced';\n                    break;\n                case 'cultural_scholar':\n                    progressIncrement = 1; // Simplified - would need more complex tracking\n                    shouldUnlock = (achievement.progress || 0) + progressIncrement >= 25;\n                    break;\n            }\n            if (progressIncrement > 0) {\n                achievement.progress = (achievement.progress || 0) + progressIncrement;\n            }\n            if (shouldUnlock && !achievement.unlockedAt) {\n                achievement.unlockedAt = now;\n                newAchievements.push(achievement);\n                this.profile.totalXP += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.ACHIEVEMENT_UNLOCK;\n            }\n        });\n        return newAchievements;\n    }\n    updateStreak() {\n        if (!this.profile) return;\n        const today = new Date().toDateString();\n        const lastStreakDate = this.profile.statistics.lastStreakDate;\n        if (lastStreakDate !== today) {\n            const yesterday = new Date();\n            yesterday.setDate(yesterday.getDate() - 1);\n            if (lastStreakDate === yesterday.toDateString()) {\n                // Continue streak\n                this.profile.statistics.streakDays++;\n            } else if (lastStreakDate !== today) {\n                // Start new streak\n                this.profile.statistics.streakDays = 1;\n            }\n            this.profile.statistics.lastStreakDate = today;\n            this.profile.totalXP += _types_progress__WEBPACK_IMPORTED_MODULE_0__.XP_REWARDS.DAILY_STREAK;\n        }\n    }\n    getGameProgress(gameId) {\n        return this.profile?.gamesProgress[gameId] || null;\n    }\n    updatePreferences(preferences) {\n        if (this.profile) {\n            this.profile.preferences = {\n                ...this.profile.preferences,\n                ...preferences\n            };\n            this.saveProfile();\n        }\n    }\n    addFavoriteGame(gameId) {\n        if (this.profile && !this.profile.statistics.favoriteGames.includes(gameId)) {\n            this.profile.statistics.favoriteGames.push(gameId);\n            this.saveProfile();\n        }\n    }\n    removeFavoriteGame(gameId) {\n        if (this.profile) {\n            this.profile.statistics.favoriteGames = this.profile.statistics.favoriteGames.filter((id)=>id !== gameId);\n            this.saveProfile();\n        }\n    }\n    resetProgress() {\n        this.profile = this.createDefaultProfile();\n        this.saveProfile();\n    }\n    exportProgress() {\n        return JSON.stringify(this.profile, null, 2);\n    }\n    importProgress(data) {\n        try {\n            const imported = JSON.parse(data);\n            // Validate the structure\n            if (imported.id && imported.username && imported.gamesProgress) {\n                this.profile = imported;\n                this.migrateAchievements();\n                this.saveProfile();\n                return true;\n            }\n        } catch (error) {\n            console.error('Error importing progress:', error);\n        }\n        return false;\n    }\n}\nconst progressService = new ProgressService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/progressService.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/useProgressStore.ts":
/*!****************************************!*\
  !*** ./src/stores/useProgressStore.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProgressStore: () => (/* binding */ useProgressStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _services_progressService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/progressService */ \"(ssr)/./src/services/progressService.ts\");\n\n\nconst useProgressStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        profile: null,\n        isLoading: true,\n        loadProfile: ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n                set({\n                    profile,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error('Error loading profile:', error);\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        updateUsername: (username)=>{\n            _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.updateUsername(username);\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n        },\n        recordGameCompletion: async (gameId, score, completionTime, difficulty, isPerfectScore = false)=>{\n            const result = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.recordGameCompletion(gameId, score, completionTime, difficulty, isPerfectScore);\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n            return result;\n        },\n        updatePreferences: (preferences)=>{\n            _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.updatePreferences(preferences);\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n        },\n        addFavoriteGame: (gameId)=>{\n            _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.addFavoriteGame(gameId);\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n        },\n        removeFavoriteGame: (gameId)=>{\n            _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.removeFavoriteGame(gameId);\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n        },\n        resetProgress: ()=>{\n            _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.resetProgress();\n            const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n            set({\n                profile\n            });\n        },\n        exportProgress: ()=>{\n            return _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.exportProgress();\n        },\n        importProgress: (data)=>{\n            const success = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.importProgress(data);\n            if (success) {\n                const profile = _services_progressService__WEBPACK_IMPORTED_MODULE_0__.progressService.getProfile();\n                set({\n                    profile\n                });\n            }\n            return success;\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/useProgressStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/progress.ts":
/*!*******************************!*\
  !*** ./src/types/progress.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACHIEVEMENTS_CATALOG: () => (/* binding */ ACHIEVEMENTS_CATALOG),\n/* harmony export */   LEVEL_SYSTEM: () => (/* binding */ LEVEL_SYSTEM),\n/* harmony export */   XP_REWARDS: () => (/* binding */ XP_REWARDS)\n/* harmony export */ });\nconst LEVEL_SYSTEM = [\n    {\n        level: 1,\n        xpRequired: 0,\n        title: \"Explorer\",\n        rewards: [\n            \"Welcome Badge\"\n        ]\n    },\n    {\n        level: 2,\n        xpRequired: 100,\n        title: \"Adventurer\",\n        rewards: [\n            \"First Steps Badge\"\n        ]\n    },\n    {\n        level: 3,\n        xpRequired: 250,\n        title: \"Traveler\",\n        rewards: [\n            \"Country Expert Badge\"\n        ]\n    },\n    {\n        level: 4,\n        xpRequired: 500,\n        title: \"Navigator\",\n        rewards: [\n            \"Regional Master Badge\"\n        ]\n    },\n    {\n        level: 5,\n        xpRequired: 1000,\n        title: \"Geographer\",\n        rewards: [\n            \"Cultural Scholar Badge\"\n        ]\n    },\n    {\n        level: 6,\n        xpRequired: 1750,\n        title: \"Cartographer\",\n        rewards: [\n            \"Speed Demon Badge\"\n        ]\n    },\n    {\n        level: 7,\n        xpRequired: 2750,\n        title: \"Scholar\",\n        rewards: [\n            \"Perfect Score Badge\"\n        ]\n    },\n    {\n        level: 8,\n        xpRequired: 4000,\n        title: \"Expert\",\n        rewards: [\n            \"Difficulty Master Badge\"\n        ]\n    },\n    {\n        level: 9,\n        xpRequired: 6000,\n        title: \"Master\",\n        rewards: [\n            \"Achievement Hunter Badge\"\n        ]\n    },\n    {\n        level: 10,\n        xpRequired: 10000,\n        title: \"Legend\",\n        rewards: [\n            \"Africa Expert Badge\"\n        ]\n    }\n];\nconst ACHIEVEMENTS_CATALOG = [\n    {\n        id: 'first_game',\n        name: 'First Steps',\n        description: 'Complete your first game',\n        icon: '🎯',\n        category: 'completion',\n        maxProgress: 1\n    },\n    {\n        id: 'perfect_score',\n        name: 'Perfectionist',\n        description: 'Achieve a perfect score in any game',\n        icon: '⭐',\n        category: 'score',\n        maxProgress: 1\n    },\n    {\n        id: 'speed_demon',\n        name: 'Speed Demon',\n        description: 'Complete a game in under 2 minutes',\n        icon: '⚡',\n        category: 'speed',\n        maxProgress: 1\n    },\n    {\n        id: 'all_games_completed',\n        name: 'Game Master',\n        description: 'Complete all 10 games',\n        icon: '🏆',\n        category: 'completion',\n        maxProgress: 10\n    },\n    {\n        id: 'advanced_difficulty',\n        name: 'Challenge Accepted',\n        description: 'Complete a game on Advanced difficulty',\n        icon: '🔥',\n        category: 'difficulty',\n        maxProgress: 1\n    },\n    {\n        id: 'north_africa_expert',\n        name: 'North Africa Expert',\n        description: 'Master all North African countries',\n        icon: '🏺',\n        category: 'regional',\n        maxProgress: 6\n    },\n    {\n        id: 'west_africa_expert',\n        name: 'West Africa Expert',\n        description: 'Master all West African countries',\n        icon: '🥁',\n        category: 'regional',\n        maxProgress: 16\n    },\n    {\n        id: 'east_africa_expert',\n        name: 'East Africa Expert',\n        description: 'Master all East African countries',\n        icon: '🦁',\n        category: 'regional',\n        maxProgress: 10\n    },\n    {\n        id: 'central_africa_expert',\n        name: 'Central Africa Expert',\n        description: 'Master all Central African countries',\n        icon: '🌳',\n        category: 'regional',\n        maxProgress: 8\n    },\n    {\n        id: 'southern_africa_expert',\n        name: 'Southern Africa Expert',\n        description: 'Master all Southern African countries',\n        icon: '💎',\n        category: 'regional',\n        maxProgress: 10\n    },\n    {\n        id: 'cultural_scholar',\n        name: 'Cultural Scholar',\n        description: 'Learn about 25 different cultural elements',\n        icon: '📚',\n        category: 'cultural',\n        maxProgress: 25\n    },\n    {\n        id: 'streak_week',\n        name: 'Dedicated Learner',\n        description: 'Play games for 7 consecutive days',\n        icon: '🔥',\n        category: 'completion',\n        maxProgress: 7\n    }\n];\nconst XP_REWARDS = {\n    GAME_COMPLETION: 50,\n    PERFECT_SCORE: 25,\n    FIRST_ATTEMPT_SUCCESS: 15,\n    SPEED_BONUS: 10,\n    DIFFICULTY_BONUS: {\n        beginner: 0,\n        intermediate: 10,\n        advanced: 25\n    },\n    ACHIEVEMENT_UNLOCK: 100,\n    DAILY_STREAK: 20\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/progress.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/dataLoader.ts":
/*!*********************************!*\
  !*** ./src/utils/dataLoader.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   getCountriesByRegion: () => (/* binding */ getCountriesByRegion),\n/* harmony export */   getCountryById: () => (/* binding */ getCountryById),\n/* harmony export */   initializeDataLoading: () => (/* binding */ initializeDataLoading),\n/* harmony export */   loadCountriesData: () => (/* binding */ loadCountriesData),\n/* harmony export */   preloadFlagImage: () => (/* binding */ preloadFlagImage),\n/* harmony export */   preloadFlagImages: () => (/* binding */ preloadFlagImages)\n/* harmony export */ });\n// Optimized data loading with caching\n// In-memory cache for countries data\nlet countriesCache = null;\nlet cacheTimestamp = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n// Preload countries data with caching\nconst loadCountriesData = async ()=>{\n    // Check if we have valid cached data\n    const now = Date.now();\n    if (countriesCache && now - cacheTimestamp < CACHE_DURATION) {\n        return countriesCache;\n    }\n    try {\n        // Use dynamic import for better code splitting\n        const { default: countriesData } = await __webpack_require__.e(/*! import() */ \"_ssr_src_data_countries_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! @/data/countries.json */ \"(ssr)/./src/data/countries.json\", 19));\n        // Extract the countries array from the data structure\n        const countriesArray = countriesData.countries || [];\n        // Validate and process the data\n        const processedCountries = countriesArray.map((country)=>({\n                ...country,\n                // Ensure all required fields are present\n                population: country.population || 0,\n                exports: country.exports || [],\n                landmarks: country.landmarks || [],\n                wildlife: country.wildlife || [],\n                culturalElements: country.culturalElements || {\n                    traditionalClothing: [],\n                    cuisine: [],\n                    music: [],\n                    dances: []\n                },\n                notableFigures: country.notableFigures || []\n            }));\n        // Update cache\n        countriesCache = processedCountries;\n        cacheTimestamp = now;\n        return processedCountries;\n    } catch (error) {\n        console.error('Failed to load countries data:', error);\n        // Return empty array as fallback\n        return [];\n    }\n};\n// Preload specific country data by ID\nconst getCountryById = async (id)=>{\n    const countries = await loadCountriesData();\n    return countries.find((country)=>country.id === id) || null;\n};\n// Preload countries by region with caching\nconst regionCache = {};\nconst getCountriesByRegion = async (region)=>{\n    // Check region cache first\n    if (regionCache[region]) {\n        return regionCache[region];\n    }\n    const countries = await loadCountriesData();\n    const filteredCountries = countries.filter((country)=>country.region === region);\n    // Cache the result\n    regionCache[region] = filteredCountries;\n    return filteredCountries;\n};\n// Preload and cache flag images\nconst flagImageCache = new Map();\nconst preloadFlagImage = (countryId, flagUrl)=>{\n    return new Promise((resolve, reject)=>{\n        // Check if already cached\n        if (flagImageCache.has(countryId)) {\n            resolve();\n            return;\n        }\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.onload = ()=>{\n            flagImageCache.set(countryId, flagUrl);\n            resolve();\n        };\n        img.onerror = ()=>{\n            reject(new Error(`Failed to load flag for ${countryId}`));\n        };\n        img.src = flagUrl;\n    });\n};\n// Batch preload flag images for better performance\nconst preloadFlagImages = async (countries)=>{\n    const preloadPromises = countries.map((country)=>preloadFlagImage(country.id, country.flagUrl).catch(()=>{\n            // Silently fail for individual flags to not block the entire batch\n            console.warn(`Failed to preload flag for ${country.name}`);\n        }));\n    try {\n        await Promise.allSettled(preloadPromises);\n    } catch (error) {\n        console.warn('Some flag images failed to preload:', error);\n    }\n};\n// Initialize data loading on app start\nconst initializeDataLoading = async ()=>{\n    try {\n        // Start loading countries data\n        const countriesPromise = loadCountriesData();\n        // Wait for countries to load, then preload flag images\n        const countries = await countriesPromise;\n        // Preload flag images in the background (don't wait)\n        preloadFlagImages(countries.slice(0, 20)).catch(()=>{\n        // Silently handle errors\n        });\n    } catch (error) {\n        console.error('Failed to initialize data loading:', error);\n    }\n};\n// Clear cache (useful for development or when data updates)\nconst clearCache = ()=>{\n    countriesCache = null;\n    cacheTimestamp = 0;\n    Object.keys(regionCache).forEach((key)=>delete regionCache[key]);\n    flagImageCache.clear();\n};\n// Get cache statistics for debugging\nconst getCacheStats = ()=>{\n    return {\n        countriesCache: {\n            loaded: !!countriesCache,\n            count: countriesCache?.length || 0,\n            age: Date.now() - cacheTimestamp\n        },\n        regionCache: {\n            regions: Object.keys(regionCache).length,\n            totalCountries: Object.values(regionCache).reduce((sum, countries)=>sum + countries.length, 0)\n        },\n        flagImageCache: {\n            count: flagImageCache.size\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/dataLoader.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/zustand","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEEK%20EDEN%5CDocuments%5Caugment-projects%5CGAMES%20FOR%20ARICA%5Cgames-for-africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();