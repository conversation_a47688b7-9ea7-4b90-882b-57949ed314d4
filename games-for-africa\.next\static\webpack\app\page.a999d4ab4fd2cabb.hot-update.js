"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useProgressStore */ \"(app-pages-browser)/./src/stores/useProgressStore.ts\");\n/* harmony import */ var _components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserDashboard */ \"(app-pages-browser)/./src/components/UserDashboard.tsx\");\n/* harmony import */ var _components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CelebrationModal */ \"(app-pages-browser)/./src/components/CelebrationModal.tsx\");\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/games/MemoryGrid */ \"(app-pages-browser)/./src/components/games/MemoryGrid.tsx\");\n/* harmony import */ var _components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/games/JigsawPuzzle */ \"(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\");\n/* harmony import */ var _components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/games/TimelineBuilder */ \"(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\");\n/* harmony import */ var _components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/games/WhereInAfrica */ \"(app-pages-browser)/./src/components/games/WhereInAfrica.tsx\");\n/* harmony import */ var _components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/games/DressTheCharacter */ \"(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\");\n/* harmony import */ var _components_games_CountryNameScramble__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/games/CountryNameScramble */ \"(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\");\n/* harmony import */ var _components_games_FlagMatching__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/games/FlagMatching */ \"(app-pages-browser)/./src/components/games/FlagMatching.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    },\n    {\n        type: 'country-name-scramble',\n        name: 'Country Name Scramble',\n        description: 'Unscramble letters to form African country names',\n        icon: '🔤',\n        difficulty: 'medium',\n        estimatedTime: 10,\n        maxScore: 300\n    },\n    {\n        type: 'flag-matching',\n        name: 'Flag Matching',\n        description: 'Match African country flags with their names',\n        icon: '🏁',\n        difficulty: 'easy',\n        estimatedTime: 6,\n        maxScore: 200\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [celebrationData, setCelebrationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: 'gameComplete',\n        data: {}\n    });\n    const { profile, loadProfile, recordGameCompletion } = (0,_stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsClient(true);\n            loadProfile();\n        }\n    }[\"Home.useEffect\"], [\n        loadProfile\n    ]);\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_17__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = async (score)=>{\n        var _gameConfigs_find;\n        if (!currentGame) return;\n        const gameTitle = ((_gameConfigs_find = gameConfigs.find((g)=>g.type === currentGame)) === null || _gameConfigs_find === void 0 ? void 0 : _gameConfigs_find.name) || currentGame;\n        const completionTime = 300; // This should be passed from the game component\n        const difficulty = (profile === null || profile === void 0 ? void 0 : profile.preferences.difficulty) || 'intermediate';\n        const isPerfectScore = score >= 100; // This should be determined by the game\n        try {\n            const result = await recordGameCompletion(currentGame, score, completionTime, difficulty, isPerfectScore);\n            // Show game completion celebration\n            setCelebrationData({\n                isOpen: true,\n                type: 'gameComplete',\n                data: {\n                    score,\n                    gameTitle,\n                    xpGained: result.xpGained\n                }\n            });\n            // Show level up celebration if applicable\n            if (result.levelUp) {\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'levelUp',\n                        data: {\n                            newLevel: profile === null || profile === void 0 ? void 0 : profile.level,\n                            xpGained: result.xpGained\n                        }\n                    });\n                }, 2000);\n            }\n            // Show achievement celebrations\n            result.newAchievements.forEach((achievement, index)=>{\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'achievement',\n                        data: {\n                            achievement\n                        }\n                    });\n                }, 3000 + index * 2000);\n            });\n        } catch (error) {\n            console.error('Error recording game completion:', error);\n        }\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 16\n                }, this);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case 'where-in-africa':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            case 'dress-character':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n            case 'country-name-scramble':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryNameScramble__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 16\n                }, this);\n            case 'flag-matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_FlagMatching__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl text-accent-gold\",\n                        children: \"Loading Games for Africa...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-gold font-medium\",\n                                                    children: profile.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"Level \",\n                                                        profile.level,\n                                                        \" • \",\n                                                        profile.totalXP,\n                                                        \" XP\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowDashboard(true),\n                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-gray-900 font-bold hover:scale-105 transition-transform\",\n                                            children: profile.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>{\n                                const gameProgress = profile === null || profile === void 0 ? void 0 : profile.gamesProgress[game.type];\n                                const isCompleted = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.completed) || false;\n                                const bestScore = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.bestScore) || 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer relative \".concat(isCompleted ? 'border-green-400' : 'border-gray-700'),\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: [\n                                        isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl\",\n                                                            children: game.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"⏱️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        game.estimatedTime,\n                                                                        \"min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                    children: game.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4 flex-grow\",\n                                                    children: game.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                            children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                bestScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-400 text-sm font-medium\",\n                                                                    children: [\n                                                                        \"Best: \",\n                                                                        bestScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: [\n                                                                        \"Max: \",\n                                                                        game.maxScore,\n                                                                        \" pts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                    children: \"▶️ Play Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, game.type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this),\n            showDashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onClose: ()=>setShowDashboard(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 475,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: celebrationData.isOpen,\n                onClose: ()=>setCelebrationData((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        })),\n                type: celebrationData.type,\n                data: celebrationData.data\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 7\n    }, this);\n}\n_s(Home, \"F/q13Zo1gnuX1bo7PfVxtyEnecY=\", false, function() {\n    return [\n        _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setRoundStartTime(Date.now());\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 279,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl\",\n                                                    children: currentCountry.flagUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl\",\n                                            children: currentCountry.flagUrl\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"cT4d+D4snhqfIe6om8d5KTnT0/g=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 || currentRound && currentRound.matches === getDifficultySettings(difficulty).pairs) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        currentRound\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 277,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-2\",\n                                                    children: flag.country.flagUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl mb-2\",\n                            children: lastMatchedCountry.flagUrl\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\\'s Up!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound.matches\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    settings.pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            Math.round(currentRound.matches / Math.max(currentRound.attempts, 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 32\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 411,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"lK7sZ8u6dyR8Xte60Z2D2JGS/bI=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ })

});