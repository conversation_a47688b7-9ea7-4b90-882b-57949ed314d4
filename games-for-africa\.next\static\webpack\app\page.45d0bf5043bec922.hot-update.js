"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setRoundStartTime(Date.now());\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            countryId: currentCountry.id,\n                                            size: \"large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 457,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"nU2AS56vxz9naySQPe7ueP3B7Io=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ })

});