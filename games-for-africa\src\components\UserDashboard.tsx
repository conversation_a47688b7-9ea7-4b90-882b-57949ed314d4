'use client';

import React, { useState } from 'react';
import { useProgressStore } from '@/stores/useProgressStore';
import { LEVEL_SYSTEM } from '@/types/progress';

interface UserDashboardProps {
  onClose: () => void;
}

const UserDashboard: React.FC<UserDashboardProps> = ({ onClose }) => {
  const { profile, updateUsername } = useProgressStore();
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [newUsername, setNewUsername] = useState(profile?.username || '');

  if (!profile) return null;

  const currentLevel = LEVEL_SYSTEM.find(l => l.level === profile.level);
  const nextLevel = LEVEL_SYSTEM.find(l => l.level === profile.level + 1);
  const progressToNext = nextLevel 
    ? ((profile.totalXP - currentLevel!.xpRequired) / (nextLevel.xpRequired - currentLevel!.xpRequired)) * 100
    : 100;

  const unlockedAchievements = profile.achievements.filter(a => a.unlockedAt);
  const completedGames = Object.values(profile.gamesProgress).filter(g => g.completed).length;

  const handleUsernameSubmit = () => {
    if (newUsername.trim()) {
      updateUsername(newUsername.trim());
      setIsEditingUsername(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
      <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-2xl font-bold text-gray-900">
              {profile.username.charAt(0).toUpperCase()}
            </div>
            <div>
              {isEditingUsername ? (
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newUsername}
                    onChange={(e) => setNewUsername(e.target.value)}
                    className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-yellow-400 outline-none"
                    onKeyPress={(e) => e.key === 'Enter' && handleUsernameSubmit()}
                    autoFocus
                  />
                  <button
                    onClick={handleUsernameSubmit}
                    className="text-green-400 hover:text-green-300"
                  >
                    ✓
                  </button>
                  <button
                    onClick={() => {
                      setIsEditingUsername(false);
                      setNewUsername(profile.username);
                    }}
                    className="text-red-400 hover:text-red-300"
                  >
                    ✗
                  </button>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold text-white">{profile.username}</h2>
                  <button
                    onClick={() => setIsEditingUsername(true)}
                    className="text-gray-400 hover:text-yellow-400"
                  >
                    ✏️
                  </button>
                </div>
              )}
              <p className="text-yellow-400 font-medium">
                Level {profile.level} • {currentLevel?.title}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ✕
          </button>
        </div>

        <div className="p-6 space-y-8">
          {/* Level Progress */}
          <div className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Experience Progress</h3>
              <div className="text-yellow-400 font-bold">
                {profile.totalXP} XP
              </div>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-4 mb-2">
              <div
                className="bg-gradient-to-r from-yellow-400 to-orange-500 h-4 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(progressToNext, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-sm text-gray-400">
              <span>Level {profile.level}</span>
              {nextLevel && (
                <span>
                  {nextLevel.xpRequired - profile.totalXP} XP to Level {nextLevel.level}
                </span>
              )}
            </div>
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-green-400">{completedGames}</div>
              <div className="text-gray-400 text-sm">Games Completed</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-blue-400">{profile.statistics.totalGamesPlayed}</div>
              <div className="text-gray-400 text-sm">Total Games Played</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-purple-400">
                {Math.round(profile.statistics.averageScore || 0)}
              </div>
              <div className="text-gray-400 text-sm">Average Score</div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-3xl font-bold text-orange-400">{unlockedAchievements.length}</div>
              <div className="text-gray-400 text-sm">Achievements</div>
            </div>
          </div>

          {/* Recent Achievements */}
          <div className="bg-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Recent Achievements</h3>
            {unlockedAchievements.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {unlockedAchievements.slice(-6).map((achievement) => (
                  <div
                    key={achievement.id}
                    className="flex items-center space-x-3 bg-gray-600 rounded-lg p-3"
                  >
                    <div className="text-2xl">{achievement.icon}</div>
                    <div>
                      <div className="text-white font-medium">{achievement.name}</div>
                      <div className="text-gray-400 text-sm">{achievement.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-400 py-8">
                <div className="text-4xl mb-2">🏆</div>
                <p>Complete your first game to earn achievements!</p>
              </div>
            )}
          </div>

          {/* Game Progress */}
          <div className="bg-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Game Progress</h3>
            <div className="space-y-3">
              {Object.entries(profile.gamesProgress).map(([gameId, progress]) => (
                <div key={gameId} className="flex items-center justify-between bg-gray-600 rounded-lg p-3">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${progress.completed ? 'bg-green-400' : 'bg-gray-500'}`} />
                    <span className="text-white font-medium capitalize">{gameId.replace('-', ' ')}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-yellow-400">Best: {progress.bestScore}</span>
                    <span className="text-gray-400">Attempts: {progress.totalAttempts}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      progress.difficulty === 'beginner' ? 'bg-green-500 bg-opacity-20 text-green-400' :
                      progress.difficulty === 'intermediate' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' :
                      'bg-red-500 bg-opacity-20 text-red-400'
                    }`}>
                      {progress.difficulty}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Streak Information */}
          {profile.statistics.streakDays > 0 && (
            <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 text-white">
              <div className="flex items-center space-x-3">
                <div className="text-3xl">🔥</div>
                <div>
                  <h3 className="text-xl font-semibold">Daily Streak</h3>
                  <p className="text-orange-100">
                    You've played for {profile.statistics.streakDays} consecutive days!
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
