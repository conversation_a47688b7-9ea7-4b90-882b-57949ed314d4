import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { GameStore, GameType, UserProgress, Achievement } from '@/types';

const initialProgress: UserProgress = {
  totalScore: 0,
  gamesPlayed: 0,
  totalTimeSpent: 0,
  achievements: [],
  favoriteGame: null,
  streakCount: 0,
  bestScores: {
    'quiz': 0,
    'matching': 0,
    'jigsaw-puzzle': 0,
    'memory-grid': 0,
    'speed-challenge': 0,
    'country-explorer': 0,
    'mystery-land': 0,
    'timeline-builder': 0,
    'dress-character': 0,
    'where-in-africa': 0,
  },
  categoryProgress: {
    'geography': 0,
    'history': 0,
    'culture': 0,
    'wildlife': 0,
    'notable-figures': 0,
  },
};

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      currentGame: null,
      gameState: null,
      userProgress: initialProgress,
      isLoading: false,
      error: null,

      setCurrentGame: (game: GameType | null) => {
        set({ currentGame: game, gameState: null, error: null });
      },

      updateGameState: (state: any) => {
        set({ gameState: state });
      },

      updateProgress: (progress: Partial<UserProgress>) => {
        const currentProgress = get().userProgress;
        set({
          userProgress: {
            ...currentProgress,
            ...progress,
          },
        });
      },

      addAchievement: (achievement: Achievement) => {
        const currentProgress = get().userProgress;
        const existingAchievement = currentProgress.achievements.find(
          (a) => a.id === achievement.id
        );

        if (!existingAchievement) {
          set({
            userProgress: {
              ...currentProgress,
              achievements: [
                ...currentProgress.achievements,
                { ...achievement, unlocked: true, unlockedAt: new Date() },
              ],
            },
          });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      resetGame: () => {
        set({ currentGame: null, gameState: null, error: null });
      },
    }),
    {
      name: 'games-for-africa-store',
      partialize: (state) => ({
        userProgress: state.userProgress,
      }),
    }
  )
);

// Helper hooks for specific game states
export const useQuizStore = () => {
  const { gameState, updateGameState } = useGameStore();
  return {
    quizState: gameState,
    updateQuizState: updateGameState,
  };
};

export const useMatchingStore = () => {
  const { gameState, updateGameState } = useGameStore();
  return {
    matchingState: gameState,
    updateMatchingState: updateGameState,
  };
};

export const useMemoryStore = () => {
  const { gameState, updateGameState } = useGameStore();
  return {
    memoryState: gameState,
    updateMemoryState: updateGameState,
  };
};

export const useMapStore = () => {
  const { gameState, updateGameState } = useGameStore();
  return {
    mapState: gameState,
    updateMapState: updateGameState,
  };
};

export const useTimedChallengeStore = () => {
  const { gameState, updateGameState } = useGameStore();
  return {
    timedState: gameState,
    updateTimedState: updateGameState,
  };
};

// Achievement checking functions
export const checkAchievements = (
  gameType: GameType,
  score: number,
  progress: UserProgress
) => {
  const achievements: Achievement[] = [];

  // First game achievement
  if (progress.gamesPlayed === 0) {
    achievements.push({
      id: 'first-game',
      name: 'Welcome Explorer!',
      description: 'Complete your first game',
      icon: '🎮',
      unlocked: false,
      condition: 'Complete any game',
    });
  }

  // Perfect score achievement
  if (score === 100) {
    achievements.push({
      id: 'perfect-score',
      name: 'Perfect Explorer',
      description: 'Get a perfect score in any game',
      icon: '⭐',
      unlocked: false,
      condition: 'Score 100% in any game',
    });
  }

  // Streak achievements
  if (progress.streakCount >= 5) {
    achievements.push({
      id: 'streak-5',
      name: 'On Fire!',
      description: 'Get 5 correct answers in a row',
      icon: '🔥',
      unlocked: false,
      condition: 'Answer 5 questions correctly in a row',
    });
  }

  // Game-specific achievements
  if (gameType === 'quiz' && score >= 80) {
    achievements.push({
      id: 'quiz-master',
      name: 'Quiz Master',
      description: 'Score 80% or higher in a quiz',
      icon: '🧠',
      unlocked: false,
      condition: 'Score 80%+ in quiz game',
    });
  }

  return achievements;
};
