{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/CountryExplorer": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/CountryExplorer", "files": ["static/chunks/_app-pages-browser_src_components_games_CountryExplorer_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/CountryNameScramble": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/CountryNameScramble", "files": ["static/chunks/_app-pages-browser_src_components_games_CountryNameScramble_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/DressTheCharacter": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/DressTheCharacter", "files": ["static/chunks/_app-pages-browser_src_components_games_DressTheCharacter_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/FlagMatching": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/FlagMatching", "files": ["static/chunks/_app-pages-browser_src_components_games_FlagMatching_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/JigsawPuzzle": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/JigsawPuzzle", "files": ["static/chunks/_app-pages-browser_src_components_games_JigsawPuzzle_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/MatchingGame": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/MatchingGame", "files": ["static/chunks/_app-pages-browser_src_components_games_MatchingGame_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/MemoryGrid": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/MemoryGrid", "files": ["static/chunks/_app-pages-browser_src_components_games_MemoryGrid_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/MysteryLand": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/MysteryLand", "files": ["static/chunks/_app-pages-browser_src_components_games_MysteryLand_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/QuizGame": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/QuizGame", "files": ["static/chunks/_app-pages-browser_src_components_games_QuizGame_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/SpeedChallenge": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/SpeedChallenge", "files": ["static/chunks/_app-pages-browser_src_components_games_SpeedChallenge_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/TimelineBuilder": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/TimelineBuilder", "files": ["static/chunks/_app-pages-browser_src_components_games_TimelineBuilder_tsx.js"]}, "components\\ui\\LazyGameLoader.tsx -> @/components/games/WhereInAfrica": {"id": "components\\ui\\LazyGameLoader.tsx -> @/components/games/WhereInAfrica", "files": ["static/chunks/_app-pages-browser_src_components_games_WhereInAfrica_tsx.js"]}, "utils\\dataLoader.ts -> @/data/countries.json": {"id": "utils\\dataLoader.ts -> @/data/countries.json", "files": ["static/chunks/_app-pages-browser_src_data_countries_json.js"]}}