"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setRoundStartTime(Date.now());\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl\",\n                                            children: currentCountry.flagUrl\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"cT4d+D4snhqfIe6om8d5KTnT0/g=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2dhbWVzL0NvdW50cnlOYW1lU2NyYW1ibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1EO0FBRVo7QUFDVztBQXdCbEQsTUFBTUssc0JBQTBEO1FBQUMsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUU7O0lBQ3hGLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ1MsUUFBUUMsVUFBVSxHQUFHViwrQ0FBUUEsQ0FBYyxFQUFFO0lBQ3BELE1BQU0sQ0FBQ1csaUJBQWlCQyxtQkFBbUIsR0FBR1osK0NBQVFBLENBQW9CLEVBQUU7SUFDNUUsTUFBTSxDQUFDYSxZQUFZQyxjQUFjLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2UsVUFBVUMsWUFBWSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDaUIsT0FBT0MsU0FBUyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDbUIsUUFBUUMsVUFBVSxHQUFHcEIsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDcUIsWUFBWUMsY0FBYyxHQUFHdEIsK0NBQVFBLENBQWtCO0lBQzlELE1BQU0sQ0FBQ3VCLGFBQWFDLGVBQWUsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3lCLGNBQWNDLGdCQUFnQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHNUIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDNkIsWUFBWUMsY0FBYyxHQUFHOUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDK0IsV0FBV0MsYUFBYSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUMsZ0JBQWdCQyxrQkFBa0IsR0FBR2xDLCtDQUFRQSxDQUFDbUMsS0FBS0MsR0FBRztJQUU3RCxNQUFNQyxjQUFjO0lBRXBCLE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQixPQUFRQTtZQUNOLEtBQUs7Z0JBQVksT0FBTztZQUN4QixLQUFLO2dCQUFnQixPQUFPO1lBQzVCLEtBQUs7Z0JBQVksT0FBTztRQUMxQjtJQUNGO0lBRUEsTUFBTUMsOEJBQThCLENBQUNuQyxXQUFzQmdCO1FBQ3pELE9BQU9oQixVQUFVb0MsTUFBTSxDQUFDQyxDQUFBQTtZQUN0QixNQUFNQyxhQUFhRCxRQUFRRSxJQUFJLENBQUNDLE9BQU8sQ0FBQyxRQUFRLElBQUlDLE1BQU07WUFDMUQsT0FBUXpCO2dCQUNOLEtBQUs7b0JBQVksT0FBT3NCLGNBQWMsS0FBS0EsY0FBYztnQkFDekQsS0FBSztvQkFBZ0IsT0FBT0EsY0FBYyxLQUFLQSxjQUFjO2dCQUM3RCxLQUFLO29CQUFZLE9BQU9BLGNBQWM7Z0JBQ3RDO29CQUFTLE9BQU87WUFDbEI7UUFDRjtJQUNGO0lBRUEsTUFBTUksc0JBQXNCLENBQUNIO1FBQzNCLE1BQU1JLFlBQVlKLEtBQUtDLE9BQU8sQ0FBQyxRQUFRLElBQUlJLFdBQVc7UUFDdEQsTUFBTUMsVUFBVUYsVUFBVUcsS0FBSyxDQUFDLElBQUlDLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQyxRQUFXO2dCQUMxREMsSUFBSSxVQUFtQkMsT0FBVEYsT0FBTSxLQUFpQixPQUFkRSxLQUFLQyxNQUFNO2dCQUNsQ0o7Z0JBQ0FLLGVBQWVKO2dCQUNmSyxRQUFRO1lBQ1Y7UUFDQSxPQUFPekQsb0RBQVlBLENBQUNnRDtJQUN0QjtJQUVBLE1BQU1VLGlCQUFpQjtRQUNyQixNQUFNQyxvQkFBb0JyQiw0QkFBNEJuQyxXQUFXZ0I7UUFDakUsTUFBTXlDLG9CQUFvQjVELG9EQUFZQSxDQUFDMkQsbUJBQW1CRSxLQUFLLENBQUMsR0FBRzFCO1FBRW5FLE9BQU95QixrQkFBa0JWLEdBQUcsQ0FBQ1YsQ0FBQUEsVUFBWTtnQkFDdkNBO2dCQUNBc0Isa0JBQWtCakIsb0JBQW9CTCxRQUFRRSxJQUFJO2dCQUNsRC9CLFlBQVk7Z0JBQ1pvRCxZQUFZO2dCQUNaQyxXQUFXO1lBQ2I7SUFDRjtJQUVBakUsZ0RBQVNBO3lDQUFDO1lBQ1IsSUFBSXNCLGVBQWVSLFdBQVcsS0FBSyxDQUFDVSxjQUFjO2dCQUNoRCxNQUFNMEMsUUFBUUM7MkRBQVcsSUFBTXBELFlBQVlELFdBQVc7MERBQUk7Z0JBQzFEO3FEQUFPLElBQU1zRCxhQUFhRjs7WUFDNUIsT0FBTyxJQUFJcEQsYUFBYSxHQUFHO2dCQUN6QnVEO1lBQ0Y7UUFDRjt3Q0FBRztRQUFDL0M7UUFBYVI7UUFBVVU7S0FBYTtJQUV4QyxNQUFNOEMsWUFBWTtRQUNoQixNQUFNQyxZQUFZWjtRQUNsQmxELFVBQVU4RDtRQUNWaEUsZ0JBQWdCO1FBQ2hCVSxTQUFTO1FBQ1RFLFVBQVU7UUFDVkosWUFBWTtRQUNaRixjQUFjO1FBQ2RGLG1CQUFtQixFQUFFO1FBQ3JCWSxlQUFlO1FBQ2ZFLGdCQUFnQjtRQUNoQkksY0FBYztRQUNkSSxrQkFBa0JDLEtBQUtDLEdBQUc7SUFDNUI7SUFFQSxNQUFNcUMsb0JBQW9CLENBQUNwQjtRQUN6QixJQUFJQSxPQUFPTSxNQUFNLElBQUk5QixZQUFZO1FBRWpDLE1BQU02QyxxQkFBcUI7ZUFBSS9EO1lBQWlCO2dCQUFFLEdBQUcwQyxNQUFNO2dCQUFFTSxRQUFRO1lBQUs7U0FBRTtRQUM1RS9DLG1CQUFtQjhEO1FBQ25CNUQsY0FBYzRELG1CQUFtQnRCLEdBQUcsQ0FBQ3VCLENBQUFBLElBQUtBLEVBQUV0QixNQUFNLEVBQUV1QixJQUFJLENBQUM7UUFFekQsd0RBQXdEO1FBQ3hELE1BQU1DLG1CQUFtQnBFLE1BQU0sQ0FBQ0YsYUFBYTtRQUM3QyxNQUFNdUUsMEJBQTBCRCxpQkFBaUJiLGdCQUFnQixDQUFDWixHQUFHLENBQUN1QixDQUFBQSxJQUNwRUEsRUFBRXBCLEVBQUUsS0FBS0YsT0FBT0UsRUFBRSxHQUFHO2dCQUFFLEdBQUdvQixDQUFDO2dCQUFFaEIsUUFBUTtZQUFLLElBQUlnQjtRQUdoRCxNQUFNSSxnQkFBZ0I7ZUFBSXRFO1NBQU87UUFDakNzRSxhQUFhLENBQUN4RSxhQUFhLEdBQUc7WUFDNUIsR0FBR3NFLGdCQUFnQjtZQUNuQmIsa0JBQWtCYztRQUNwQjtRQUNBcEUsVUFBVXFFO0lBQ1o7SUFFQSxNQUFNQyxvQkFBb0I7UUFDeEIsSUFBSW5ELFlBQVk7UUFFaEJqQixtQkFBbUIsRUFBRTtRQUNyQkUsY0FBYztRQUVkLDhCQUE4QjtRQUM5QixNQUFNK0QsbUJBQW1CcEUsTUFBTSxDQUFDRixhQUFhO1FBQzdDLE1BQU0wRSx3QkFBd0JKLGlCQUFpQmIsZ0JBQWdCLENBQUNaLEdBQUcsQ0FBQ3VCLENBQUFBLElBQU07Z0JBQUUsR0FBR0EsQ0FBQztnQkFBRWhCLFFBQVE7WUFBTTtRQUVoRyxNQUFNb0IsZ0JBQWdCO2VBQUl0RTtTQUFPO1FBQ2pDc0UsYUFBYSxDQUFDeEUsYUFBYSxHQUFHO1lBQzVCLEdBQUdzRSxnQkFBZ0I7WUFDbkJiLGtCQUFrQmlCO1FBQ3BCO1FBQ0F2RSxVQUFVcUU7SUFDWjtJQUVBLE1BQU1HLHFCQUFxQjtRQUN6QixJQUFJLENBQUNyRSxjQUFjZ0IsWUFBWTtRQUUvQixNQUFNc0QsaUJBQWlCMUUsTUFBTSxDQUFDRixhQUFhLENBQUNtQyxPQUFPO1FBQ25ELE1BQU0wQyxnQkFBZ0JELGVBQWV2QyxJQUFJLENBQUNDLE9BQU8sQ0FBQyxRQUFRLElBQUlJLFdBQVc7UUFDekUsTUFBTW9DLGtCQUFrQnhFLFdBQVdnQyxPQUFPLENBQUMsUUFBUSxJQUFJSSxXQUFXO1FBQ2xFLE1BQU1xQyxVQUFVRCxvQkFBb0JEO1FBRXBDcEQsYUFBYXNEO1FBQ2J4RCxjQUFjO1FBRWQsTUFBTW9DLFlBQVksQ0FBQy9CLEtBQUtDLEdBQUcsS0FBS0gsY0FBYSxJQUFLO1FBRWxELElBQUlxRCxTQUFTO1lBQ1gsTUFBTUMsYUFBYUgsY0FBY3RDLE1BQU07WUFDdkMsTUFBTTBDLFlBQVloQyxLQUFLaUMsR0FBRyxDQUFDLEdBQUdqQyxLQUFLa0MsS0FBSyxDQUFDLENBQUMsS0FBS3hCLFNBQVEsSUFBSztZQUM1RCxNQUFNeUIsbUJBQW1CLElBQUt4RSxTQUFTO1lBQ3ZDLE1BQU15RSx1QkFBdUJ0RCx3QkFBd0JqQjtZQUVyRCxNQUFNd0UsYUFBYXJDLEtBQUtrQyxLQUFLLENBQUMsQ0FBQ0gsYUFBYUMsU0FBUSxJQUFLRyxtQkFBbUJDO1lBQzVFMUUsU0FBU0QsUUFBUTRFO1lBQ2pCekUsVUFBVUQsU0FBUztRQUNyQixPQUFPO1lBQ0xDLFVBQVU7UUFDWjtRQUVBLG9CQUFvQjtRQUNwQixNQUFNMkQsZ0JBQWdCO2VBQUl0RTtTQUFPO1FBQ2pDc0UsYUFBYSxDQUFDeEUsYUFBYSxHQUFHO1lBQzVCLEdBQUd3RSxhQUFhLENBQUN4RSxhQUFhO1lBQzlCTTtZQUNBb0QsWUFBWTtZQUNaQztRQUNGO1FBQ0F4RCxVQUFVcUU7UUFFVlgsV0FBVztZQUNULElBQUk3RCxlQUFlOEIsY0FBYyxHQUFHO2dCQUNsQzdCLGdCQUFnQkQsZUFBZTtnQkFDL0JPLGNBQWM7Z0JBQ2RGLG1CQUFtQixFQUFFO2dCQUNyQmtCLGNBQWM7Z0JBQ2RGLFlBQVk7Z0JBQ1pNLGtCQUFrQkMsS0FBS0MsR0FBRztnQkFFMUIsK0JBQStCO2dCQUMvQixNQUFNMEQsZ0JBQWdCZixhQUFhLENBQUN4RSxlQUFlLEVBQUU7Z0JBQ3JELE1BQU13RixlQUFlRCxjQUFjOUIsZ0JBQWdCLENBQUNaLEdBQUcsQ0FBQ3VCLENBQUFBLElBQU07d0JBQUUsR0FBR0EsQ0FBQzt3QkFBRWhCLFFBQVE7b0JBQU07Z0JBQ3BGb0IsYUFBYSxDQUFDeEUsZUFBZSxFQUFFLEdBQUc7b0JBQ2hDLEdBQUd1RixhQUFhO29CQUNoQjlCLGtCQUFrQitCO2dCQUNwQjtnQkFDQXJGLFVBQVVxRTtZQUNaLE9BQU87Z0JBQ0xUO1lBQ0Y7UUFDRixHQUFHO0lBQ0w7SUFFQSxNQUFNQSxnQkFBZ0I7UUFDcEI1QyxnQkFBZ0I7UUFDaEIwQyxXQUFXLElBQU05RCxXQUFXVyxRQUFRO0lBQ3RDO0lBRUEsTUFBTStFLGFBQWE7UUFDakJwRSxZQUFZLENBQUNEO0lBQ2Y7SUFFQSxJQUFJLENBQUNKLGFBQWE7UUFDaEIscUJBQ0UsOERBQUMwRTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFxQzs7Ozs7O2tDQUNuRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQWdCOzs7Ozs7a0NBQy9CLDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBNkI7Ozs7OztrQ0FLMUMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQUdILFdBQVU7MENBQXdDOzs7Ozs7MENBQ3RELDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWjtvQ0FBRTtvQ0FBWTtvQ0FBZ0I7aUNBQVcsQ0FBdUI5QyxHQUFHLENBQUMsQ0FBQ2IscUJBQ3BFLDhEQUFDK0Q7d0NBRUNDLFNBQVMsSUFBTWpGLGNBQWNpQjt3Q0FDN0IyRCxXQUFXLDBDQUlWLE9BSEM3RSxlQUFla0IsT0FDWCxnQ0FDQTtrREFHTiw0RUFBQzBEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQTRCM0Q7Ozs7Ozs4REFDM0MsOERBQUMwRDtvREFBSUMsV0FBVTs7d0RBQ1ozRCxTQUFTLGNBQWM7d0RBQ3ZCQSxTQUFTLGtCQUFrQjt3REFDM0JBLFNBQVMsY0FBYzs7Ozs7Ozs7Ozs7Ozt1Q0FidkJBOzs7Ozs7Ozs7Ozs7Ozs7O2tDQXFCYiw4REFBQzBEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7Ozs7Ozs7OztrQ0FHTCw4REFBQ0U7d0JBQ0NDLFNBQVNoQzt3QkFDVDJCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxJQUFJLENBQUN6RixNQUFNLENBQUNGLGFBQWEsRUFBRTtRQUN6QixxQkFDRSw4REFBQzBGO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLE1BQU1mLGlCQUFpQjFFLE1BQU0sQ0FBQ0YsYUFBYSxDQUFDbUMsT0FBTztJQUVuRCxxQkFDRSw4REFBQ3VEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUdELFdBQVU7MENBQWdDOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1oxQyxLQUFLa0MsS0FBSyxDQUFDM0UsV0FBVztvQ0FBSTtvQ0FBR0EsQ0FBQUEsV0FBVyxFQUFDLEVBQUd5RixRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHOzs7Ozs7Ozs7Ozs7O2tDQUl4RSw4REFBQ1I7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUFzQ2pGOzs7Ozs7a0RBQ3JELDhEQUFDZ0Y7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBRXpDLDhEQUFDRDs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUFxQy9FOzs7Ozs7a0RBQ3BELDhEQUFDOEU7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBRXpDLDhEQUFDRDs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUFvQzNGLGVBQWU7Ozs7OztrREFDbEUsOERBQUMwRjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FFekMsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQXNDN0Q7Ozs7OztrREFDckQsOERBQUM0RDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FFekMsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQWlEN0U7Ozs7OztrREFDaEUsOERBQUM0RTt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLM0MsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQ0NDLFdBQVU7Z0NBQ1ZRLE9BQU87b0NBQUVDLE9BQU8sR0FBNEMsT0FBekMsQ0FBRXBHLGVBQWUsS0FBSzhCLGNBQWUsS0FBSTtnQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkUsOERBQUM0RDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQUdILFdBQVU7MENBQXFDOzs7Ozs7MENBS25ELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNJO3dDQUNDQyxTQUFTUDt3Q0FDVEUsV0FBVTs7NENBRVR2RSxXQUFXLGNBQWM7NENBQVk7Ozs7Ozs7b0NBR3ZDQSwwQkFDQyw4REFBQ3NFO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMvRixnRUFBU0E7b0RBQ1J5RyxXQUFXekIsZUFBZTVCLEVBQUU7b0RBQzVCc0QsTUFBSzs7Ozs7OzhEQUVQLDhEQUFDWjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOztnRUFBOEI7Z0VBQVNmLGVBQWUyQixNQUFNOzs7Ozs7O3NFQUMzRSw4REFBQ2I7NERBQUlDLFdBQVU7O2dFQUF3QjtnRUFBVWYsZUFBZTRCLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTbkYsOERBQUNkO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2M7Z0NBQUdkLFdBQVU7MENBQW9EOzs7Ozs7MENBQ2xFLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWnpGLE1BQU0sQ0FBQ0YsYUFBYSxDQUFDeUQsZ0JBQWdCLENBQUNaLEdBQUcsQ0FBQyxDQUFDQyx1QkFDMUMsOERBQUNpRDt3Q0FFQ0MsU0FBUyxJQUFNOUIsa0JBQWtCcEI7d0NBQ2pDNEQsVUFBVTVELE9BQU9NLE1BQU0sSUFBSTlCO3dDQUMzQnFFLFdBQVcsc0VBSVYsT0FIQzdDLE9BQU9NLE1BQU0sR0FDVCxpREFDQTtrREFHTE4sT0FBT0EsTUFBTTt1Q0FUVEEsT0FBT0UsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztrQ0FnQnRCLDhEQUFDMEM7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDYztnQ0FBR2QsV0FBVTswQ0FBb0Q7Ozs7OzswQ0FDbEUsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnJGLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1yQiw4REFBQ29GO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0k7Z0NBQ0NDLFNBQVN2QjtnQ0FDVGlDLFVBQVVwRjtnQ0FDVnFFLFdBQVU7MENBQ1g7Ozs7OzswQ0FHRCw4REFBQ0k7Z0NBQ0NDLFNBQVNyQjtnQ0FDVCtCLFVBQVUsQ0FBQ3BHLGNBQWNnQjtnQ0FDekJxRSxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7b0JBTUZyRSw0QkFDQyw4REFBQ29FO3dCQUFJQyxXQUFXLDhCQUlmLE9BSENuRSxZQUNJLGdEQUNBO2tDQUVKLDRFQUFDa0U7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVywyQkFBeUUsT0FBOUNuRSxZQUFZLG1CQUFtQjs4Q0FDdkVBLFlBQVksZUFBZTs7Ozs7OzhDQUU5Qiw4REFBQ2tFO29DQUFJQyxXQUFVOzt3Q0FBMEI7c0RBQ3ZCLDhEQUFDZ0I7c0RBQVEvQixlQUFldkMsSUFBSTs7Ozs7Ozs7Ozs7OzhDQUU5Qyw4REFBQ3FEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQVlmLGVBQWVnQyxPQUFPOzs7Ozs7c0RBQ2pELDhEQUFDbEI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7d0RBQTJCO3dEQUFVZixlQUFlNEIsT0FBTzs7Ozs7Ozs4REFDMUUsOERBQUNkO29EQUFJQyxXQUFVOzt3REFBZ0I7d0RBQVNmLGVBQWUyQixNQUFNOzs7Ozs7OzhEQUM3RCw4REFBQ2I7b0RBQUlDLFdBQVU7O3dEQUFnQjt3REFBYWYsZUFBZWlDLFVBQVUsQ0FBQ0MsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FHdkZ0RiwyQkFDQyw4REFBQ2tFO29DQUFJQyxXQUFVOzt3Q0FBZ0M7d0NBQzNDMUMsS0FBS2tDLEtBQUssQ0FBQyxDQUFDUCxlQUFldkMsSUFBSSxDQUFDQyxPQUFPLENBQUMsUUFBUSxJQUFJQyxNQUFNLEdBQUdVLEtBQUtpQyxHQUFHLENBQUMsR0FBR2pDLEtBQUtrQyxLQUFLLENBQUMsQ0FBQyxLQUFLakYsTUFBTSxDQUFDRixhQUFhLENBQUMyRCxTQUFTLElBQUksR0FBRSxJQUFNLEtBQUsvQyxTQUFTLEdBQUcsSUFBS21CLHdCQUF3QmpCO3dDQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTNU1JLDhCQUNDLDhEQUFDd0U7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNaakYsU0FBUyxNQUFNLE9BQU9BLFNBQVMsTUFBTSxPQUFPOzs7Ozs7MENBRy9DLDhEQUFDZ0Y7O2tEQUNDLDhEQUFDSTt3Q0FBR0gsV0FBVTtrREFBMEM7Ozs7OztrREFHeEQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7O29EQUFFO2tFQUFhLDhEQUFDa0I7d0RBQUtwQixXQUFVO2tFQUE2QmpGOzs7Ozs7Ozs7Ozs7MERBQzdELDhEQUFDbUY7O29EQUFFO2tFQUFhLDhEQUFDa0I7d0RBQUtwQixXQUFVO2tFQUFrQjFDLEtBQUtpQyxHQUFHLElBQUloRixPQUFPMkMsR0FBRyxDQUFDLENBQUNtRSxHQUFHQyxJQUFNQSxLQUFLakgsZUFBZVksU0FBUzs7Ozs7Ozs7Ozs7OzBEQUNoSCw4REFBQ2lGOztvREFBRTtrRUFBWSw4REFBQ2tCO3dEQUFLcEIsV0FBVTtrRUFBOEI3RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUlqRSw4REFBQzRFO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDSTtvQ0FDQ0MsU0FBU2hDO29DQUNUMkIsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0dBbmNNOUY7S0FBQUE7QUFxY04saUVBQWVBLG1CQUFtQkEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNRUVLIEVERU5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcR0FNRVMgRk9SIEFSSUNBXFxnYW1lcy1mb3ItYWZyaWNhXFxzcmNcXGNvbXBvbmVudHNcXGdhbWVzXFxDb3VudHJ5TmFtZVNjcmFtYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ291bnRyeSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgc2h1ZmZsZUFycmF5IH0gZnJvbSAnQC91dGlscyc7XG5pbXBvcnQgRmxhZ0ltYWdlIGZyb20gJ0AvY29tcG9uZW50cy91aS9GbGFnSW1hZ2UnO1xuXG5pbnRlcmZhY2UgQ291bnRyeU5hbWVTY3JhbWJsZVByb3BzIHtcbiAgY291bnRyaWVzOiBDb3VudHJ5W107XG4gIG9uQ29tcGxldGU6IChzY29yZTogbnVtYmVyKSA9PiB2b2lkO1xufVxuXG5pbnRlcmZhY2UgU2NyYW1ibGVkTGV0dGVyIHtcbiAgaWQ6IHN0cmluZztcbiAgbGV0dGVyOiBzdHJpbmc7XG4gIG9yaWdpbmFsSW5kZXg6IG51bWJlcjtcbiAgaXNVc2VkOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgR2FtZVJvdW5kIHtcbiAgY291bnRyeTogQ291bnRyeTtcbiAgc2NyYW1ibGVkTGV0dGVyczogU2NyYW1ibGVkTGV0dGVyW107XG4gIHVzZXJBbnN3ZXI6IHN0cmluZztcbiAgaXNDb21wbGV0ZTogYm9vbGVhbjtcbiAgdGltZVNwZW50OiBudW1iZXI7XG59XG5cbnR5cGUgRGlmZmljdWx0eUxldmVsID0gJ2JlZ2lubmVyJyB8ICdpbnRlcm1lZGlhdGUnIHwgJ2FkdmFuY2VkJztcblxuY29uc3QgQ291bnRyeU5hbWVTY3JhbWJsZTogUmVhY3QuRkM8Q291bnRyeU5hbWVTY3JhbWJsZVByb3BzPiA9ICh7IGNvdW50cmllcywgb25Db21wbGV0ZSB9KSA9PiB7XG4gIGNvbnN0IFtjdXJyZW50Um91bmQsIHNldEN1cnJlbnRSb3VuZF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3JvdW5kcywgc2V0Um91bmRzXSA9IHVzZVN0YXRlPEdhbWVSb3VuZFtdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZExldHRlcnMsIHNldFNlbGVjdGVkTGV0dGVyc10gPSB1c2VTdGF0ZTxTY3JhbWJsZWRMZXR0ZXJbXT4oW10pO1xuICBjb25zdCBbdXNlckFuc3dlciwgc2V0VXNlckFuc3dlcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt0aW1lTGVmdCwgc2V0VGltZUxlZnRdID0gdXNlU3RhdGUoNjApO1xuICBjb25zdCBbc2NvcmUsIHNldFNjb3JlXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbc3RyZWFrLCBzZXRTdHJlYWtdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtkaWZmaWN1bHR5LCBzZXREaWZmaWN1bHR5XSA9IHVzZVN0YXRlPERpZmZpY3VsdHlMZXZlbD4oJ2JlZ2lubmVyJyk7XG4gIGNvbnN0IFtnYW1lU3RhcnRlZCwgc2V0R2FtZVN0YXJ0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZ2FtZUNvbXBsZXRlLCBzZXRHYW1lQ29tcGxldGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0hpbnQsIHNldFNob3dIaW50XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dSZXN1bHQsIHNldFNob3dSZXN1bHRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNDb3JyZWN0LCBzZXRJc0NvcnJlY3RdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcm91bmRTdGFydFRpbWUsIHNldFJvdW5kU3RhcnRUaW1lXSA9IHVzZVN0YXRlKERhdGUubm93KCkpO1xuXG4gIGNvbnN0IHRvdGFsUm91bmRzID0gMTA7XG5cbiAgY29uc3QgZ2V0RGlmZmljdWx0eU11bHRpcGxpZXIgPSAoZGlmZjogRGlmZmljdWx0eUxldmVsKTogbnVtYmVyID0+IHtcbiAgICBzd2l0Y2ggKGRpZmYpIHtcbiAgICAgIGNhc2UgJ2JlZ2lubmVyJzogcmV0dXJuIDE7XG4gICAgICBjYXNlICdpbnRlcm1lZGlhdGUnOiByZXR1cm4gMS41O1xuICAgICAgY2FzZSAnYWR2YW5jZWQnOiByZXR1cm4gMjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmlsdGVyQ291bnRyaWVzQnlEaWZmaWN1bHR5ID0gKGNvdW50cmllczogQ291bnRyeVtdLCBkaWZmaWN1bHR5OiBEaWZmaWN1bHR5TGV2ZWwpOiBDb3VudHJ5W10gPT4ge1xuICAgIHJldHVybiBjb3VudHJpZXMuZmlsdGVyKGNvdW50cnkgPT4ge1xuICAgICAgY29uc3QgbmFtZUxlbmd0aCA9IGNvdW50cnkubmFtZS5yZXBsYWNlKC9cXHMrL2csICcnKS5sZW5ndGg7XG4gICAgICBzd2l0Y2ggKGRpZmZpY3VsdHkpIHtcbiAgICAgICAgY2FzZSAnYmVnaW5uZXInOiByZXR1cm4gbmFtZUxlbmd0aCA+PSA0ICYmIG5hbWVMZW5ndGggPD0gNjtcbiAgICAgICAgY2FzZSAnaW50ZXJtZWRpYXRlJzogcmV0dXJuIG5hbWVMZW5ndGggPj0gNyAmJiBuYW1lTGVuZ3RoIDw9IDEwO1xuICAgICAgICBjYXNlICdhZHZhbmNlZCc6IHJldHVybiBuYW1lTGVuZ3RoID49IDExO1xuICAgICAgICBkZWZhdWx0OiByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBzY3JhbWJsZUNvdW50cnlOYW1lID0gKG5hbWU6IHN0cmluZyk6IFNjcmFtYmxlZExldHRlcltdID0+IHtcbiAgICBjb25zdCBjbGVhbk5hbWUgPSBuYW1lLnJlcGxhY2UoL1xccysvZywgJycpLnRvVXBwZXJDYXNlKCk7XG4gICAgY29uc3QgbGV0dGVycyA9IGNsZWFuTmFtZS5zcGxpdCgnJykubWFwKChsZXR0ZXIsIGluZGV4KSA9PiAoe1xuICAgICAgaWQ6IGBsZXR0ZXItJHtpbmRleH0tJHtNYXRoLnJhbmRvbSgpfWAsXG4gICAgICBsZXR0ZXIsXG4gICAgICBvcmlnaW5hbEluZGV4OiBpbmRleCxcbiAgICAgIGlzVXNlZDogZmFsc2UsXG4gICAgfSkpO1xuICAgIHJldHVybiBzaHVmZmxlQXJyYXkobGV0dGVycyk7XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVSb3VuZHMgPSAoKTogR2FtZVJvdW5kW10gPT4ge1xuICAgIGNvbnN0IGZpbHRlcmVkQ291bnRyaWVzID0gZmlsdGVyQ291bnRyaWVzQnlEaWZmaWN1bHR5KGNvdW50cmllcywgZGlmZmljdWx0eSk7XG4gICAgY29uc3Qgc2VsZWN0ZWRDb3VudHJpZXMgPSBzaHVmZmxlQXJyYXkoZmlsdGVyZWRDb3VudHJpZXMpLnNsaWNlKDAsIHRvdGFsUm91bmRzKTtcbiAgICBcbiAgICByZXR1cm4gc2VsZWN0ZWRDb3VudHJpZXMubWFwKGNvdW50cnkgPT4gKHtcbiAgICAgIGNvdW50cnksXG4gICAgICBzY3JhbWJsZWRMZXR0ZXJzOiBzY3JhbWJsZUNvdW50cnlOYW1lKGNvdW50cnkubmFtZSksXG4gICAgICB1c2VyQW5zd2VyOiAnJyxcbiAgICAgIGlzQ29tcGxldGU6IGZhbHNlLFxuICAgICAgdGltZVNwZW50OiAwLFxuICAgIH0pKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChnYW1lU3RhcnRlZCAmJiB0aW1lTGVmdCA+IDAgJiYgIWdhbWVDb21wbGV0ZSkge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHNldFRpbWVMZWZ0KHRpbWVMZWZ0IC0gMSksIDEwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfSBlbHNlIGlmICh0aW1lTGVmdCA9PT0gMCkge1xuICAgICAgaGFuZGxlR2FtZUVuZCgpO1xuICAgIH1cbiAgfSwgW2dhbWVTdGFydGVkLCB0aW1lTGVmdCwgZ2FtZUNvbXBsZXRlXSk7XG5cbiAgY29uc3Qgc3RhcnRHYW1lID0gKCkgPT4ge1xuICAgIGNvbnN0IG5ld1JvdW5kcyA9IGdlbmVyYXRlUm91bmRzKCk7XG4gICAgc2V0Um91bmRzKG5ld1JvdW5kcyk7XG4gICAgc2V0Q3VycmVudFJvdW5kKDApO1xuICAgIHNldFNjb3JlKDApO1xuICAgIHNldFN0cmVhaygwKTtcbiAgICBzZXRUaW1lTGVmdCg2MCk7XG4gICAgc2V0VXNlckFuc3dlcignJyk7XG4gICAgc2V0U2VsZWN0ZWRMZXR0ZXJzKFtdKTtcbiAgICBzZXRHYW1lU3RhcnRlZCh0cnVlKTtcbiAgICBzZXRHYW1lQ29tcGxldGUoZmFsc2UpO1xuICAgIHNldFNob3dSZXN1bHQoZmFsc2UpO1xuICAgIHNldFJvdW5kU3RhcnRUaW1lKERhdGUubm93KCkpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUxldHRlckNsaWNrID0gKGxldHRlcjogU2NyYW1ibGVkTGV0dGVyKSA9PiB7XG4gICAgaWYgKGxldHRlci5pc1VzZWQgfHwgc2hvd1Jlc3VsdCkgcmV0dXJuO1xuXG4gICAgY29uc3QgbmV3U2VsZWN0ZWRMZXR0ZXJzID0gWy4uLnNlbGVjdGVkTGV0dGVycywgeyAuLi5sZXR0ZXIsIGlzVXNlZDogdHJ1ZSB9XTtcbiAgICBzZXRTZWxlY3RlZExldHRlcnMobmV3U2VsZWN0ZWRMZXR0ZXJzKTtcbiAgICBzZXRVc2VyQW5zd2VyKG5ld1NlbGVjdGVkTGV0dGVycy5tYXAobCA9PiBsLmxldHRlcikuam9pbignJykpO1xuXG4gICAgLy8gVXBkYXRlIHRoZSBzY3JhbWJsZWQgbGV0dGVycyB0byBtYXJrIHRoaXMgb25lIGFzIHVzZWRcbiAgICBjb25zdCBjdXJyZW50Um91bmREYXRhID0gcm91bmRzW2N1cnJlbnRSb3VuZF07XG4gICAgY29uc3QgdXBkYXRlZFNjcmFtYmxlZExldHRlcnMgPSBjdXJyZW50Um91bmREYXRhLnNjcmFtYmxlZExldHRlcnMubWFwKGwgPT5cbiAgICAgIGwuaWQgPT09IGxldHRlci5pZCA/IHsgLi4ubCwgaXNVc2VkOiB0cnVlIH0gOiBsXG4gICAgKTtcbiAgICBcbiAgICBjb25zdCB1cGRhdGVkUm91bmRzID0gWy4uLnJvdW5kc107XG4gICAgdXBkYXRlZFJvdW5kc1tjdXJyZW50Um91bmRdID0ge1xuICAgICAgLi4uY3VycmVudFJvdW5kRGF0YSxcbiAgICAgIHNjcmFtYmxlZExldHRlcnM6IHVwZGF0ZWRTY3JhbWJsZWRMZXR0ZXJzLFxuICAgIH07XG4gICAgc2V0Um91bmRzKHVwZGF0ZWRSb3VuZHMpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNsZWFyQW5zd2VyID0gKCkgPT4ge1xuICAgIGlmIChzaG93UmVzdWx0KSByZXR1cm47XG5cbiAgICBzZXRTZWxlY3RlZExldHRlcnMoW10pO1xuICAgIHNldFVzZXJBbnN3ZXIoJycpO1xuICAgIFxuICAgIC8vIFJlc2V0IGFsbCBsZXR0ZXJzIHRvIHVudXNlZFxuICAgIGNvbnN0IGN1cnJlbnRSb3VuZERhdGEgPSByb3VuZHNbY3VycmVudFJvdW5kXTtcbiAgICBjb25zdCByZXNldFNjcmFtYmxlZExldHRlcnMgPSBjdXJyZW50Um91bmREYXRhLnNjcmFtYmxlZExldHRlcnMubWFwKGwgPT4gKHsgLi4ubCwgaXNVc2VkOiBmYWxzZSB9KSk7XG4gICAgXG4gICAgY29uc3QgdXBkYXRlZFJvdW5kcyA9IFsuLi5yb3VuZHNdO1xuICAgIHVwZGF0ZWRSb3VuZHNbY3VycmVudFJvdW5kXSA9IHtcbiAgICAgIC4uLmN1cnJlbnRSb3VuZERhdGEsXG4gICAgICBzY3JhbWJsZWRMZXR0ZXJzOiByZXNldFNjcmFtYmxlZExldHRlcnMsXG4gICAgfTtcbiAgICBzZXRSb3VuZHModXBkYXRlZFJvdW5kcyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0QW5zd2VyID0gKCkgPT4ge1xuICAgIGlmICghdXNlckFuc3dlciB8fCBzaG93UmVzdWx0KSByZXR1cm47XG5cbiAgICBjb25zdCBjdXJyZW50Q291bnRyeSA9IHJvdW5kc1tjdXJyZW50Um91bmRdLmNvdW50cnk7XG4gICAgY29uc3QgY29ycmVjdEFuc3dlciA9IGN1cnJlbnRDb3VudHJ5Lm5hbWUucmVwbGFjZSgvXFxzKy9nLCAnJykudG9VcHBlckNhc2UoKTtcbiAgICBjb25zdCB1c2VyQW5zd2VyQ2xlYW4gPSB1c2VyQW5zd2VyLnJlcGxhY2UoL1xccysvZywgJycpLnRvVXBwZXJDYXNlKCk7XG4gICAgY29uc3QgY29ycmVjdCA9IHVzZXJBbnN3ZXJDbGVhbiA9PT0gY29ycmVjdEFuc3dlcjtcbiAgICBcbiAgICBzZXRJc0NvcnJlY3QoY29ycmVjdCk7XG4gICAgc2V0U2hvd1Jlc3VsdCh0cnVlKTtcblxuICAgIGNvbnN0IHRpbWVTcGVudCA9IChEYXRlLm5vdygpIC0gcm91bmRTdGFydFRpbWUpIC8gMTAwMDtcbiAgICBcbiAgICBpZiAoY29ycmVjdCkge1xuICAgICAgY29uc3QgYmFzZVBvaW50cyA9IGNvcnJlY3RBbnN3ZXIubGVuZ3RoO1xuICAgICAgY29uc3QgdGltZUJvbnVzID0gTWF0aC5tYXgoMCwgTWF0aC5mbG9vcigoMTAgLSB0aW1lU3BlbnQpICogMikpO1xuICAgICAgY29uc3Qgc3RyZWFrTXVsdGlwbGllciA9IDEgKyAoc3RyZWFrICogMC4xKTtcbiAgICAgIGNvbnN0IGRpZmZpY3VsdHlNdWx0aXBsaWVyID0gZ2V0RGlmZmljdWx0eU11bHRpcGxpZXIoZGlmZmljdWx0eSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHJvdW5kU2NvcmUgPSBNYXRoLmZsb29yKChiYXNlUG9pbnRzICsgdGltZUJvbnVzKSAqIHN0cmVha011bHRpcGxpZXIgKiBkaWZmaWN1bHR5TXVsdGlwbGllcik7XG4gICAgICBzZXRTY29yZShzY29yZSArIHJvdW5kU2NvcmUpO1xuICAgICAgc2V0U3RyZWFrKHN0cmVhayArIDEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTdHJlYWsoMCk7XG4gICAgfVxuXG4gICAgLy8gVXBkYXRlIHJvdW5kIGRhdGFcbiAgICBjb25zdCB1cGRhdGVkUm91bmRzID0gWy4uLnJvdW5kc107XG4gICAgdXBkYXRlZFJvdW5kc1tjdXJyZW50Um91bmRdID0ge1xuICAgICAgLi4udXBkYXRlZFJvdW5kc1tjdXJyZW50Um91bmRdLFxuICAgICAgdXNlckFuc3dlcixcbiAgICAgIGlzQ29tcGxldGU6IHRydWUsXG4gICAgICB0aW1lU3BlbnQsXG4gICAgfTtcbiAgICBzZXRSb3VuZHModXBkYXRlZFJvdW5kcyk7XG5cbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGlmIChjdXJyZW50Um91bmQgPCB0b3RhbFJvdW5kcyAtIDEpIHtcbiAgICAgICAgc2V0Q3VycmVudFJvdW5kKGN1cnJlbnRSb3VuZCArIDEpO1xuICAgICAgICBzZXRVc2VyQW5zd2VyKCcnKTtcbiAgICAgICAgc2V0U2VsZWN0ZWRMZXR0ZXJzKFtdKTtcbiAgICAgICAgc2V0U2hvd1Jlc3VsdChmYWxzZSk7XG4gICAgICAgIHNldFNob3dIaW50KGZhbHNlKTtcbiAgICAgICAgc2V0Um91bmRTdGFydFRpbWUoRGF0ZS5ub3coKSk7XG4gICAgICAgIFxuICAgICAgICAvLyBSZXNldCBsZXR0ZXJzIGZvciBuZXh0IHJvdW5kXG4gICAgICAgIGNvbnN0IG5leHRSb3VuZERhdGEgPSB1cGRhdGVkUm91bmRzW2N1cnJlbnRSb3VuZCArIDFdO1xuICAgICAgICBjb25zdCByZXNldExldHRlcnMgPSBuZXh0Um91bmREYXRhLnNjcmFtYmxlZExldHRlcnMubWFwKGwgPT4gKHsgLi4ubCwgaXNVc2VkOiBmYWxzZSB9KSk7XG4gICAgICAgIHVwZGF0ZWRSb3VuZHNbY3VycmVudFJvdW5kICsgMV0gPSB7XG4gICAgICAgICAgLi4ubmV4dFJvdW5kRGF0YSxcbiAgICAgICAgICBzY3JhbWJsZWRMZXR0ZXJzOiByZXNldExldHRlcnMsXG4gICAgICAgIH07XG4gICAgICAgIHNldFJvdW5kcyh1cGRhdGVkUm91bmRzKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGhhbmRsZUdhbWVFbmQoKTtcbiAgICAgIH1cbiAgICB9LCAzMDAwKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVHYW1lRW5kID0gKCkgPT4ge1xuICAgIHNldEdhbWVDb21wbGV0ZSh0cnVlKTtcbiAgICBzZXRUaW1lb3V0KCgpID0+IG9uQ29tcGxldGUoc2NvcmUpLCAxMDAwKTtcbiAgfTtcblxuICBjb25zdCB0b2dnbGVIaW50ID0gKCkgPT4ge1xuICAgIHNldFNob3dIaW50KCFzaG93SGludCk7XG4gIH07XG5cbiAgaWYgKCFnYW1lU3RhcnRlZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLThcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPvCflKQgQ291bnRyeSBOYW1lIFNjcmFtYmxlPC9oMj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTZcIj7wn6epPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktMzAwIG1iLTZcIj5cbiAgICAgICAgICAgIFVuc2NyYW1ibGUgdGhlIGxldHRlcnMgdG8gZm9ybSBBZnJpY2FuIGNvdW50cnkgbmFtZXMhXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBEaWZmaWN1bHR5IFNlbGVjdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+Q2hvb3NlIERpZmZpY3VsdHk6PC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICB7KFsnYmVnaW5uZXInLCAnaW50ZXJtZWRpYXRlJywgJ2FkdmFuY2VkJ10gYXMgRGlmZmljdWx0eUxldmVsW10pLm1hcCgoZGlmZikgPT4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17ZGlmZn1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldERpZmZpY3VsdHkoZGlmZil9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTMgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBkaWZmaWN1bHR5ID09PSBkaWZmXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmcteWVsbG93LTQwMCB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktNzAwIHRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBjYXBpdGFsaXplXCI+e2RpZmZ9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtkaWZmID09PSAnYmVnaW5uZXInICYmICc0LTYgbGV0dGVycyd9XG4gICAgICAgICAgICAgICAgICAgICAge2RpZmYgPT09ICdpbnRlcm1lZGlhdGUnICYmICc3LTEwIGxldHRlcnMnfVxuICAgICAgICAgICAgICAgICAgICAgIHtkaWZmID09PSAnYWR2YW5jZWQnICYmICcxMSsgbGV0dGVycyd9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCB0ZXh0LWdyYXktMzAwIG1iLThcIj5cbiAgICAgICAgICAgIDxwPuKAoiBVbnNjcmFtYmxlIGxldHRlcnMgdG8gZm9ybSBjb3VudHJ5IG5hbWVzPC9wPlxuICAgICAgICAgICAgPHA+4oCiIExvbmdlciBuYW1lcyA9IG1vcmUgcG9pbnRzPC9wPlxuICAgICAgICAgICAgPHA+4oCiIEJ1aWxkIHN0cmVha3MgZm9yIGJvbnVzIG11bHRpcGxpZXJzPC9wPlxuICAgICAgICAgICAgPHA+4oCiIFVzZSBoaW50cyB0byBzZWUgZmxhZ3MgYW5kIHJlZ2lvbnM8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17c3RhcnRHYW1lfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmcteWVsbG93LTQwMCB0ZXh0LWdyYXktOTAwIHB5LTMgcHgtOCByb3VuZGVkLWxnIHRleHQteGwgZm9udC1ib2xkIGhvdmVyOmJnLXllbGxvdy0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIPCfmoAgU3RhcnQgU2NyYW1ibGluZ1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoIXJvdW5kc1tjdXJyZW50Um91bmRdKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLXllbGxvdy00MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBjb25zdCBjdXJyZW50Q291bnRyeSA9IHJvdW5kc1tjdXJyZW50Um91bmRdLmNvdW50cnk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHAtNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNiBtYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj7wn5SkIENvdW50cnkgTmFtZSBTY3JhbWJsZTwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC14bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgIHtNYXRoLmZsb29yKHRpbWVMZWZ0IC8gNjApfTp7KHRpbWVMZWZ0ICUgNjApLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTUgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNDAwXCI+e3Njb3JlfTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5TY29yZTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTQwMFwiPntzdHJlYWt9PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlN0cmVhazwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+e2N1cnJlbnRSb3VuZCArIDF9PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlJvdW5kPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTQwMFwiPnt0b3RhbFJvdW5kc308L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+VG90YWw8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtNDAwIGNhcGl0YWxpemVcIj57ZGlmZmljdWx0eX08L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+RGlmZmljdWx0eTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUHJvZ3Jlc3MgQmFyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXllbGxvdy00MDAgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7KChjdXJyZW50Um91bmQgKyAxKSAvIHRvdGFsUm91bmRzKSAqIDEwMH0lYCB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEdhbWUgQXJlYSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNiBtYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICBVbnNjcmFtYmxlIHRoaXMgQWZyaWNhbiBjb3VudHJ5IG5hbWU6XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogSGludCBTZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVIaW50fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3Nob3dIaW50ID8gJ0hpZGUgSGludCcgOiAnU2hvdyBIaW50J30g8J+SoVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtzaG93SGludCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBiZy1ibHVlLTUwMCBiZy1vcGFjaXR5LTEwIGJvcmRlciBib3JkZXItYmx1ZS00MDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPEZsYWdJbWFnZVxuICAgICAgICAgICAgICAgICAgICBjb3VudHJ5SWQ9e2N1cnJlbnRDb3VudHJ5LmlkfVxuICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTQwMCBmb250LXNlbWlib2xkXCI+UmVnaW9uOiB7Y3VycmVudENvdW50cnkucmVnaW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbVwiPkNhcGl0YWw6IHtjdXJyZW50Q291bnRyeS5jYXBpdGFsfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFNjcmFtYmxlZCBMZXR0ZXJzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNCB0ZXh0LWNlbnRlclwiPkF2YWlsYWJsZSBMZXR0ZXJzOjwvaDQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAge3JvdW5kc1tjdXJyZW50Um91bmRdLnNjcmFtYmxlZExldHRlcnMubWFwKChsZXR0ZXIpID0+IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17bGV0dGVyLmlkfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUxldHRlckNsaWNrKGxldHRlcil9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xldHRlci5pc1VzZWQgfHwgc2hvd1Jlc3VsdH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTEyIGgtMTIgcm91bmRlZC1sZyBmb250LWJvbGQgdGV4dC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgIGxldHRlci5pc1VzZWQgXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYXktNjAwIHRleHQtZ3JheS00MDAgY3Vyc29yLW5vdC1hbGxvd2VkJyBcbiAgICAgICAgICAgICAgICAgICAgOiAnYmcteWVsbG93LTQwMCB0ZXh0LWdyYXktOTAwIGhvdmVyOmJnLXllbGxvdy0zMDAgY3Vyc29yLXBvaW50ZXIgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtsZXR0ZXIubGV0dGVyfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVXNlciBBbnN3ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00IHRleHQtY2VudGVyXCI+WW91ciBBbnN3ZXI6PC9oND5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBwLTQgbWluLWgtWzYwcHhdIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgIHt1c2VyQW5zd2VyIHx8ICdDbGljayBsZXR0ZXJzIHRvIGJ1aWxkIHlvdXIgYW5zd2VyLi4uJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGVhckFuc3dlcn1cbiAgICAgICAgICAgIGRpc2FibGVkPXtzaG93UmVzdWx0fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLXJlZC02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsZWFyXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3VibWl0QW5zd2VyfVxuICAgICAgICAgICAgZGlzYWJsZWQ9eyF1c2VyQW5zd2VyIHx8IHNob3dSZXN1bHR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmVlbi02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFN1Ym1pdCBBbnN3ZXJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJlc3VsdCBEaXNwbGF5ICovfVxuICAgICAgICB7c2hvd1Jlc3VsdCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtdC02IHAtNCByb3VuZGVkLWxnIGJvcmRlciAke1xuICAgICAgICAgICAgaXNDb3JyZWN0IFxuICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAgYmctb3BhY2l0eS0xMCBib3JkZXItZ3JlZW4tNDAwJyBcbiAgICAgICAgICAgICAgOiAnYmctcmVkLTUwMCBiZy1vcGFjaXR5LTEwIGJvcmRlci1yZWQtNDAwJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmb250LWJvbGQgbWItMiAke2lzQ29ycmVjdCA/ICd0ZXh0LWdyZWVuLTQwMCcgOiAndGV4dC1yZWQtNDAwJ31gfT5cbiAgICAgICAgICAgICAgICB7aXNDb3JyZWN0ID8gJ+KckyBDb3JyZWN0IScgOiAn4pyXIEluY29ycmVjdCd9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1sZyBtYi0yXCI+XG4gICAgICAgICAgICAgICAgVGhlIGFuc3dlciB3YXM6IDxzdHJvbmc+e2N1cnJlbnRDb3VudHJ5Lm5hbWV9PC9zdHJvbmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bFwiPntjdXJyZW50Q291bnRyeS5mbGFnVXJsfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPkNhcGl0YWw6IHtjdXJyZW50Q291bnRyeS5jYXBpdGFsfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+UmVnaW9uOiB7Y3VycmVudENvdW50cnkucmVnaW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+UG9wdWxhdGlvbjoge2N1cnJlbnRDb3VudHJ5LnBvcHVsYXRpb24udG9Mb2NhbGVTdHJpbmcoKX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIHtpc0NvcnJlY3QgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgICt7TWF0aC5mbG9vcigoY3VycmVudENvdW50cnkubmFtZS5yZXBsYWNlKC9cXHMrL2csICcnKS5sZW5ndGggKyBNYXRoLm1heCgwLCBNYXRoLmZsb29yKCgxMCAtIHJvdW5kc1tjdXJyZW50Um91bmRdLnRpbWVTcGVudCkgKiAyKSkpICogKDEgKyAoc3RyZWFrICogMC4xKSkgKiBnZXREaWZmaWN1bHR5TXVsdGlwbGllcihkaWZmaWN1bHR5KSl9IHBvaW50cyFcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBHYW1lIENvbXBsZXRlIE1vZGFsICovfVxuICAgICAge2dhbWVDb21wbGV0ZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCBiZy1ibGFjayBiZy1vcGFjaXR5LTc1XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHAtNiBtYXgtdy1tZCB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bFwiPlxuICAgICAgICAgICAgICAgIHtzY29yZSA+PSAyMDAgPyAn8J+PhicgOiBzY29yZSA+PSAxNTAgPyAn8J+OiScgOiAn8J+UpCd9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQteWVsbG93LTQwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBHYW1lIENvbXBsZXRlIVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgPHA+RmluYWwgU2NvcmU6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCBmb250LWJvbGRcIj57c2NvcmV9PC9zcGFuPjwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPkJlc3QgU3RyZWFrOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMFwiPntNYXRoLm1heCguLi5yb3VuZHMubWFwKChfLCBpKSA9PiBpIDw9IGN1cnJlbnRSb3VuZCA/IHN0cmVhayA6IDApKX08L3NwYW4+PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+RGlmZmljdWx0eTogPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtNDAwIGNhcGl0YWxpemVcIj57ZGlmZmljdWx0eX08L3NwYW4+PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3N0YXJ0R2FtZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXllbGxvdy00MDAgdGV4dC1ncmF5LTkwMCBweS0yIHB4LTQgcm91bmRlZC1sZyBob3ZlcjpiZy15ZWxsb3ctMzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDwn5SEIFBsYXkgQWdhaW5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ291bnRyeU5hbWVTY3JhbWJsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0Iiwic2h1ZmZsZUFycmF5IiwiRmxhZ0ltYWdlIiwiQ291bnRyeU5hbWVTY3JhbWJsZSIsImNvdW50cmllcyIsIm9uQ29tcGxldGUiLCJjdXJyZW50Um91bmQiLCJzZXRDdXJyZW50Um91bmQiLCJyb3VuZHMiLCJzZXRSb3VuZHMiLCJzZWxlY3RlZExldHRlcnMiLCJzZXRTZWxlY3RlZExldHRlcnMiLCJ1c2VyQW5zd2VyIiwic2V0VXNlckFuc3dlciIsInRpbWVMZWZ0Iiwic2V0VGltZUxlZnQiLCJzY29yZSIsInNldFNjb3JlIiwic3RyZWFrIiwic2V0U3RyZWFrIiwiZGlmZmljdWx0eSIsInNldERpZmZpY3VsdHkiLCJnYW1lU3RhcnRlZCIsInNldEdhbWVTdGFydGVkIiwiZ2FtZUNvbXBsZXRlIiwic2V0R2FtZUNvbXBsZXRlIiwic2hvd0hpbnQiLCJzZXRTaG93SGludCIsInNob3dSZXN1bHQiLCJzZXRTaG93UmVzdWx0IiwiaXNDb3JyZWN0Iiwic2V0SXNDb3JyZWN0Iiwicm91bmRTdGFydFRpbWUiLCJzZXRSb3VuZFN0YXJ0VGltZSIsIkRhdGUiLCJub3ciLCJ0b3RhbFJvdW5kcyIsImdldERpZmZpY3VsdHlNdWx0aXBsaWVyIiwiZGlmZiIsImZpbHRlckNvdW50cmllc0J5RGlmZmljdWx0eSIsImZpbHRlciIsImNvdW50cnkiLCJuYW1lTGVuZ3RoIiwibmFtZSIsInJlcGxhY2UiLCJsZW5ndGgiLCJzY3JhbWJsZUNvdW50cnlOYW1lIiwiY2xlYW5OYW1lIiwidG9VcHBlckNhc2UiLCJsZXR0ZXJzIiwic3BsaXQiLCJtYXAiLCJsZXR0ZXIiLCJpbmRleCIsImlkIiwiTWF0aCIsInJhbmRvbSIsIm9yaWdpbmFsSW5kZXgiLCJpc1VzZWQiLCJnZW5lcmF0ZVJvdW5kcyIsImZpbHRlcmVkQ291bnRyaWVzIiwic2VsZWN0ZWRDb3VudHJpZXMiLCJzbGljZSIsInNjcmFtYmxlZExldHRlcnMiLCJpc0NvbXBsZXRlIiwidGltZVNwZW50IiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlR2FtZUVuZCIsInN0YXJ0R2FtZSIsIm5ld1JvdW5kcyIsImhhbmRsZUxldHRlckNsaWNrIiwibmV3U2VsZWN0ZWRMZXR0ZXJzIiwibCIsImpvaW4iLCJjdXJyZW50Um91bmREYXRhIiwidXBkYXRlZFNjcmFtYmxlZExldHRlcnMiLCJ1cGRhdGVkUm91bmRzIiwiaGFuZGxlQ2xlYXJBbnN3ZXIiLCJyZXNldFNjcmFtYmxlZExldHRlcnMiLCJoYW5kbGVTdWJtaXRBbnN3ZXIiLCJjdXJyZW50Q291bnRyeSIsImNvcnJlY3RBbnN3ZXIiLCJ1c2VyQW5zd2VyQ2xlYW4iLCJjb3JyZWN0IiwiYmFzZVBvaW50cyIsInRpbWVCb251cyIsIm1heCIsImZsb29yIiwic3RyZWFrTXVsdGlwbGllciIsImRpZmZpY3VsdHlNdWx0aXBsaWVyIiwicm91bmRTY29yZSIsIm5leHRSb3VuZERhdGEiLCJyZXNldExldHRlcnMiLCJ0b2dnbGVIaW50IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwidG9TdHJpbmciLCJwYWRTdGFydCIsInN0eWxlIiwid2lkdGgiLCJjb3VudHJ5SWQiLCJzaXplIiwicmVnaW9uIiwiY2FwaXRhbCIsImg0IiwiZGlzYWJsZWQiLCJzdHJvbmciLCJmbGFnVXJsIiwicG9wdWxhdGlvbiIsInRvTG9jYWxlU3RyaW5nIiwic3BhbiIsIl8iLCJpIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ })

});