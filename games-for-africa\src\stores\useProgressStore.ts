import { create } from 'zustand';
import { UserProfile, Achievement } from '@/types/progress';
import { progressService } from '@/services/progressService';

interface ProgressState {
  profile: UserProfile | null;
  isLoading: boolean;
  
  // Actions
  loadProfile: () => void;
  updateUsername: (username: string) => void;
  recordGameCompletion: (
    gameId: string,
    score: number,
    completionTime: number,
    difficulty: 'beginner' | 'intermediate' | 'advanced',
    isPerfectScore?: boolean
  ) => Promise<{
    xpGained: number;
    levelUp: boolean;
    newAchievements: Achievement[];
  }>;
  updatePreferences: (preferences: Partial<UserProfile['preferences']>) => void;
  addFavoriteGame: (gameId: string) => void;
  removeFavoriteGame: (gameId: string) => void;
  resetProgress: () => void;
  exportProgress: () => string;
  importProgress: (data: string) => boolean;
}

export const useProgressStore = create<ProgressState>((set, get) => ({
  profile: null,
  isLoading: true,

  loadProfile: () => {
    set({ isLoading: true });
    try {
      const profile = progressService.getProfile();
      set({ profile, isLoading: false });
    } catch (error) {
      console.error('Error loading profile:', error);
      set({ isLoading: false });
    }
  },

  updateUsername: (username: string) => {
    progressService.updateUsername(username);
    const profile = progressService.getProfile();
    set({ profile });
  },

  recordGameCompletion: async (
    gameId: string,
    score: number,
    completionTime: number,
    difficulty: 'beginner' | 'intermediate' | 'advanced',
    isPerfectScore: boolean = false
  ) => {
    const result = progressService.recordGameCompletion(
      gameId,
      score,
      completionTime,
      difficulty,
      isPerfectScore
    );
    
    const profile = progressService.getProfile();
    set({ profile });
    
    return result;
  },

  updatePreferences: (preferences: Partial<UserProfile['preferences']>) => {
    progressService.updatePreferences(preferences);
    const profile = progressService.getProfile();
    set({ profile });
  },

  addFavoriteGame: (gameId: string) => {
    progressService.addFavoriteGame(gameId);
    const profile = progressService.getProfile();
    set({ profile });
  },

  removeFavoriteGame: (gameId: string) => {
    progressService.removeFavoriteGame(gameId);
    const profile = progressService.getProfile();
    set({ profile });
  },

  resetProgress: () => {
    progressService.resetProgress();
    const profile = progressService.getProfile();
    set({ profile });
  },

  exportProgress: () => {
    return progressService.exportProgress();
  },

  importProgress: (data: string) => {
    const success = progressService.importProgress(data);
    if (success) {
      const profile = progressService.getProfile();
      set({ profile });
    }
    return success;
  },
}));
