export interface GameProgress {
  gameId: string;
  completed: boolean;
  bestScore: number;
  totalAttempts: number;
  completionTime?: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  lastPlayed: string;
  perfectScore: boolean;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'completion' | 'score' | 'speed' | 'difficulty' | 'regional' | 'cultural';
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
}

export interface UserProfile {
  id: string;
  username: string;
  createdAt: string;
  lastActive: string;
  totalXP: number;
  level: number;
  gamesProgress: Record<string, GameProgress>;
  achievements: Achievement[];
  preferences: {
    theme: 'light' | 'dark';
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    soundEnabled: boolean;
    hintsEnabled: boolean;
    accessibilityMode: boolean;
  };
  statistics: {
    totalGamesPlayed: number;
    totalGamesCompleted: number;
    averageScore: number;
    totalPlayTime: number;
    favoriteGames: string[];
    streakDays: number;
    lastStreakDate: string;
  };
}

export interface DifficultySettings {
  beginner: {
    timeMultiplier: 1.5;
    hintsAvailable: 3;
    questionsReduced: 0.7;
    scoreMultiplier: 0.8;
  };
  intermediate: {
    timeMultiplier: 1;
    hintsAvailable: 1;
    questionsReduced: 1;
    scoreMultiplier: 1;
  };
  advanced: {
    timeMultiplier: 0.7;
    hintsAvailable: 0;
    questionsReduced: 1.3;
    scoreMultiplier: 1.5;
  };
}

export interface LevelSystem {
  level: number;
  xpRequired: number;
  title: string;
  rewards: string[];
}

export const LEVEL_SYSTEM: LevelSystem[] = [
  { level: 1, xpRequired: 0, title: "Explorer", rewards: ["Welcome Badge"] },
  { level: 2, xpRequired: 100, title: "Adventurer", rewards: ["First Steps Badge"] },
  { level: 3, xpRequired: 250, title: "Traveler", rewards: ["Country Expert Badge"] },
  { level: 4, xpRequired: 500, title: "Navigator", rewards: ["Regional Master Badge"] },
  { level: 5, xpRequired: 1000, title: "Geographer", rewards: ["Cultural Scholar Badge"] },
  { level: 6, xpRequired: 1750, title: "Cartographer", rewards: ["Speed Demon Badge"] },
  { level: 7, xpRequired: 2750, title: "Scholar", rewards: ["Perfect Score Badge"] },
  { level: 8, xpRequired: 4000, title: "Expert", rewards: ["Difficulty Master Badge"] },
  { level: 9, xpRequired: 6000, title: "Master", rewards: ["Achievement Hunter Badge"] },
  { level: 10, xpRequired: 10000, title: "Legend", rewards: ["Africa Expert Badge"] },
];

export const ACHIEVEMENTS_CATALOG: Omit<Achievement, 'unlockedAt' | 'progress'>[] = [
  {
    id: 'first_game',
    name: 'First Steps',
    description: 'Complete your first game',
    icon: '🎯',
    category: 'completion',
    maxProgress: 1,
  },
  {
    id: 'perfect_score',
    name: 'Perfectionist',
    description: 'Achieve a perfect score in any game',
    icon: '⭐',
    category: 'score',
    maxProgress: 1,
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Complete a game in under 2 minutes',
    icon: '⚡',
    category: 'speed',
    maxProgress: 1,
  },
  {
    id: 'all_games_completed',
    name: 'Game Master',
    description: 'Complete all 10 games',
    icon: '🏆',
    category: 'completion',
    maxProgress: 10,
  },
  {
    id: 'advanced_difficulty',
    name: 'Challenge Accepted',
    description: 'Complete a game on Advanced difficulty',
    icon: '🔥',
    category: 'difficulty',
    maxProgress: 1,
  },
  {
    id: 'north_africa_expert',
    name: 'North Africa Expert',
    description: 'Master all North African countries',
    icon: '🏺',
    category: 'regional',
    maxProgress: 6,
  },
  {
    id: 'west_africa_expert',
    name: 'West Africa Expert',
    description: 'Master all West African countries',
    icon: '🥁',
    category: 'regional',
    maxProgress: 16,
  },
  {
    id: 'east_africa_expert',
    name: 'East Africa Expert',
    description: 'Master all East African countries',
    icon: '🦁',
    category: 'regional',
    maxProgress: 10,
  },
  {
    id: 'central_africa_expert',
    name: 'Central Africa Expert',
    description: 'Master all Central African countries',
    icon: '🌳',
    category: 'regional',
    maxProgress: 8,
  },
  {
    id: 'southern_africa_expert',
    name: 'Southern Africa Expert',
    description: 'Master all Southern African countries',
    icon: '💎',
    category: 'regional',
    maxProgress: 10,
  },
  {
    id: 'cultural_scholar',
    name: 'Cultural Scholar',
    description: 'Learn about 25 different cultural elements',
    icon: '📚',
    category: 'cultural',
    maxProgress: 25,
  },
  {
    id: 'streak_week',
    name: 'Dedicated Learner',
    description: 'Play games for 7 consecutive days',
    icon: '🔥',
    category: 'completion',
    maxProgress: 7,
  },
];

export const XP_REWARDS = {
  GAME_COMPLETION: 50,
  PERFECT_SCORE: 25,
  FIRST_ATTEMPT_SUCCESS: 15,
  SPEED_BONUS: 10,
  DIFFICULTY_BONUS: {
    beginner: 0,
    intermediate: 10,
    advanced: 25,
  },
  ACHIEVEMENT_UNLOCK: 100,
  DAILY_STREAK: 20,
};
