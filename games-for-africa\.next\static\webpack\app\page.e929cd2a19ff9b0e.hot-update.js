"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n            // Check if puzzle is complete\n            if (placedPieces.length + 1 === pieces.length) {\n                setGameComplete(true);\n                const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n                const moveBonus = Math.max(0, 50 - moves);\n                const finalScore = score + 10 + timeBonus + moveBonus;\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 299,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl mb-1\",\n                                                            children: placedPiece.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl\",\n                                                            children: piece.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"\\uD83C\\uDFC6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Puzzle Master!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Time: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 491,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 371,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"akLakEg/BK2nvyLbrgu3pV4cVRk=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2dhbWVzL0ppZ3Nhd1B1enpsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVtRDtBQUVaO0FBbUJ2QyxNQUFNSSxlQUE0QztRQUFDLEVBQUVDLFNBQVMsRUFBRUMsVUFBVSxFQUFFOztJQUMxRSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR1AsK0NBQVFBLENBQWdCLEVBQUU7SUFDdEQsTUFBTSxDQUFDUSxjQUFjQyxnQkFBZ0IsR0FBR1QsK0NBQVFBLENBQWdCO0lBQ2hFLE1BQU0sQ0FBQ1UsY0FBY0MsZ0JBQWdCLEdBQUdYLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2MsT0FBT0MsU0FBUyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNnQixjQUFjQyxnQkFBZ0IsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2tCLGFBQWFDLGVBQWUsR0FBR25CLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3NCLGdCQUFnQkMsa0JBQWtCLEdBQUd2QiwrQ0FBUUEsQ0FBUztJQUU3RCxNQUFNd0IsVUFBVTtRQUFDO1FBQU87UUFBZ0I7UUFBZTtRQUFlO1FBQWtCO0tBQWtCO0lBRTFHLDZDQUE2QztJQUM3QyxNQUFNQyxlQUFlO1FBQ25CLGdCQUFnQjtRQUNoQixlQUFlO1FBQ2YsZUFBZTtRQUNmLGtCQUFrQjtRQUNsQixtQkFBbUI7SUFDckI7SUFFQXhCLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlpQixlQUFlLENBQUNGLGNBQWM7Z0JBQ2hDLE1BQU1VLFFBQVFDO29EQUFZO3dCQUN4Qk47NERBQWVPLENBQUFBLE9BQVFBLE9BQU87O29CQUNoQzttREFBRztnQkFDSDs4Q0FBTyxJQUFNQyxjQUFjSDs7WUFDN0I7UUFDRjtpQ0FBRztRQUFDUjtRQUFhRjtLQUFhO0lBRTlCLE1BQU1jLGlCQUFpQjtRQUNyQixJQUFJQyxvQkFBK0IsRUFBRTtRQUNyQyxJQUFJQyxXQUFXO1lBQUVDLE1BQU07WUFBR0MsTUFBTTtRQUFFLEdBQUcsb0JBQW9CO1FBRXpELElBQUlaLG1CQUFtQixPQUFPO1lBQzVCLDBFQUEwRTtZQUMxRSxNQUFNYSxjQUFjL0IsVUFBVWdDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsTUFBTSxLQUFLLGdCQUFnQkMsS0FBSyxDQUFDLEdBQUc7WUFDaEYsTUFBTUMsYUFBYXBDLFVBQVVnQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sS0FBSyxlQUFlQyxLQUFLLENBQUMsR0FBRztZQUM5RSxNQUFNRSxhQUFhckMsVUFBVWdDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsTUFBTSxLQUFLLGVBQWVDLEtBQUssQ0FBQyxHQUFHO1lBQzlFLE1BQU1HLGdCQUFnQnRDLFVBQVVnQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sS0FBSyxrQkFBa0JDLEtBQUssQ0FBQyxHQUFHO1lBQ3BGLE1BQU1JLGlCQUFpQnZDLFVBQVVnQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sS0FBSyxtQkFBbUJDLEtBQUssQ0FBQyxHQUFHO1lBRXRGUixvQkFBb0I7bUJBQUlJO21CQUFnQks7bUJBQWVDO21CQUFlQzttQkFBa0JDO2FBQWU7WUFDdkdYLFdBQVc7Z0JBQUVDLE1BQU07Z0JBQUdDLE1BQU07WUFBRSxHQUFHLGtDQUFrQztRQUNyRSxPQUFPO1lBQ0wsd0VBQXdFO1lBQ3hFSCxvQkFBb0IzQixVQUFVZ0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNLEtBQUtoQjtZQUV2RCxtQ0FBbUM7WUFDbkMsT0FBUUE7Z0JBQ04sS0FBSztvQkFDSFUsV0FBVzt3QkFBRUMsTUFBTTt3QkFBR0MsTUFBTTtvQkFBRSxHQUFHLGNBQWM7b0JBQy9DO2dCQUNGLEtBQUs7b0JBQ0hGLFdBQVc7d0JBQUVDLE1BQU07d0JBQUdDLE1BQU07b0JBQUUsR0FBRyxlQUFlO29CQUNoRDtnQkFDRixLQUFLO29CQUNIRixXQUFXO3dCQUFFQyxNQUFNO3dCQUFHQyxNQUFNO29CQUFFLEdBQUcsZUFBZTtvQkFDaEQ7Z0JBQ0YsS0FBSztvQkFDSEYsV0FBVzt3QkFBRUMsTUFBTTt3QkFBR0MsTUFBTTtvQkFBRSxHQUFHLGNBQWM7b0JBQy9DO2dCQUNGLEtBQUs7b0JBQ0hGLFdBQVc7d0JBQUVDLE1BQU07d0JBQUdDLE1BQU07b0JBQUUsR0FBRyxlQUFlO29CQUNoRDtZQUNKO1FBQ0Y7UUFFQSxvRUFBb0U7UUFDcEUsTUFBTVUsa0JBQThEO1lBQ2xFLGdCQUFnQjtnQkFDZDtvQkFBRUMsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTthQUNkO1lBQ0QsZUFBZTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTthQUNkO1lBQ0QsZUFBZTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTthQUNkO1lBQ0Qsa0JBQWtCO2dCQUNoQjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTthQUNkO1lBQ0QsbUJBQW1CO2dCQUNqQjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBRztvQkFBR0MsR0FBRztnQkFBRTthQUNkO1FBQ0g7UUFFQSxNQUFNQyxlQUE4QixFQUFFO1FBRXRDaEIsa0JBQWtCaUIsT0FBTyxDQUFDLENBQUNDLFNBQVNDO1lBQ2xDLElBQUlDO1lBRUosSUFBSTdCLG1CQUFtQixPQUFPO2dCQUM1QiwrQ0FBK0M7Z0JBQy9DLElBQUkyQixRQUFRWCxNQUFNLEtBQUssZ0JBQWdCO29CQUNyQ2EsYUFBYTt3QkFBRU4sR0FBR0ssUUFBUTt3QkFBR0osR0FBRztvQkFBRTtnQkFDcEMsT0FBTyxJQUFJRyxRQUFRWCxNQUFNLEtBQUssZUFBZTtvQkFDM0MsTUFBTWMsWUFBWUYsUUFBUSxHQUFHLG1DQUFtQztvQkFDaEVDLGFBQWE7d0JBQUVOLEdBQUdPLFlBQVk7d0JBQUdOLEdBQUc7b0JBQUU7Z0JBQ3hDLE9BQU8sSUFBSUcsUUFBUVgsTUFBTSxLQUFLLGVBQWU7b0JBQzNDLE1BQU1lLFlBQVlILFFBQVEsR0FBRywwQ0FBMEM7b0JBQ3ZFQyxhQUFhO3dCQUFFTixHQUFHUSxZQUFZO3dCQUFHUCxHQUFHO29CQUFFO2dCQUN4QyxPQUFPLElBQUlHLFFBQVFYLE1BQU0sS0FBSyxrQkFBa0I7b0JBQzlDLE1BQU1nQixlQUFlSixRQUFRLElBQUksNkJBQTZCO29CQUM5REMsYUFBYTt3QkFBRU4sR0FBR1MsZUFBZTt3QkFBR1IsR0FBRztvQkFBRTtnQkFDM0MsT0FBTztvQkFDTCxNQUFNUyxhQUFhTCxRQUFRLElBQUksNkJBQTZCO29CQUM1REMsYUFBYTt3QkFBRU4sR0FBR1UsYUFBYTt3QkFBR1QsR0FBRztvQkFBRTtnQkFDekM7WUFDRixPQUFPO2dCQUNMLHVEQUF1RDtnQkFDdkQsTUFBTVUsd0JBQXdCWixlQUFlLENBQUNLLFFBQVFYLE1BQU0sQ0FBQyxJQUFJLEVBQUU7Z0JBQ25FYSxhQUFhSyxxQkFBcUIsQ0FBQ04sTUFBTSxJQUFJO29CQUFFTCxHQUFHSyxRQUFRbEIsU0FBU0MsSUFBSTtvQkFBRWEsR0FBR1csS0FBS0MsS0FBSyxDQUFDUixRQUFRbEIsU0FBU0MsSUFBSTtnQkFBRTtZQUNoSDtZQUVBYyxhQUFhWSxJQUFJLENBQUM7Z0JBQ2hCQyxJQUFJLFNBQW9CLE9BQVhYLFFBQVFXLEVBQUU7Z0JBQ3ZCQyxXQUFXWixRQUFRVyxFQUFFO2dCQUNyQkUsYUFBYWIsUUFBUWMsSUFBSTtnQkFDekJDLE1BQU1mLFFBQVFnQixPQUFPO2dCQUNyQjNCLFFBQVFXLFFBQVFYLE1BQU07Z0JBQ3RCNEIsVUFBVTtvQkFBRXJCLEdBQUcsQ0FBQztvQkFBR0MsR0FBRyxDQUFDO2dCQUFFO2dCQUN6QnFCLFFBQVE7Z0JBQ1JDLGlCQUFpQmpCO1lBQ25CO1FBQ0Y7UUFFQSxPQUFPakQsb0RBQVlBLENBQUM2QztJQUN0QjtJQUVBLE1BQU1zQixZQUFZO1FBQ2hCLE1BQU1DLFlBQVl4QztRQUNsQnZCLFVBQVUrRDtRQUNWM0QsZ0JBQWdCLEVBQUU7UUFDbEJFLFNBQVM7UUFDVEUsU0FBUztRQUNUTSxlQUFlO1FBQ2ZKLGdCQUFnQjtRQUNoQkUsZUFBZTtJQUNqQjtJQUVBLE1BQU1vRCxrQkFBa0IsQ0FBQ0MsR0FBb0JDO1FBQzNDaEUsZ0JBQWdCZ0U7UUFDaEJELEVBQUVFLFlBQVksQ0FBQ0MsYUFBYSxHQUFHO0lBQ2pDO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNKO1FBQ3RCQSxFQUFFSyxjQUFjO1FBQ2hCTCxFQUFFRSxZQUFZLENBQUNJLFVBQVUsR0FBRztJQUM5QjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ1AsR0FBb0JRLFNBQWlCQztRQUN2RFQsRUFBRUssY0FBYztRQUVoQixJQUFJLENBQUNyRSxjQUFjO1FBRW5CLE1BQU0wRSxRQUFRNUUsT0FBTzZFLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXhCLEVBQUUsS0FBS3BEO1FBQ3hDLElBQUksQ0FBQzBFLE9BQU87UUFFWm5FLFNBQVNELFFBQVE7UUFFakIsK0JBQStCO1FBQy9CLE1BQU11RSxZQUFZSCxNQUFNZCxlQUFlLENBQUN2QixDQUFDLEtBQUttQyxXQUFXRSxNQUFNZCxlQUFlLENBQUN0QixDQUFDLEtBQUttQztRQUVyRixJQUFJSSxXQUFXO1lBQ2Isb0JBQW9CO1lBQ3BCOUUsVUFBVXFCLENBQUFBLE9BQVFBLEtBQUswRCxHQUFHLENBQUNGLENBQUFBLElBQ3pCQSxFQUFFeEIsRUFBRSxLQUFLcEQsZUFDTDt3QkFBRSxHQUFHNEUsQ0FBQzt3QkFBRWxCLFVBQVU7NEJBQUVyQixHQUFHbUM7NEJBQVNsQyxHQUFHbUM7d0JBQVE7d0JBQUdkLFFBQVE7b0JBQUssSUFDM0RpQjtZQUVOekUsZ0JBQWdCaUIsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1wQjtpQkFBYTtZQUMvQ0ssU0FBU0QsUUFBUTtZQUVqQiw4QkFBOEI7WUFDOUIsSUFBSUYsYUFBYTZFLE1BQU0sR0FBRyxNQUFNakYsT0FBT2lGLE1BQU0sRUFBRTtnQkFDN0N0RSxnQkFBZ0I7Z0JBQ2hCLE1BQU11RSxZQUFZL0IsS0FBS2dDLEdBQUcsQ0FBQyxHQUFHLE1BQU1oQyxLQUFLQyxLQUFLLENBQUN0QyxjQUFjO2dCQUM3RCxNQUFNc0UsWUFBWWpDLEtBQUtnQyxHQUFHLENBQUMsR0FBRyxLQUFLM0U7Z0JBQ25DLE1BQU02RSxhQUFhL0UsUUFBUSxLQUFLNEUsWUFBWUU7Z0JBQzVDRSxXQUFXLElBQU12RixXQUFXc0YsYUFBYTtZQUMzQztRQUNGLE9BQU87WUFDTCwyQ0FBMkM7WUFDM0NDLFdBQVc7Z0JBQ1RyRixVQUFVcUIsQ0FBQUEsT0FBUUEsS0FBSzBELEdBQUcsQ0FBQ0YsQ0FBQUEsSUFDekJBLEVBQUV4QixFQUFFLEtBQUtwRCxlQUNMOzRCQUFFLEdBQUc0RSxDQUFDOzRCQUFFbEIsVUFBVTtnQ0FBRXJCLEdBQUcsQ0FBQztnQ0FBR0MsR0FBRyxDQUFDOzRCQUFFOzRCQUFHcUIsUUFBUTt3QkFBTSxJQUNsRGlCO1lBRVIsR0FBRztRQUNMO1FBRUEzRSxnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNb0YsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxPQUFPdEMsS0FBS0MsS0FBSyxDQUFDb0MsVUFBVTtRQUNsQyxNQUFNRSxPQUFPRixVQUFVO1FBQ3ZCLE9BQU8sR0FBV0UsT0FBUkQsTUFBSyxLQUFvQyxPQUFqQ0MsS0FBS0MsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRztJQUNoRDtJQUVBLE1BQU1DLGtCQUFrQixDQUFDdEQsR0FBV0M7UUFDbEMsT0FBT3hDLE9BQU82RSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVsQixRQUFRLENBQUNyQixDQUFDLEtBQUtBLEtBQUt1QyxFQUFFbEIsUUFBUSxDQUFDcEIsQ0FBQyxLQUFLQSxLQUFLc0MsRUFBRWpCLE1BQU07SUFDOUU7SUFFQSxNQUFNaUMsY0FBYztRQUNsQixJQUFJOUUsbUJBQW1CLE9BQU87WUFDNUIsT0FBTztnQkFBRVcsTUFBTTtnQkFBR0MsTUFBTTtZQUFFO1FBQzVCO1FBRUEsT0FBUVo7WUFDTixLQUFLO2dCQUNILE9BQU87b0JBQUVXLE1BQU07b0JBQUdDLE1BQU07Z0JBQUU7WUFDNUIsS0FBSztnQkFDSCxPQUFPO29CQUFFRCxNQUFNO29CQUFHQyxNQUFNO2dCQUFFO1lBQzVCLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRUQsTUFBTTtvQkFBR0MsTUFBTTtnQkFBRTtZQUM1QixLQUFLO2dCQUNILE9BQU87b0JBQUVELE1BQU07b0JBQUdDLE1BQU07Z0JBQUU7WUFDNUIsS0FBSztnQkFDSCxPQUFPO29CQUFFRCxNQUFNO29CQUFHQyxNQUFNO2dCQUFFO1lBQzVCO2dCQUNFLE9BQU87b0JBQUVELE1BQU07b0JBQUdDLE1BQU07Z0JBQUU7UUFDOUI7SUFDRjtJQUVBLElBQUksQ0FBQ2hCLGFBQWE7UUFDaEIscUJBQ0UsOERBQUNtRjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFxQzs7Ozs7O2tDQUNuRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQWdCOzs7Ozs7a0NBQy9CLDhEQUFDbEI7d0JBQUVrQixXQUFVO2tDQUE2Qjs7Ozs7O2tDQUsxQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBR0YsV0FBVTswQ0FBd0M7Ozs7OzswQ0FDdEQsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNaOUUsUUFBUThELEdBQUcsQ0FBQyxDQUFDaEQ7b0NBQ1osTUFBTW1FLGdCQUFnQixDQUFDbkU7d0NBQ3JCLElBQUlBLFdBQVcsT0FBTzs0Q0FDcEIsT0FBTztnREFBRW9FLE9BQU87Z0RBQUlDLGFBQWE7NENBQWtCO3dDQUNyRDt3Q0FDQSxNQUFNRCxRQUFRakYsWUFBWSxDQUFDYSxPQUFvQyxJQUFJO3dDQUNuRSxPQUFPOzRDQUFFb0U7NENBQU9DLGFBQWEsR0FBUyxPQUFORCxPQUFNO3dDQUFZO29DQUNwRDtvQ0FFQSxNQUFNLEVBQUVBLEtBQUssRUFBRUMsV0FBVyxFQUFFLEdBQUdGLGNBQWNuRTtvQ0FFN0MscUJBQ0UsOERBQUNzRTt3Q0FFQ0MsU0FBUyxJQUFNdEYsa0JBQWtCZTt3Q0FDakNnRSxXQUFXLHVEQUlWLE9BSENoRixtQkFBbUJnQixTQUNmLHNFQUNBO2tEQUdOLDRFQUFDK0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBeUJoRTs7Ozs7OzhEQUN4Qyw4REFBQytEO29EQUFJQyxXQUFXLFdBQXlFLE9BQTlEaEYsbUJBQW1CZ0IsU0FBUyxrQkFBa0I7OERBQ3RFcUU7Ozs7OztnREFFRnJFLFdBQVcsdUJBQ1YsOERBQUMrRDtvREFBSUMsV0FBVyxnQkFBOEUsT0FBOURoRixtQkFBbUJnQixTQUFTLGtCQUFrQjs7d0RBQW1CO3dEQUNsRm9FLFNBQVMsSUFBSSxTQUFTQSxTQUFTLEtBQUssV0FBVzs7Ozs7Ozs7Ozs7Ozt1Q0FmN0RwRTs7Ozs7Z0NBcUJYOzs7Ozs7Ozs7Ozs7a0NBSUosOERBQUMrRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNsQjs7b0NBQUU7a0RBQUUsOERBQUMwQjtrREFBTzs7Ozs7O29DQUF5Qjs7Ozs7OzswQ0FDdEMsOERBQUMxQjs7b0NBQUU7a0RBQUUsOERBQUMwQjtrREFBTzs7Ozs7O29DQUF3Qjs7Ozs7OzswQ0FDckMsOERBQUMxQjs7b0NBQUU7a0RBQUUsOERBQUMwQjtrREFBTzs7Ozs7O29DQUFzQjs7Ozs7OzswQ0FDbkMsOERBQUMxQjs7b0NBQUU7a0RBQUUsOERBQUMwQjtrREFBTzs7Ozs7O29DQUFzQzs7Ozs7OzswQ0FDbkQsOERBQUMxQjs7b0NBQUU7a0RBQUUsOERBQUMwQjtrREFBTzs7Ozs7O29DQUFxQjs7Ozs7OzswQ0FDbEMsOERBQUMxQjswQ0FBRTs7Ozs7OzBDQUNILDhEQUFDQTswQ0FBRTs7Ozs7Ozs7Ozs7O2tDQUdMLDhEQUFDd0I7d0JBQ0NDLFNBQVN4Qzt3QkFDVGlDLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBR0QsV0FBVTswQ0FBZ0M7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNaVCxXQUFXekU7Ozs7Ozs7Ozs7OztrQ0FJaEIsOERBQUNpRjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQXNDMUY7Ozs7OztrREFDckQsOERBQUN5Rjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FFekMsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQW9DeEY7Ozs7OztrREFDbkQsOERBQUN1Rjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7OzswQ0FFekMsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQXFDNUYsYUFBYTZFLE1BQU07Ozs7OztrREFDdkUsOERBQUNjO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBc0NoRyxPQUFPaUYsTUFBTTs7Ozs7O2tEQUNsRSw4REFBQ2M7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzdDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUdGLFdBQVU7O29DQUF3QztvQ0FDL0NoRixtQkFBbUIsUUFBUSx3QkFBd0IsR0FBa0IsT0FBZkEsZ0JBQWU7Ozs7Ozs7MENBRTVFLDhEQUFDK0U7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FDQ0MsV0FBWTt3Q0FDWlMsT0FBTzs0Q0FDTEMscUJBQXFCLFVBQTZCLE9BQW5CWixjQUFjbkUsSUFBSSxFQUFDOzRDQUNsRGdGLGtCQUFrQixVQUE2QixPQUFuQmIsY0FBY2xFLElBQUksRUFBQzt3Q0FDakQ7a0RBRUNnRixNQUFNQyxJQUFJLENBQUM7NENBQUU1QixRQUFRYSxjQUFjbkUsSUFBSSxHQUFHbUUsY0FBY2xFLElBQUk7d0NBQUMsR0FBRyxDQUFDa0YsR0FBR2xFOzRDQUNuRSxNQUFNTCxJQUFJSyxRQUFRa0QsY0FBY25FLElBQUk7NENBQ3BDLE1BQU1hLElBQUlXLEtBQUtDLEtBQUssQ0FBQ1IsUUFBUWtELGNBQWNuRSxJQUFJOzRDQUMvQyxNQUFNb0YsY0FBY2xCLGdCQUFnQnRELEdBQUdDOzRDQUV2QyxxQkFDRSw4REFBQ3VEO2dEQUVDQyxXQUFXLGdJQUVWLE9BRENlLGNBQWMsZ0RBQWdEO2dEQUVoRUMsWUFBWTFDO2dEQUNaMkMsUUFBUSxDQUFDL0MsSUFBTU8sV0FBV1AsR0FBRzNCLEdBQUdDOzBEQUUvQnVFLDZCQUNDLDhEQUFDaEI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBaUJlLFlBQVlyRCxJQUFJOzs7Ozs7c0VBQ2hELDhEQUFDcUM7NERBQUlDLFdBQVU7c0VBQ1plLFlBQVl2RCxXQUFXOzs7Ozs7Ozs7Ozs7K0NBWHpCLEdBQVFoQixPQUFMRCxHQUFFLEtBQUssT0FBRkM7Ozs7O3dDQWlCbkI7Ozs7OztrREFJRiw4REFBQ3VEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDWmhGLG1CQUFtQixRQUNoQixpREFDQSxhQUE0RkEsT0FBL0VHLFlBQVksQ0FBQ0gsZUFBNEMsSUFBSSxHQUFFLGtCQUErQixPQUFmQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUXhHLDhEQUFDK0U7OzBDQUNDLDhEQUFDRztnQ0FBR0YsV0FBVTswQ0FBd0M7Ozs7OzswQ0FDdEQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1poRyxPQUFPOEIsTUFBTSxDQUFDZ0QsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFakIsTUFBTSxFQUFFbUIsR0FBRyxDQUFDLENBQUNKLHNCQUNsQyw4REFBQ21CO2dEQUVDbUIsU0FBUztnREFDVEMsYUFBYSxDQUFDakQsSUFBTUQsZ0JBQWdCQyxHQUFHVSxNQUFNdEIsRUFBRTtnREFDL0MwQyxXQUFXLHFIQUVWLE9BREM5RixpQkFBaUIwRSxNQUFNdEIsRUFBRSxHQUFHLGVBQWU7MERBRzdDLDRFQUFDeUM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBWXBCLE1BQU1sQixJQUFJOzs7Ozs7c0VBQ3JDLDhEQUFDcUM7OzhFQUNDLDhEQUFDQTtvRUFBSUMsV0FBVTs4RUFBMEJwQixNQUFNcEIsV0FBVzs7Ozs7OzhFQUMxRCw4REFBQ3VDO29FQUFJQyxXQUFVOzhFQUF5QnBCLE1BQU01QyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBWG5ENEMsTUFBTXRCLEVBQUU7Ozs7Ozs7Ozs7b0NBa0JsQnRELE9BQU84QixNQUFNLENBQUNnRCxDQUFBQSxJQUFLLENBQUNBLEVBQUVqQixNQUFNLEVBQUVvQixNQUFNLEtBQUssbUJBQ3hDLDhEQUFDYzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFnQjs7Ozs7OzBEQUMvQiw4REFBQ0Q7MERBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFkckYsOEJBQ0MsOERBQUNxRjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQVc7Ozs7OzswQ0FFMUIsOERBQUNEOztrREFDQyw4REFBQ0c7d0NBQUdGLFdBQVU7a0RBQTBDOzs7Ozs7a0RBR3hELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNsQjs7b0RBQUU7a0VBQU0sOERBQUNzQzt3REFBS3BCLFdBQVU7a0VBQWlCVCxXQUFXekU7Ozs7Ozs7Ozs7OzswREFDckQsOERBQUNnRTs7b0RBQUU7a0VBQU8sOERBQUNzQzt3REFBS3BCLFdBQVU7a0VBQW1CeEY7Ozs7Ozs7Ozs7OzswREFDN0MsOERBQUNzRTs7b0RBQUU7a0VBQWEsOERBQUNzQzt3REFBS3BCLFdBQVU7a0VBQTZCMUY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJakUsOERBQUN5RjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ007b0NBQ0NDLFNBQVN4QztvQ0FDVGlDLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQjtHQWpmTW5HO0tBQUFBO0FBbWZOLGlFQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1FRUsgRURFTlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxHQU1FUyBGT1IgQVJJQ0FcXGdhbWVzLWZvci1hZnJpY2FcXHNyY1xcY29tcG9uZW50c1xcZ2FtZXNcXEppZ3Nhd1B1enpsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENvdW50cnkgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IHNodWZmbGVBcnJheSB9IGZyb20gJ0AvdXRpbHMnO1xuaW1wb3J0IEZsYWdJbWFnZSBmcm9tICdAL2NvbXBvbmVudHMvdWkvRmxhZ0ltYWdlJztcblxuaW50ZXJmYWNlIEppZ3Nhd1B1enpsZVByb3BzIHtcbiAgY291bnRyaWVzOiBDb3VudHJ5W107XG4gIG9uQ29tcGxldGU6IChzY29yZTogbnVtYmVyKSA9PiB2b2lkO1xufVxuXG5pbnRlcmZhY2UgUHV6emxlUGllY2Uge1xuICBpZDogc3RyaW5nO1xuICBjb3VudHJ5SWQ6IHN0cmluZztcbiAgY291bnRyeU5hbWU6IHN0cmluZztcbiAgZmxhZzogc3RyaW5nO1xuICByZWdpb246IHN0cmluZztcbiAgcG9zaXRpb246IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfTtcbiAgcGxhY2VkOiBib29sZWFuO1xuICBjb3JyZWN0UG9zaXRpb246IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfTtcbn1cblxuY29uc3QgSmlnc2F3UHV6emxlOiBSZWFjdC5GQzxKaWdzYXdQdXp6bGVQcm9wcz4gPSAoeyBjb3VudHJpZXMsIG9uQ29tcGxldGUgfSkgPT4ge1xuICBjb25zdCBbcGllY2VzLCBzZXRQaWVjZXNdID0gdXNlU3RhdGU8UHV6emxlUGllY2VbXT4oW10pO1xuICBjb25zdCBbZHJhZ2dlZFBpZWNlLCBzZXREcmFnZ2VkUGllY2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtwbGFjZWRQaWVjZXMsIHNldFBsYWNlZFBpZWNlc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xuICBjb25zdCBbc2NvcmUsIHNldFNjb3JlXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbbW92ZXMsIHNldE1vdmVzXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbZ2FtZUNvbXBsZXRlLCBzZXRHYW1lQ29tcGxldGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZ2FtZVN0YXJ0ZWQsIHNldEdhbWVTdGFydGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3RpbWVFbGFwc2VkLCBzZXRUaW1lRWxhcHNlZF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3NlbGVjdGVkUmVnaW9uLCBzZXRTZWxlY3RlZFJlZ2lvbl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdBbGwnKTtcblxuICBjb25zdCByZWdpb25zID0gWydBbGwnLCAnTm9ydGggQWZyaWNhJywgJ1dlc3QgQWZyaWNhJywgJ0Vhc3QgQWZyaWNhJywgJ0NlbnRyYWwgQWZyaWNhJywgJ1NvdXRoZXJuIEFmcmljYSddO1xuXG4gIC8vIERlZmluZSB0aGUgZXhhY3QgY291bnRyeSBjb3VudHMgcGVyIHJlZ2lvblxuICBjb25zdCByZWdpb25Db3VudHMgPSB7XG4gICAgJ05vcnRoIEFmcmljYSc6IDYsXG4gICAgJ1dlc3QgQWZyaWNhJzogMTYsXG4gICAgJ0Vhc3QgQWZyaWNhJzogMTAsXG4gICAgJ0NlbnRyYWwgQWZyaWNhJzogOCxcbiAgICAnU291dGhlcm4gQWZyaWNhJzogMTBcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChnYW1lU3RhcnRlZCAmJiAhZ2FtZUNvbXBsZXRlKSB7XG4gICAgICBjb25zdCB0aW1lciA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc2V0VGltZUVsYXBzZWQocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgICB9LCAxMDAwKTtcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKTtcbiAgICB9XG4gIH0sIFtnYW1lU3RhcnRlZCwgZ2FtZUNvbXBsZXRlXSk7XG5cbiAgY29uc3QgZ2VuZXJhdGVQdXp6bGUgPSAoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkQ291bnRyaWVzOiBDb3VudHJ5W10gPSBbXTtcbiAgICBsZXQgZ3JpZFNpemUgPSB7IGNvbHM6IDYsIHJvd3M6IDUgfTsgLy8gRGVmYXVsdCBncmlkIHNpemVcblxuICAgIGlmIChzZWxlY3RlZFJlZ2lvbiA9PT0gJ0FsbCcpIHtcbiAgICAgIC8vIENvbXBsZXRlIEFmcmljYSBtb2RlIC0gc2VsZWN0IHJlcHJlc2VudGF0aXZlIGNvdW50cmllcyBmcm9tIGVhY2ggcmVnaW9uXG4gICAgICBjb25zdCBub3J0aEFmcmljYSA9IGNvdW50cmllcy5maWx0ZXIoYyA9PiBjLnJlZ2lvbiA9PT0gJ05vcnRoIEFmcmljYScpLnNsaWNlKDAsIDMpO1xuICAgICAgY29uc3Qgd2VzdEFmcmljYSA9IGNvdW50cmllcy5maWx0ZXIoYyA9PiBjLnJlZ2lvbiA9PT0gJ1dlc3QgQWZyaWNhJykuc2xpY2UoMCwgNik7XG4gICAgICBjb25zdCBlYXN0QWZyaWNhID0gY291bnRyaWVzLmZpbHRlcihjID0+IGMucmVnaW9uID09PSAnRWFzdCBBZnJpY2EnKS5zbGljZSgwLCA0KTtcbiAgICAgIGNvbnN0IGNlbnRyYWxBZnJpY2EgPSBjb3VudHJpZXMuZmlsdGVyKGMgPT4gYy5yZWdpb24gPT09ICdDZW50cmFsIEFmcmljYScpLnNsaWNlKDAsIDMpO1xuICAgICAgY29uc3Qgc291dGhlcm5BZnJpY2EgPSBjb3VudHJpZXMuZmlsdGVyKGMgPT4gYy5yZWdpb24gPT09ICdTb3V0aGVybiBBZnJpY2EnKS5zbGljZSgwLCA0KTtcblxuICAgICAgZmlsdGVyZWRDb3VudHJpZXMgPSBbLi4ubm9ydGhBZnJpY2EsIC4uLndlc3RBZnJpY2EsIC4uLmVhc3RBZnJpY2EsIC4uLmNlbnRyYWxBZnJpY2EsIC4uLnNvdXRoZXJuQWZyaWNhXTtcbiAgICAgIGdyaWRTaXplID0geyBjb2xzOiA4LCByb3dzOiA0IH07IC8vIExhcmdlciBncmlkIGZvciBjb21wbGV0ZSBBZnJpY2FcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gUmVnaW9uLXNwZWNpZmljIG1vZGUgLSBpbmNsdWRlIGFsbCBjb3VudHJpZXMgZnJvbSB0aGUgc2VsZWN0ZWQgcmVnaW9uXG4gICAgICBmaWx0ZXJlZENvdW50cmllcyA9IGNvdW50cmllcy5maWx0ZXIoYyA9PiBjLnJlZ2lvbiA9PT0gc2VsZWN0ZWRSZWdpb24pO1xuXG4gICAgICAvLyBBZGp1c3QgZ3JpZCBzaXplIGJhc2VkIG9uIHJlZ2lvblxuICAgICAgc3dpdGNoIChzZWxlY3RlZFJlZ2lvbikge1xuICAgICAgICBjYXNlICdOb3J0aCBBZnJpY2EnOlxuICAgICAgICAgIGdyaWRTaXplID0geyBjb2xzOiAzLCByb3dzOiAyIH07IC8vIDYgY291bnRyaWVzXG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ1dlc3QgQWZyaWNhJzpcbiAgICAgICAgICBncmlkU2l6ZSA9IHsgY29sczogNCwgcm93czogNCB9OyAvLyAxNiBjb3VudHJpZXNcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnRWFzdCBBZnJpY2EnOlxuICAgICAgICAgIGdyaWRTaXplID0geyBjb2xzOiA1LCByb3dzOiAyIH07IC8vIDEwIGNvdW50cmllc1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdDZW50cmFsIEFmcmljYSc6XG4gICAgICAgICAgZ3JpZFNpemUgPSB7IGNvbHM6IDQsIHJvd3M6IDIgfTsgLy8gOCBjb3VudHJpZXNcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnU291dGhlcm4gQWZyaWNhJzpcbiAgICAgICAgICBncmlkU2l6ZSA9IHsgY29sczogNSwgcm93czogMiB9OyAvLyAxMCBjb3VudHJpZXNcbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBnZW9ncmFwaGljYWwgcG9zaXRpb25zIGJhc2VkIG9uIGFjdHVhbCBBZnJpY2FuIGdlb2dyYXBoeVxuICAgIGNvbnN0IHJlZ2lvblBvc2l0aW9uczogUmVjb3JkPHN0cmluZywgeyB4OiBudW1iZXI7IHk6IG51bWJlciB9W10+ID0ge1xuICAgICAgJ05vcnRoIEFmcmljYSc6IFtcbiAgICAgICAgeyB4OiAwLCB5OiAwIH0sIC8vIE1vcm9jY29cbiAgICAgICAgeyB4OiAxLCB5OiAwIH0sIC8vIEFsZ2VyaWFcbiAgICAgICAgeyB4OiAyLCB5OiAwIH0sIC8vIFR1bmlzaWFcbiAgICAgICAgeyB4OiAzLCB5OiAwIH0sIC8vIExpYnlhXG4gICAgICAgIHsgeDogNCwgeTogMCB9LCAvLyBFZ3lwdFxuICAgICAgICB7IHg6IDIsIHk6IDEgfSwgLy8gU3VkYW5cbiAgICAgIF0sXG4gICAgICAnV2VzdCBBZnJpY2EnOiBbXG4gICAgICAgIHsgeDogMCwgeTogMCB9LCAvLyBNYXVyaXRhbmlhXG4gICAgICAgIHsgeDogMSwgeTogMCB9LCAvLyBNYWxpXG4gICAgICAgIHsgeDogMiwgeTogMCB9LCAvLyBOaWdlclxuICAgICAgICB7IHg6IDAsIHk6IDEgfSwgLy8gU2VuZWdhbFxuICAgICAgICB7IHg6IDEsIHk6IDEgfSwgLy8gQnVya2luYSBGYXNvXG4gICAgICAgIHsgeDogMiwgeTogMSB9LCAvLyBOaWdlcmlhXG4gICAgICAgIHsgeDogMCwgeTogMiB9LCAvLyBHdWluZWEtQmlzc2F1XG4gICAgICAgIHsgeDogMSwgeTogMiB9LCAvLyBHdWluZWFcbiAgICAgICAgeyB4OiAyLCB5OiAyIH0sIC8vIEl2b3J5IENvYXN0XG4gICAgICAgIHsgeDogMywgeTogMiB9LCAvLyBHaGFuYVxuICAgICAgICB7IHg6IDAsIHk6IDMgfSwgLy8gU2llcnJhIExlb25lXG4gICAgICAgIHsgeDogMSwgeTogMyB9LCAvLyBMaWJlcmlhXG4gICAgICAgIHsgeDogMiwgeTogMyB9LCAvLyBUb2dvXG4gICAgICAgIHsgeDogMywgeTogMyB9LCAvLyBCZW5pblxuICAgICAgICB7IHg6IDQsIHk6IDAgfSwgLy8gQ2FwZSBWZXJkZSAob2ZmIGNvYXN0KVxuICAgICAgICB7IHg6IDQsIHk6IDEgfSwgLy8gR2FtYmlhXG4gICAgICBdLFxuICAgICAgJ0Vhc3QgQWZyaWNhJzogW1xuICAgICAgICB7IHg6IDAsIHk6IDAgfSwgLy8gRXRoaW9waWFcbiAgICAgICAgeyB4OiAxLCB5OiAwIH0sIC8vIEVyaXRyZWFcbiAgICAgICAgeyB4OiAyLCB5OiAwIH0sIC8vIERqaWJvdXRpXG4gICAgICAgIHsgeDogMywgeTogMCB9LCAvLyBTb21hbGlhXG4gICAgICAgIHsgeDogMCwgeTogMSB9LCAvLyBTb3V0aCBTdWRhblxuICAgICAgICB7IHg6IDEsIHk6IDEgfSwgLy8gVWdhbmRhXG4gICAgICAgIHsgeDogMiwgeTogMSB9LCAvLyBLZW55YVxuICAgICAgICB7IHg6IDMsIHk6IDEgfSwgLy8gVGFuemFuaWFcbiAgICAgICAgeyB4OiA0LCB5OiAxIH0sIC8vIFJ3YW5kYVxuICAgICAgICB7IHg6IDQsIHk6IDAgfSwgLy8gQnVydW5kaVxuICAgICAgXSxcbiAgICAgICdDZW50cmFsIEFmcmljYSc6IFtcbiAgICAgICAgeyB4OiAwLCB5OiAwIH0sIC8vIENoYWRcbiAgICAgICAgeyB4OiAxLCB5OiAwIH0sIC8vIENlbnRyYWwgQWZyaWNhbiBSZXB1YmxpY1xuICAgICAgICB7IHg6IDIsIHk6IDAgfSwgLy8gQ2FtZXJvb25cbiAgICAgICAgeyB4OiAwLCB5OiAxIH0sIC8vIERlbW9jcmF0aWMgUmVwdWJsaWMgb2YgQ29uZ29cbiAgICAgICAgeyB4OiAxLCB5OiAxIH0sIC8vIFJlcHVibGljIG9mIENvbmdvXG4gICAgICAgIHsgeDogMiwgeTogMSB9LCAvLyBHYWJvblxuICAgICAgICB7IHg6IDMsIHk6IDEgfSwgLy8gRXF1YXRvcmlhbCBHdWluZWFcbiAgICAgICAgeyB4OiAzLCB5OiAwIH0sIC8vIFPDo28gVG9tw6kgYW5kIFByw61uY2lwZVxuICAgICAgXSxcbiAgICAgICdTb3V0aGVybiBBZnJpY2EnOiBbXG4gICAgICAgIHsgeDogMCwgeTogMCB9LCAvLyBBbmdvbGFcbiAgICAgICAgeyB4OiAxLCB5OiAwIH0sIC8vIFphbWJpYVxuICAgICAgICB7IHg6IDIsIHk6IDAgfSwgLy8gTWFsYXdpXG4gICAgICAgIHsgeDogMywgeTogMCB9LCAvLyBNb3phbWJpcXVlXG4gICAgICAgIHsgeDogMCwgeTogMSB9LCAvLyBOYW1pYmlhXG4gICAgICAgIHsgeDogMSwgeTogMSB9LCAvLyBCb3Rzd2FuYVxuICAgICAgICB7IHg6IDIsIHk6IDEgfSwgLy8gWmltYmFid2VcbiAgICAgICAgeyB4OiAzLCB5OiAxIH0sIC8vIFNvdXRoIEFmcmljYVxuICAgICAgICB7IHg6IDQsIHk6IDEgfSwgLy8gTGVzb3Rob1xuICAgICAgICB7IHg6IDQsIHk6IDAgfSwgLy8gRXN3YXRpbmlcbiAgICAgIF1cbiAgICB9O1xuXG4gICAgY29uc3QgcHV6emxlUGllY2VzOiBQdXp6bGVQaWVjZVtdID0gW107XG5cbiAgICBmaWx0ZXJlZENvdW50cmllcy5mb3JFYWNoKChjb3VudHJ5LCBpbmRleCkgPT4ge1xuICAgICAgbGV0IGNvcnJlY3RQb3M6IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfTtcblxuICAgICAgaWYgKHNlbGVjdGVkUmVnaW9uID09PSAnQWxsJykge1xuICAgICAgICAvLyBGb3IgY29tcGxldGUgQWZyaWNhIG1vZGUsIGFycmFuZ2UgYnkgcmVnaW9uc1xuICAgICAgICBpZiAoY291bnRyeS5yZWdpb24gPT09ICdOb3J0aCBBZnJpY2EnKSB7XG4gICAgICAgICAgY29ycmVjdFBvcyA9IHsgeDogaW5kZXggJSA4LCB5OiAwIH07XG4gICAgICAgIH0gZWxzZSBpZiAoY291bnRyeS5yZWdpb24gPT09ICdXZXN0IEFmcmljYScpIHtcbiAgICAgICAgICBjb25zdCB3ZXN0SW5kZXggPSBpbmRleCAtIDM7IC8vIE9mZnNldCBieSBOb3J0aCBBZnJpY2EgY291bnRyaWVzXG4gICAgICAgICAgY29ycmVjdFBvcyA9IHsgeDogd2VzdEluZGV4ICUgOCwgeTogMSB9O1xuICAgICAgICB9IGVsc2UgaWYgKGNvdW50cnkucmVnaW9uID09PSAnRWFzdCBBZnJpY2EnKSB7XG4gICAgICAgICAgY29uc3QgZWFzdEluZGV4ID0gaW5kZXggLSA5OyAvLyBPZmZzZXQgYnkgTm9ydGggKyBXZXN0IEFmcmljYSBjb3VudHJpZXNcbiAgICAgICAgICBjb3JyZWN0UG9zID0geyB4OiBlYXN0SW5kZXggJSA4LCB5OiAyIH07XG4gICAgICAgIH0gZWxzZSBpZiAoY291bnRyeS5yZWdpb24gPT09ICdDZW50cmFsIEFmcmljYScpIHtcbiAgICAgICAgICBjb25zdCBjZW50cmFsSW5kZXggPSBpbmRleCAtIDEzOyAvLyBPZmZzZXQgYnkgcHJldmlvdXMgcmVnaW9uc1xuICAgICAgICAgIGNvcnJlY3RQb3MgPSB7IHg6IGNlbnRyYWxJbmRleCAlIDgsIHk6IDMgfTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zdCBzb3V0aEluZGV4ID0gaW5kZXggLSAxNjsgLy8gT2Zmc2V0IGJ5IHByZXZpb3VzIHJlZ2lvbnNcbiAgICAgICAgICBjb3JyZWN0UG9zID0geyB4OiBzb3V0aEluZGV4ICUgOCwgeTogMyB9O1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBGb3IgcmVnaW9uLXNwZWNpZmljIG1vZGUsIHVzZSBnZW9ncmFwaGljYWwgcG9zaXRpb25zXG4gICAgICAgIGNvbnN0IHJlZ2lvblBvc2l0aW9uc19hcnJheSA9IHJlZ2lvblBvc2l0aW9uc1tjb3VudHJ5LnJlZ2lvbl0gfHwgW107XG4gICAgICAgIGNvcnJlY3RQb3MgPSByZWdpb25Qb3NpdGlvbnNfYXJyYXlbaW5kZXhdIHx8IHsgeDogaW5kZXggJSBncmlkU2l6ZS5jb2xzLCB5OiBNYXRoLmZsb29yKGluZGV4IC8gZ3JpZFNpemUuY29scykgfTtcbiAgICAgIH1cblxuICAgICAgcHV6emxlUGllY2VzLnB1c2goe1xuICAgICAgICBpZDogYHBpZWNlLSR7Y291bnRyeS5pZH1gLFxuICAgICAgICBjb3VudHJ5SWQ6IGNvdW50cnkuaWQsXG4gICAgICAgIGNvdW50cnlOYW1lOiBjb3VudHJ5Lm5hbWUsXG4gICAgICAgIGZsYWc6IGNvdW50cnkuZmxhZ1VybCxcbiAgICAgICAgcmVnaW9uOiBjb3VudHJ5LnJlZ2lvbixcbiAgICAgICAgcG9zaXRpb246IHsgeDogLTEsIHk6IC0xIH0sIC8vIFN0YXJ0IHVucGxhY2VkXG4gICAgICAgIHBsYWNlZDogZmFsc2UsXG4gICAgICAgIGNvcnJlY3RQb3NpdGlvbjogY29ycmVjdFBvcyxcbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHNodWZmbGVBcnJheShwdXp6bGVQaWVjZXMpO1xuICB9O1xuXG4gIGNvbnN0IHN0YXJ0R2FtZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdQaWVjZXMgPSBnZW5lcmF0ZVB1enpsZSgpO1xuICAgIHNldFBpZWNlcyhuZXdQaWVjZXMpO1xuICAgIHNldFBsYWNlZFBpZWNlcyhbXSk7XG4gICAgc2V0U2NvcmUoMCk7XG4gICAgc2V0TW92ZXMoMCk7XG4gICAgc2V0VGltZUVsYXBzZWQoMCk7XG4gICAgc2V0R2FtZUNvbXBsZXRlKGZhbHNlKTtcbiAgICBzZXRHYW1lU3RhcnRlZCh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnU3RhcnQgPSAoZTogUmVhY3QuRHJhZ0V2ZW50LCBwaWVjZUlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXREcmFnZ2VkUGllY2UocGllY2VJZCk7XG4gICAgZS5kYXRhVHJhbnNmZXIuZWZmZWN0QWxsb3dlZCA9ICdtb3ZlJztcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnT3ZlciA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgZS5kYXRhVHJhbnNmZXIuZHJvcEVmZmVjdCA9ICdtb3ZlJztcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGU6IFJlYWN0LkRyYWdFdmVudCwgdGFyZ2V0WDogbnVtYmVyLCB0YXJnZXRZOiBudW1iZXIpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgXG4gICAgaWYgKCFkcmFnZ2VkUGllY2UpIHJldHVybjtcblxuICAgIGNvbnN0IHBpZWNlID0gcGllY2VzLmZpbmQocCA9PiBwLmlkID09PSBkcmFnZ2VkUGllY2UpO1xuICAgIGlmICghcGllY2UpIHJldHVybjtcblxuICAgIHNldE1vdmVzKG1vdmVzICsgMSk7XG5cbiAgICAvLyBDaGVjayBpZiBwb3NpdGlvbiBpcyBjb3JyZWN0XG4gICAgY29uc3QgaXNDb3JyZWN0ID0gcGllY2UuY29ycmVjdFBvc2l0aW9uLnggPT09IHRhcmdldFggJiYgcGllY2UuY29ycmVjdFBvc2l0aW9uLnkgPT09IHRhcmdldFk7XG4gICAgXG4gICAgaWYgKGlzQ29ycmVjdCkge1xuICAgICAgLy8gQ29ycmVjdCBwbGFjZW1lbnRcbiAgICAgIHNldFBpZWNlcyhwcmV2ID0+IHByZXYubWFwKHAgPT4gXG4gICAgICAgIHAuaWQgPT09IGRyYWdnZWRQaWVjZSBcbiAgICAgICAgICA/IHsgLi4ucCwgcG9zaXRpb246IHsgeDogdGFyZ2V0WCwgeTogdGFyZ2V0WSB9LCBwbGFjZWQ6IHRydWUgfVxuICAgICAgICAgIDogcFxuICAgICAgKSk7XG4gICAgICBzZXRQbGFjZWRQaWVjZXMocHJldiA9PiBbLi4ucHJldiwgZHJhZ2dlZFBpZWNlXSk7XG4gICAgICBzZXRTY29yZShzY29yZSArIDEwKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgcHV6emxlIGlzIGNvbXBsZXRlXG4gICAgICBpZiAocGxhY2VkUGllY2VzLmxlbmd0aCArIDEgPT09IHBpZWNlcy5sZW5ndGgpIHtcbiAgICAgICAgc2V0R2FtZUNvbXBsZXRlKHRydWUpO1xuICAgICAgICBjb25zdCB0aW1lQm9udXMgPSBNYXRoLm1heCgwLCAxMDAgLSBNYXRoLmZsb29yKHRpbWVFbGFwc2VkIC8gMTApKTtcbiAgICAgICAgY29uc3QgbW92ZUJvbnVzID0gTWF0aC5tYXgoMCwgNTAgLSBtb3Zlcyk7XG4gICAgICAgIGNvbnN0IGZpbmFsU2NvcmUgPSBzY29yZSArIDEwICsgdGltZUJvbnVzICsgbW92ZUJvbnVzO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IG9uQ29tcGxldGUoZmluYWxTY29yZSksIDEwMDApO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBJbmNvcnJlY3QgcGxhY2VtZW50IC0gcGllY2UgYm91bmNlcyBiYWNrXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0UGllY2VzKHByZXYgPT4gcHJldi5tYXAocCA9PiBcbiAgICAgICAgICBwLmlkID09PSBkcmFnZ2VkUGllY2UgXG4gICAgICAgICAgICA/IHsgLi4ucCwgcG9zaXRpb246IHsgeDogLTEsIHk6IC0xIH0sIHBsYWNlZDogZmFsc2UgfVxuICAgICAgICAgICAgOiBwXG4gICAgICAgICkpO1xuICAgICAgfSwgNTAwKTtcbiAgICB9XG5cbiAgICBzZXREcmFnZ2VkUGllY2UobnVsbCk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0VGltZSA9IChzZWNvbmRzOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBtaW5zID0gTWF0aC5mbG9vcihzZWNvbmRzIC8gNjApO1xuICAgIGNvbnN0IHNlY3MgPSBzZWNvbmRzICUgNjA7XG4gICAgcmV0dXJuIGAke21pbnN9OiR7c2Vjcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbiAgfTtcblxuICBjb25zdCBnZXRHcmlkUG9zaXRpb24gPSAoeDogbnVtYmVyLCB5OiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gcGllY2VzLmZpbmQocCA9PiBwLnBvc2l0aW9uLnggPT09IHggJiYgcC5wb3NpdGlvbi55ID09PSB5ICYmIHAucGxhY2VkKTtcbiAgfTtcblxuICBjb25zdCBnZXRHcmlkU2l6ZSA9ICgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRSZWdpb24gPT09ICdBbGwnKSB7XG4gICAgICByZXR1cm4geyBjb2xzOiA4LCByb3dzOiA0IH07XG4gICAgfVxuXG4gICAgc3dpdGNoIChzZWxlY3RlZFJlZ2lvbikge1xuICAgICAgY2FzZSAnTm9ydGggQWZyaWNhJzpcbiAgICAgICAgcmV0dXJuIHsgY29sczogMywgcm93czogMiB9O1xuICAgICAgY2FzZSAnV2VzdCBBZnJpY2EnOlxuICAgICAgICByZXR1cm4geyBjb2xzOiA0LCByb3dzOiA0IH07XG4gICAgICBjYXNlICdFYXN0IEFmcmljYSc6XG4gICAgICAgIHJldHVybiB7IGNvbHM6IDUsIHJvd3M6IDIgfTtcbiAgICAgIGNhc2UgJ0NlbnRyYWwgQWZyaWNhJzpcbiAgICAgICAgcmV0dXJuIHsgY29sczogNCwgcm93czogMiB9O1xuICAgICAgY2FzZSAnU291dGhlcm4gQWZyaWNhJzpcbiAgICAgICAgcmV0dXJuIHsgY29sczogNSwgcm93czogMiB9O1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIHsgY29sczogNiwgcm93czogNSB9O1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWdhbWVTdGFydGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtOFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+8J+nqSBKaWdzYXcgUHV6emxlIE1hcDwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi02XCI+8J+Xuu+4jzwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTMwMCBtYi02XCI+XG4gICAgICAgICAgICBFeHBsb3JlIGFsbCA1MCBBZnJpY2FuIGNvdW50cmllcyEgQ2hvb3NlIGEgc3BlY2lmaWMgcmVnaW9uIG9yIGNoYWxsZW5nZSB5b3Vyc2VsZiB3aXRoIHRoZSBjb21wbGV0ZSBBZnJpY2EgbWFwLlxuICAgICAgICAgIDwvcD5cblxuICAgICAgICAgIHsvKiBSZWdpb24gU2VsZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5DaG9vc2UgUmVnaW9uOjwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTMgbWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAge3JlZ2lvbnMubWFwKChyZWdpb24pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBnZXRSZWdpb25JbmZvID0gKHJlZ2lvbjogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAocmVnaW9uID09PSAnQWxsJykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4geyBjb3VudDogNTAsIGRlc2NyaXB0aW9uOiAnQ29tcGxldGUgQWZyaWNhJyB9O1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgY29uc3QgY291bnQgPSByZWdpb25Db3VudHNbcmVnaW9uIGFzIGtleW9mIHR5cGVvZiByZWdpb25Db3VudHNdIHx8IDA7XG4gICAgICAgICAgICAgICAgICByZXR1cm4geyBjb3VudCwgZGVzY3JpcHRpb246IGAke2NvdW50fSBjb3VudHJpZXNgIH07XG4gICAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICAgIGNvbnN0IHsgY291bnQsIGRlc2NyaXB0aW9uIH0gPSBnZXRSZWdpb25JbmZvKHJlZ2lvbik7XG5cbiAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBrZXk9e3JlZ2lvbn1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRSZWdpb24ocmVnaW9uKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJvcmRlci0yICR7XG4gICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRSZWdpb24gPT09IHJlZ2lvblxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmcteWVsbG93LTQwMCB0ZXh0LWdyYXktOTAwIGJvcmRlci15ZWxsb3ctNDAwIHRyYW5zZm9ybSBzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlIGJvcmRlci1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTYwMCBob3Zlcjpib3JkZXItZ3JheS01MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGdcIj57cmVnaW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3NlbGVjdGVkUmVnaW9uID09PSByZWdpb24gPyAndGV4dC1ncmF5LTcwMCcgOiAndGV4dC1ncmF5LTQwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAge3JlZ2lvbiAhPT0gJ0FsbCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtzZWxlY3RlZFJlZ2lvbiA9PT0gcmVnaW9uID8gJ3RleHQtZ3JheS02MDAnIDogJ3RleHQtZ3JheS01MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBEaWZmaWN1bHR5OiB7Y291bnQgPD0gNiA/ICdFYXN5JyA6IGNvdW50IDw9IDEwID8gJ01lZGl1bScgOiAnSGFyZCd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCB0ZXh0LWdyYXktMzAwIG1iLThcIj5cbiAgICAgICAgICAgIDxwPuKAoiA8c3Ryb25nPkNvbXBsZXRlIEFmcmljYTo8L3N0cm9uZz4gNTAgY291bnRyaWVzIGFjcm9zcyBhbGwgcmVnaW9uczwvcD5cbiAgICAgICAgICAgIDxwPuKAoiA8c3Ryb25nPlJlZ2lvbmFsIEZvY3VzOjwvc3Ryb25nPiBNYXN0ZXIgc3BlY2lmaWMgZ2VvZ3JhcGhpY2FsIGFyZWFzPC9wPlxuICAgICAgICAgICAgPHA+4oCiIDxzdHJvbmc+Tm9ydGggQWZyaWNhOjwvc3Ryb25nPiA2IGNvdW50cmllcyAoRWFzeSk8L3A+XG4gICAgICAgICAgICA8cD7igKIgPHN0cm9uZz5FYXN0L0NlbnRyYWwvU291dGhlcm4gQWZyaWNhOjwvc3Ryb25nPiA4LTEwIGNvdW50cmllcyAoTWVkaXVtKTwvcD5cbiAgICAgICAgICAgIDxwPuKAoiA8c3Ryb25nPldlc3QgQWZyaWNhOjwvc3Ryb25nPiAxNiBjb3VudHJpZXMgKEhhcmQpPC9wPlxuICAgICAgICAgICAgPHA+4oCiIERyYWcgcGllY2VzIHRvIHRoZWlyIGNvcnJlY3QgZ2VvZ3JhcGhpY2FsIHBvc2l0aW9uczwvcD5cbiAgICAgICAgICAgIDxwPuKAoiBMZWFybiBhYm91dCBBZnJpY2FuIGdlb2dyYXBoeSBhbmQgY291bnRyeSBsb2NhdGlvbnM8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17c3RhcnRHYW1lfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmcteWVsbG93LTQwMCB0ZXh0LWdyYXktOTAwIHB5LTMgcHgtOCByb3VuZGVkLWxnIHRleHQteGwgZm9udC1ib2xkIGhvdmVyOmJnLXllbGxvdy0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIPCfmoAgU3RhcnQgUHV6emxlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBwLTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTYgbWItNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+8J+nqSBKaWdzYXcgUHV6emxlIE1hcDwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC14bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgIHtmb3JtYXRUaW1lKHRpbWVFbGFwc2VkKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNDAwXCI+e3Njb3JlfTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5TY29yZTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+e21vdmVzfTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5Nb3ZlczwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTQwMFwiPntwbGFjZWRQaWVjZXMubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5QbGFjZWQ8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNDAwXCI+e3BpZWNlcy5sZW5ndGh9PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlRvdGFsPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICB7LyogUHV6emxlIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICDwn5e677iPIHtzZWxlY3RlZFJlZ2lvbiA9PT0gJ0FsbCcgPyAnQ29tcGxldGUgQWZyaWNhIE1hcCcgOiBgJHtzZWxlY3RlZFJlZ2lvbn0gTWFwYH1cbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BncmlkIGdhcC0yIG1pbi1oLVs0MDBweF1gfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6IGByZXBlYXQoJHtnZXRHcmlkU2l6ZSgpLmNvbHN9LCBtaW5tYXgoMCwgMWZyKSlgLFxuICAgICAgICAgICAgICAgIGdyaWRUZW1wbGF0ZVJvd3M6IGByZXBlYXQoJHtnZXRHcmlkU2l6ZSgpLnJvd3N9LCBtaW5tYXgoMCwgMWZyKSlgXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiBnZXRHcmlkU2l6ZSgpLmNvbHMgKiBnZXRHcmlkU2l6ZSgpLnJvd3MgfSwgKF8sIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgeCA9IGluZGV4ICUgZ2V0R3JpZFNpemUoKS5jb2xzO1xuICAgICAgICAgICAgICAgIGNvbnN0IHkgPSBNYXRoLmZsb29yKGluZGV4IC8gZ2V0R3JpZFNpemUoKS5jb2xzKTtcbiAgICAgICAgICAgICAgICBjb25zdCBwbGFjZWRQaWVjZSA9IGdldEdyaWRQb3NpdGlvbih4LCB5KTtcblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17YCR7eH0tJHt5fWB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGFzcGVjdC1zcXVhcmUgYm9yZGVyLTIgYm9yZGVyLWRhc2hlZCBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWRQaWVjZSA/ICdiZy1ncmVlbi01MDAgYmctb3BhY2l0eS0yMCBib3JkZXItZ3JlZW4tNDAwJyA6ICdob3Zlcjpib3JkZXIteWVsbG93LTQwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uRHJhZ092ZXI9e2hhbmRsZURyYWdPdmVyfVxuICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiBoYW5kbGVEcm9wKGUsIHgsIHkpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7cGxhY2VkUGllY2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgbWItMVwiPntwbGFjZWRQaWVjZS5mbGFnfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3BsYWNlZFBpZWNlLmNvdW50cnlOYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUmVnaW9uIExlZ2VuZCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFJlZ2lvbiA9PT0gJ0FsbCdcbiAgICAgICAgICAgICAgICAgID8gJ0RyYWcgY291bnRyaWVzIHRvIHRoZWlyIGdlb2dyYXBoaWNhbCByZWdpb25zJ1xuICAgICAgICAgICAgICAgICAgOiBgUGxhY2UgYWxsICR7cmVnaW9uQ291bnRzW3NlbGVjdGVkUmVnaW9uIGFzIGtleW9mIHR5cGVvZiByZWdpb25Db3VudHNdIHx8IDB9IGNvdW50cmllcyBpbiAke3NlbGVjdGVkUmVnaW9ufWBcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQaWVjZXMgUGFuZWwgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj7wn6epIFB1enpsZSBQaWVjZXM8L2gzPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNCBtYXgtaC1bNTAwcHhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge3BpZWNlcy5maWx0ZXIocCA9PiAhcC5wbGFjZWQpLm1hcCgocGllY2UpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e3BpZWNlLmlkfVxuICAgICAgICAgICAgICAgICAgZHJhZ2dhYmxlXG4gICAgICAgICAgICAgICAgICBvbkRyYWdTdGFydD17KGUpID0+IGhhbmRsZURyYWdTdGFydChlLCBwaWVjZS5pZCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgcC0zIGN1cnNvci1tb3ZlIGhvdmVyOmJvcmRlci15ZWxsb3ctNDAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICBkcmFnZ2VkUGllY2UgPT09IHBpZWNlLmlkID8gJ29wYWNpdHktNTAnIDogJydcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGxcIj57cGllY2UuZmxhZ308L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57cGllY2UuY291bnRyeU5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj57cGllY2UucmVnaW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7cGllY2VzLmZpbHRlcihwID0+ICFwLnBsYWNlZCkubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNDAwIHB5LThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1iLTJcIj7wn46JPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5BbGwgcGllY2VzIHBsYWNlZCE8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogR2FtZSBDb21wbGV0ZSBNb2RhbCAqL31cbiAgICAgIHtnYW1lQ29tcGxldGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgYmctYmxhY2sgYmctb3BhY2l0eS03NVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcm91bmRlZC1sZyBwLTYgbWF4LXctbWQgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGxcIj7wn4+GPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNDAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIFB1enpsZSBNYXN0ZXIhXG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICA8cD5UaW1lOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwXCI+e2Zvcm1hdFRpbWUodGltZUVsYXBzZWQpfTwvc3Bhbj48L3A+XG4gICAgICAgICAgICAgICAgICA8cD5Nb3ZlczogPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtNDAwXCI+e21vdmVzfTwvc3Bhbj48L3A+XG4gICAgICAgICAgICAgICAgICA8cD5GaW5hbCBTY29yZTogPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIGZvbnQtYm9sZFwiPntzY29yZX08L3NwYW4+PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3N0YXJ0R2FtZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXllbGxvdy00MDAgdGV4dC1ncmF5LTkwMCBweS0yIHB4LTQgcm91bmRlZC1sZyBob3ZlcjpiZy15ZWxsb3ctMzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDwn5SEIFBsYXkgQWdhaW5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSmlnc2F3UHV6emxlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJzaHVmZmxlQXJyYXkiLCJKaWdzYXdQdXp6bGUiLCJjb3VudHJpZXMiLCJvbkNvbXBsZXRlIiwicGllY2VzIiwic2V0UGllY2VzIiwiZHJhZ2dlZFBpZWNlIiwic2V0RHJhZ2dlZFBpZWNlIiwicGxhY2VkUGllY2VzIiwic2V0UGxhY2VkUGllY2VzIiwic2NvcmUiLCJzZXRTY29yZSIsIm1vdmVzIiwic2V0TW92ZXMiLCJnYW1lQ29tcGxldGUiLCJzZXRHYW1lQ29tcGxldGUiLCJnYW1lU3RhcnRlZCIsInNldEdhbWVTdGFydGVkIiwidGltZUVsYXBzZWQiLCJzZXRUaW1lRWxhcHNlZCIsInNlbGVjdGVkUmVnaW9uIiwic2V0U2VsZWN0ZWRSZWdpb24iLCJyZWdpb25zIiwicmVnaW9uQ291bnRzIiwidGltZXIiLCJzZXRJbnRlcnZhbCIsInByZXYiLCJjbGVhckludGVydmFsIiwiZ2VuZXJhdGVQdXp6bGUiLCJmaWx0ZXJlZENvdW50cmllcyIsImdyaWRTaXplIiwiY29scyIsInJvd3MiLCJub3J0aEFmcmljYSIsImZpbHRlciIsImMiLCJyZWdpb24iLCJzbGljZSIsIndlc3RBZnJpY2EiLCJlYXN0QWZyaWNhIiwiY2VudHJhbEFmcmljYSIsInNvdXRoZXJuQWZyaWNhIiwicmVnaW9uUG9zaXRpb25zIiwieCIsInkiLCJwdXp6bGVQaWVjZXMiLCJmb3JFYWNoIiwiY291bnRyeSIsImluZGV4IiwiY29ycmVjdFBvcyIsIndlc3RJbmRleCIsImVhc3RJbmRleCIsImNlbnRyYWxJbmRleCIsInNvdXRoSW5kZXgiLCJyZWdpb25Qb3NpdGlvbnNfYXJyYXkiLCJNYXRoIiwiZmxvb3IiLCJwdXNoIiwiaWQiLCJjb3VudHJ5SWQiLCJjb3VudHJ5TmFtZSIsIm5hbWUiLCJmbGFnIiwiZmxhZ1VybCIsInBvc2l0aW9uIiwicGxhY2VkIiwiY29ycmVjdFBvc2l0aW9uIiwic3RhcnRHYW1lIiwibmV3UGllY2VzIiwiaGFuZGxlRHJhZ1N0YXJ0IiwiZSIsInBpZWNlSWQiLCJkYXRhVHJhbnNmZXIiLCJlZmZlY3RBbGxvd2VkIiwiaGFuZGxlRHJhZ092ZXIiLCJwcmV2ZW50RGVmYXVsdCIsImRyb3BFZmZlY3QiLCJoYW5kbGVEcm9wIiwidGFyZ2V0WCIsInRhcmdldFkiLCJwaWVjZSIsImZpbmQiLCJwIiwiaXNDb3JyZWN0IiwibWFwIiwibGVuZ3RoIiwidGltZUJvbnVzIiwibWF4IiwibW92ZUJvbnVzIiwiZmluYWxTY29yZSIsInNldFRpbWVvdXQiLCJmb3JtYXRUaW1lIiwic2Vjb25kcyIsIm1pbnMiLCJzZWNzIiwidG9TdHJpbmciLCJwYWRTdGFydCIsImdldEdyaWRQb3NpdGlvbiIsImdldEdyaWRTaXplIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJoMyIsImdldFJlZ2lvbkluZm8iLCJjb3VudCIsImRlc2NyaXB0aW9uIiwiYnV0dG9uIiwib25DbGljayIsInN0cm9uZyIsInN0eWxlIiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImdyaWRUZW1wbGF0ZVJvd3MiLCJBcnJheSIsImZyb20iLCJfIiwicGxhY2VkUGllY2UiLCJvbkRyYWdPdmVyIiwib25Ecm9wIiwiZHJhZ2dhYmxlIiwib25EcmFnU3RhcnQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});