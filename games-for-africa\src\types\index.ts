// Core data types
export interface Country {
  id: string;
  name: string;
  capital: string;
  flagUrl: string;
  currency: string;
  languages: string[];
  population: number;
  region: string;
  independence: string;
  exports: string[];
  landmarks: string[];
  wildlife: string[];
  culturalElements: {
    traditionalClothing: string[];
    cuisine: string[];
    music: string[];
    dances: string[];
  };
  notableFigures: NotableFigure[];
}

export interface NotableFigure {
  name: string;
  field: string;
  achievement: string;
}

// Game types
export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank';
  category: 'geography' | 'history' | 'culture' | 'wildlife' | 'notable-figures';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  countryId?: string;
}

export interface GameSession {
  id: string;
  gameType: GameType;
  startTime: Date;
  endTime?: Date;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlocked: boolean;
  unlockedAt?: Date;
  condition: string;
}

export interface UserProgress {
  totalScore: number;
  gamesPlayed: number;
  totalTimeSpent: number;
  achievements: Achievement[];
  favoriteGame: GameType | null;
  streakCount: number;
  bestScores: Record<GameType, number>;
  categoryProgress: Record<string, number>;
}

// Game state types
export interface QuizGameState {
  currentQuestion: number;
  questions: QuizQuestion[];
  selectedAnswers: (string | null)[];
  score: number;
  timeRemaining: number;
  isComplete: boolean;
  showExplanation: boolean;
}

export interface MatchingGameState {
  pairs: MatchingPair[];
  selectedItems: string[];
  matchedPairs: string[];
  score: number;
  moves: number;
  isComplete: boolean;
}

export interface MemoryGameState {
  cards: MemoryCard[];
  flippedCards: number[];
  matchedCards: number[];
  moves: number;
  score: number;
  isComplete: boolean;
}

export interface MapGameState {
  countries: Country[];
  placedCountries: string[];
  selectedCountry: string | null;
  score: number;
  isComplete: boolean;
}

export interface TimedChallengeState {
  currentQuestion: number;
  questions: QuizQuestion[];
  score: number;
  timeRemaining: number;
  streak: number;
  isComplete: boolean;
}

// Component types
export interface MatchingPair {
  id: string;
  leftItem: string;
  rightItem: string;
  type: 'country-flag' | 'country-capital' | 'flag-currency';
}

export interface MemoryCard {
  id: string;
  content: string;
  type: 'image' | 'text';
  pairId: string;
  isFlipped: boolean;
  isMatched: boolean;
}

// Game configuration
export type GameType =
  | 'quiz'
  | 'matching'
  | 'jigsaw-puzzle'
  | 'memory-grid'
  | 'speed-challenge'
  | 'country-explorer'
  | 'mystery-land'
  | 'timeline-builder'
  | 'dress-character'
  | 'where-in-africa'
  | 'country-name-scramble'
  | 'flag-matching';

export interface GameConfig {
  type: GameType;
  name: string;
  description: string;
  icon: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // in minutes
  maxScore: number;
}

// UI types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

// Store types
export interface GameStore {
  currentGame: GameType | null;
  gameState: any;
  userProgress: UserProgress;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentGame: (game: GameType | null) => void;
  updateGameState: (state: any) => void;
  updateProgress: (progress: Partial<UserProgress>) => void;
  addAchievement: (achievement: Achievement) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetGame: () => void;
}
