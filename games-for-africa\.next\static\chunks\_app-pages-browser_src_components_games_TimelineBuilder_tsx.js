"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_TimelineBuilder_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/TimelineBuilder.tsx":
/*!**************************************************!*\
  !*** ./src/components/games/TimelineBuilder.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=getRandomItems,shuffleArray!=!@/utils */ \"(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst TimelineBuilder = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedEvent, setDraggedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const generateEvents = ()=>{\n        const selectedCountries = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, 6);\n        const timelineEvents = [];\n        selectedCountries.forEach((country, index)=>{\n            const independenceYear = new Date(country.independence).getFullYear();\n            // Skip countries with invalid dates\n            if (isNaN(independenceYear) || independenceYear < 1800) return;\n            timelineEvents.push({\n                id: \"event-\".concat(country.id),\n                country: country.name,\n                event: 'Independence',\n                year: independenceYear,\n                description: \"\".concat(country.name, \" gained independence\"),\n                placed: false,\n                position: -1\n            });\n        });\n        // Add some additional historical events\n        const additionalEvents = [\n            {\n                id: 'suez-canal',\n                country: 'Egypt',\n                event: 'Suez Canal Opens',\n                year: 1869,\n                description: 'The Suez Canal opened, connecting the Mediterranean and Red Seas',\n                placed: false,\n                position: -1\n            },\n            {\n                id: 'apartheid-end',\n                country: 'South Africa',\n                event: 'End of Apartheid',\n                year: 1994,\n                description: 'Nelson Mandela became the first Black president of South Africa',\n                placed: false,\n                position: -1\n            },\n            {\n                id: 'oau-formation',\n                country: 'Ethiopia',\n                event: 'OAU Formation',\n                year: 1963,\n                description: 'Organization of African Unity was formed in Addis Ababa',\n                placed: false,\n                position: -1\n            }\n        ];\n        const allEvents = [\n            ...timelineEvents,\n            ...additionalEvents\n        ].slice(0, 8);\n        return (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(allEvents);\n    };\n    const startGame = ()=>{\n        const newEvents = generateEvents();\n        setEvents(newEvents);\n        setTimeline(new Array(newEvents.length).fill(null));\n        setScore(0);\n        setMoves(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowHints(false);\n    };\n    const handleDragStart = (e, eventId)=>{\n        setDraggedEvent(eventId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, position)=>{\n        e.preventDefault();\n        if (!draggedEvent) return;\n        const event = events.find((e)=>e.id === draggedEvent);\n        if (!event || event.placed) return;\n        setMoves(moves + 1);\n        // Place the event in the timeline\n        const newTimeline = [\n            ...timeline\n        ];\n        // Remove event from its current position if it was already placed\n        const currentPos = newTimeline.findIndex((e)=>(e === null || e === void 0 ? void 0 : e.id) === draggedEvent);\n        if (currentPos !== -1) {\n            newTimeline[currentPos] = null;\n        }\n        // Place event in new position\n        newTimeline[position] = event;\n        setTimeline(newTimeline);\n        // Update event as placed\n        setEvents((prev)=>prev.map((e)=>e.id === draggedEvent ? {\n                    ...e,\n                    placed: true,\n                    position\n                } : e));\n        setDraggedEvent(null);\n        // Check if timeline is complete and correct\n        if (newTimeline.every((e)=>e !== null)) {\n            checkTimelineCorrectness(newTimeline);\n        }\n    };\n    const checkTimelineCorrectness = (completedTimeline)=>{\n        const sortedEvents = [\n            ...completedTimeline\n        ].sort((a, b)=>a.year - b.year);\n        let correctPlacements = 0;\n        completedTimeline.forEach((event, index)=>{\n            if (event.id === sortedEvents[index].id) {\n                correctPlacements++;\n            }\n        });\n        const accuracy = correctPlacements / completedTimeline.length * 100;\n        const finalScore = Math.round(accuracy + Math.max(0, 50 - moves));\n        setScore(finalScore);\n        setGameComplete(true);\n        setTimeout(()=>onComplete(finalScore), 1500);\n    };\n    const removeFromTimeline = (position)=>{\n        const event = timeline[position];\n        if (!event) return;\n        const newTimeline = [\n            ...timeline\n        ];\n        newTimeline[position] = null;\n        setTimeline(newTimeline);\n        setEvents((prev)=>prev.map((e)=>e.id === event.id ? {\n                    ...e,\n                    placed: false,\n                    position: -1\n                } : e));\n    };\n    const getSortedEvents = ()=>{\n        return [\n            ...events\n        ].sort((a, b)=>a.year - b.year);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDCDA Timeline Builder\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"⏰\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Arrange historical events from African countries in chronological order!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag events to arrange them chronologically\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Events include independence dates and major milestones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Get the correct order for maximum points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints if you need help with dates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Timeline\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDCDA Timeline Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowHints(!showHints),\n                                    className: \"px-4 py-2 rounded-lg transition-colors \".concat(showHints ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                    children: [\n                                        \"\\uD83D\\uDCA1 \",\n                                        showHints ? 'Hide' : 'Show',\n                                        \" Hints\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: [\n                                            timeline.filter((e)=>e !== null).length,\n                                            \"/\",\n                                            events.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"⏰ Timeline (Earliest → Latest)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: timeline.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-h-[80px] border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-200 \".concat(event ? 'bg-blue-500 bg-opacity-20 border-blue-400' : 'border-gray-600 hover:border-yellow-400'),\n                                            onDragOver: handleDragOver,\n                                            onDrop: (e)=>handleDrop(e, index),\n                                            children: event ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between w-full p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-semibold\",\n                                                                children: event.event\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: event.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-yellow-400 text-sm mt-1\",\n                                                                children: [\n                                                                    \"Year: \",\n                                                                    event.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeFromTimeline(index),\n                                                        className: \"text-red-400 hover:text-red-300 ml-4\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-500 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: \"Drop event here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Position \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDCCB Historical Events\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[600px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: events.filter((e)=>!e.placed).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, event.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedEvent === event.id ? 'opacity-50' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: event.event\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300 text-sm\",\n                                                        children: event.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs mt-1\",\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-yellow-400 text-sm mt-2\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCC5 \",\n                                                            event.year\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    events.filter((e)=>!e.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All events placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-yellow-400 font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCA1 Correct Order:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm\",\n                                        children: getSortedEvents().map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    index + 1,\n                                                    \". \",\n                                                    event.event,\n                                                    \" (\",\n                                                    event.year,\n                                                    \")\"\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 80 ? '🏆' : score >= 60 ? '🎉' : '📚'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Timeline Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: [\n                                                            score,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: score >= 80 ? 'Perfect historian!' : score >= 60 ? 'Great job!' : 'Keep studying history!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimelineBuilder, \"1qbbfbVkHKbrJfJScGRkEVWdU4o=\");\n_c = TimelineBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimelineBuilder);\nvar _c;\n$RefreshReg$(_c, \"TimelineBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts ***!
  \************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   formatScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatScore),\n/* harmony export */   formatTime: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.shuffleArray),\n/* harmony export */   validateAnswer: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.validateAnswer)\n/* harmony export */ });\n/* harmony import */ var C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/index.ts */ \"(app-pages-browser)/./src/utils/index.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPWdldFJhbmRvbUl0ZW1zLHNodWZmbGVBcnJheSE9IS4vc3JjL3V0aWxzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1FRUsgRURFTlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxHQU1FUyBGT1IgQVJJQ0FcXGdhbWVzLWZvci1hZnJpY2FcXHNyY1xcdXRpbHNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxNRUVLIEVERU5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcR0FNRVMgRk9SIEFSSUNBXFxcXGdhbWVzLWZvci1hZnJpY2FcXFxcc3JjXFxcXHV0aWxzXFxcXGluZGV4LnRzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\n"));

/***/ })

}]);