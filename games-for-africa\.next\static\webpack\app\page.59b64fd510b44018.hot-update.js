"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/games/MemoryGrid */ \"(app-pages-browser)/./src/components/games/MemoryGrid.tsx\");\n/* harmony import */ var _components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/games/JigsawPuzzle */ \"(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\");\n/* harmony import */ var _components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/games/TimelineBuilder */ \"(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\");\n/* harmony import */ var _components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/games/WhereInAfrica */ \"(app-pages-browser)/./src/components/games/WhereInAfrica.tsx\");\n/* harmony import */ var _components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/games/DressTheCharacter */ \"(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_12__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = (score)=>{\n        console.log('Game completed with score:', score);\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, this);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            case 'where-in-africa':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            case 'dress-character':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-accent-gold font-medium\",\n                                children: \"Score: 0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border border-gray-700 rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer\",\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: game.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⏱️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    game.estimatedTime,\n                                                                    \"min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-4 flex-grow\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                        children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            game.maxScore,\n                                                            \" pts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                children: \"▶️ Play Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                }, game.type, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"g4JRP421rujMtt/9zwXe7LlkLmM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/games/DressTheCharacter.tsx":
/*!****************************************************!*\
  !*** ./src/components/games/DressTheCharacter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DressTheCharacter = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [targetCountry, setTargetCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availableClothing, setAvailableClothing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        head: null,\n        body: null,\n        accessory: null\n    });\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [round, setRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const maxRounds = 5;\n    const generateClothingItems = (targetCountry, otherCountries)=>{\n        const items = [];\n        // Target country items (correct answers)\n        targetCountry.culturalElements.traditionalClothing.forEach((clothing, index)=>{\n            const types = [\n                'head',\n                'body',\n                'accessory'\n            ];\n            items.push({\n                id: \"\".concat(targetCountry.id, \"-\").concat(index),\n                name: clothing,\n                country: targetCountry.name,\n                countryId: targetCountry.id,\n                type: types[index % 3],\n                emoji: index % 3 === 0 ? '👑' : index % 3 === 1 ? '👘' : '📿'\n            });\n        });\n        // Other countries' items (wrong answers)\n        otherCountries.forEach((country)=>{\n            country.culturalElements.traditionalClothing.forEach((clothing, index)=>{\n                const types = [\n                    'head',\n                    'body',\n                    'accessory'\n                ];\n                items.push({\n                    id: \"\".concat(country.id, \"-\").concat(index),\n                    name: clothing,\n                    country: country.name,\n                    countryId: country.id,\n                    type: types[index % 3],\n                    emoji: index % 3 === 0 ? '🎩' : index % 3 === 1 ? '👔' : '⌚'\n                });\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(items);\n    };\n    const startNewRound = ()=>{\n        const selectedCountry = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, 1)[0];\n        const otherCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries.filter((c)=>c.id !== selectedCountry.id), 3);\n        setTargetCountry(selectedCountry);\n        setAvailableClothing(generateClothingItems(selectedCountry, otherCountries));\n        setCharacter({\n            head: null,\n            body: null,\n            accessory: null\n        });\n        setShowResult(false);\n    };\n    const startGame = ()=>{\n        setScore(0);\n        setRound(1);\n        setGameComplete(false);\n        setGameStarted(true);\n        startNewRound();\n    };\n    const handleClothingSelect = (item)=>{\n        setCharacter((prev)=>({\n                ...prev,\n                [item.type]: item\n            }));\n    };\n    const removeClothing = (type)=>{\n        setCharacter((prev)=>({\n                ...prev,\n                [type]: null\n            }));\n    };\n    const submitOutfit = ()=>{\n        if (!targetCountry) return;\n        let correctItems = 0;\n        let totalItems = 0;\n        Object.values(character).forEach((item)=>{\n            if (item) {\n                totalItems++;\n                if (item.countryId === targetCountry.id) {\n                    correctItems++;\n                }\n            }\n        });\n        const roundScore = totalItems > 0 ? Math.round(correctItems / totalItems * 20) : 0;\n        setScore(score + roundScore);\n        setShowResult(true);\n        setTimeout(()=>{\n            if (round < maxRounds) {\n                setRound(round + 1);\n                startNewRound();\n            } else {\n                setGameComplete(true);\n                setTimeout(()=>onComplete(score + roundScore), 1000);\n            }\n        }, 3000);\n    };\n    const isOutfitComplete = ()=>{\n        return character.head || character.body || character.accessory;\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFAD Dress the Character\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDC57\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Dress characters in traditional African clothing from different countries!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    maxRounds,\n                                    \" rounds of fashion challenges\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Choose traditional clothing from the target country\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Mix and match head wear, body clothing, and accessories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Score points for authentic cultural outfits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Styling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!targetCountry) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFAD Dress the Character\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    \"Round \",\n                                    round,\n                                    \"/\",\n                                    maxRounds\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl mb-2\",\n                                children: targetCountry.flagUrl\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Dress in traditional \",\n                                    targetCountry.name,\n                                    \" clothing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: [\n                                    \"Score: \",\n                                    score,\n                                    \" points\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDC64 Character\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl\",\n                                                        children: \"\\uD83D\\uDE0A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    character.head && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeClothing('head'),\n                                                            className: \"bg-blue-500 bg-opacity-20 border border-blue-400 rounded-lg px-3 py-1 text-blue-300 text-sm hover:bg-blue-500 hover:bg-opacity-30\",\n                                                            children: [\n                                                                character.head.emoji,\n                                                                \" \",\n                                                                character.head.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-8xl\",\n                                                        children: \"\\uD83E\\uDDCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    character.body && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeClothing('body'),\n                                                            className: \"bg-green-500 bg-opacity-20 border border-green-400 rounded-lg px-3 py-1 text-green-300 text-sm hover:bg-green-500 hover:bg-opacity-30\",\n                                                            children: [\n                                                                character.body.emoji,\n                                                                \" \",\n                                                                character.body.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: character.accessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeClothing('accessory'),\n                                                        className: \"bg-purple-500 bg-opacity-20 border border-purple-400 rounded-lg px-3 py-1 text-purple-300 text-sm hover:bg-purple-500 hover:bg-opacity-30\",\n                                                        children: [\n                                                            character.accessory.emoji,\n                                                            \" \",\n                                                            character.accessory.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: submitOutfit,\n                                            disabled: !isOutfitComplete() || showResult,\n                                            className: \"w-full py-3 px-4 rounded-lg font-bold transition-colors \".concat(isOutfitComplete() && !showResult ? 'bg-yellow-400 text-gray-900 hover:bg-yellow-300' : 'bg-gray-600 text-gray-400 cursor-not-allowed'),\n                                            children: showResult ? '⏳ Next Round...' : '✨ Complete Outfit'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDC57 Clothing Options\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-blue-300 mb-3\",\n                                        children: \"\\uD83D\\uDC51 Head Wear\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'head').map((item)=>{\n                                            var _character_head;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-blue-400 transition-all duration-200 \".concat(((_character_head = character.head) === null || _character_head === void 0 ? void 0 : _character_head.id) === item.id ? 'border-blue-400 bg-blue-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-green-300 mb-3\",\n                                        children: \"\\uD83D\\uDC58 Body Clothing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'body').map((item)=>{\n                                            var _character_body;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-green-400 transition-all duration-200 \".concat(((_character_body = character.body) === null || _character_body === void 0 ? void 0 : _character_body.id) === item.id ? 'border-green-400 bg-green-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-purple-300 mb-3\",\n                                        children: \"\\uD83D\\uDCFF Accessories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'accessory').map((item)=>{\n                                            var _character_accessory;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-purple-400 transition-all duration-200 \".concat(((_character_accessory = character.accessory) === null || _character_accessory === void 0 ? void 0 : _character_accessory.id) === item.id ? 'border-purple-400 bg-purple-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-4\",\n                            children: [\n                                \"Round \",\n                                round,\n                                \" Results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: Object.entries(character).map((param)=>{\n                                let [type, item] = param;\n                                if (!item) return null;\n                                const isCorrect = item.countryId === targetCountry.id;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-20 border-green-400' : 'bg-red-500 bg-opacity-20 border-red-400'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-1\",\n                                            children: item.emoji\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-medium\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: item.country\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mt-1 \".concat(isCorrect ? 'text-green-300' : 'text-red-300'),\n                                            children: isCorrect ? '✓ Correct!' : '✗ Wrong country'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 80 ? '🏆' : score >= 60 ? '🎉' : '🎭'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Fashion Show Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 35\n                                                    }, undefined),\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: score >= 80 ? 'Fashion Expert!' : score >= 60 ? 'Style Star!' : 'Keep Learning!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Style Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 369,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DressTheCharacter, \"BnZM+SaSRtauKLp0mOx0dvcn9vM=\");\n_c = DressTheCharacter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DressTheCharacter);\nvar _c;\n$RefreshReg$(_c, \"DressTheCharacter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\n"));

/***/ })

});