'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { getRandomItems, shuffleArray } from '@/utils';

interface MysteryLandProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface MysteryClue {
  text: string;
  type: 'landmark' | 'culture' | 'geography' | 'history' | 'notable-figure';
}

const MysteryLand: React.FC<MysteryLandProps> = ({ countries, onComplete }) => {
  const [currentCountry, setCurrentCountry] = useState<Country | null>(null);
  const [clues, setClues] = useState<MysteryClue[]>([]);
  const [revealedClues, setRevealedClues] = useState<number>(0);
  const [options, setOptions] = useState<string[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [score, setScore] = useState(0);
  const [round, setRound] = useState(1);
  const [gameComplete, setGameComplete] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  const maxRounds = 10;

  useEffect(() => {
    startNewRound();
  }, []);

  const generateClues = (country: Country): MysteryClue[] => {
    const allClues: MysteryClue[] = [];

    // Geography clues
    allClues.push({
      text: `I am located in ${country.region}.`,
      type: 'geography'
    });
    allClues.push({
      text: `My capital city is ${country.capital}.`,
      type: 'geography'
    });
    allClues.push({
      text: `My currency is ${country.currency}.`,
      type: 'geography'
    });

    // Landmark clues
    if (country.landmarks.length > 0) {
      allClues.push({
        text: `I am home to ${country.landmarks[0]}.`,
        type: 'landmark'
      });
      if (country.landmarks.length > 1) {
        allClues.push({
          text: `You can visit ${country.landmarks[1]} in my territory.`,
          type: 'landmark'
        });
      }
    }

    // Cultural clues
    if (country.culturalElements.cuisine.length > 0) {
      allClues.push({
        text: `My people enjoy traditional dishes like ${country.culturalElements.cuisine[0]}.`,
        type: 'culture'
      });
    }
    if (country.culturalElements.music.length > 0) {
      allClues.push({
        text: `${country.culturalElements.music[0]} music is popular in my culture.`,
        type: 'culture'
      });
    }

    // Language clues
    if (country.languages.length > 1) {
      allClues.push({
        text: `My people speak ${country.languages[0]} and ${country.languages[1]}.`,
        type: 'culture'
      });
    } else {
      allClues.push({
        text: `The main language spoken here is ${country.languages[0]}.`,
        type: 'culture'
      });
    }

    // Wildlife clues
    if (country.wildlife.length > 0) {
      allClues.push({
        text: `${country.wildlife[0]} can be found in my wilderness.`,
        type: 'geography'
      });
    }

    // Historical clues
    const independenceYear = new Date(country.independence).getFullYear();
    if (independenceYear > 1800) {
      allClues.push({
        text: `I gained independence in ${independenceYear}.`,
        type: 'history'
      });
    }

    // Notable figures
    if (country.notableFigures.length > 0) {
      allClues.push({
        text: `${country.notableFigures[0].name} is one of my famous citizens, known for ${country.notableFigures[0].field}.`,
        type: 'notable-figure'
      });
    }

    // Export clues
    if (country.exports.length > 0) {
      allClues.push({
        text: `I am known for exporting ${country.exports[0]}.`,
        type: 'geography'
      });
    }

    // Population clue
    if (country.population > 50000000) {
      allClues.push({
        text: `I have a large population of over ${Math.floor(country.population / 1000000)} million people.`,
        type: 'geography'
      });
    } else if (country.population < 5000000) {
      allClues.push({
        text: `I am a smaller country with less than 5 million people.`,
        type: 'geography'
      });
    }

    return shuffleArray(allClues).slice(0, 6);
  };

  const startNewRound = () => {
    const selectedCountry = getRandomItems(countries, 1)[0];
    const wrongOptions = getRandomItems(
      countries.filter(c => c.id !== selectedCountry.id),
      3
    ).map(c => c.name);
    
    setCurrentCountry(selectedCountry);
    setClues(generateClues(selectedCountry));
    setRevealedClues(1);
    setOptions(shuffleArray([selectedCountry.name, ...wrongOptions]));
    setSelectedAnswer(null);
    setShowResult(false);
    setIsCorrect(false);
  };

  const revealNextClue = () => {
    if (revealedClues < clues.length) {
      setRevealedClues(revealedClues + 1);
    }
  };

  const handleGuess = (answer: string) => {
    if (selectedAnswer) return;
    
    setSelectedAnswer(answer);
    const correct = answer === currentCountry?.name;
    setIsCorrect(correct);
    setShowResult(true);

    if (correct) {
      // Calculate score based on how many clues were revealed
      const baseScore = 20;
      const clueBonus = Math.max(0, (clues.length - revealedClues) * 5);
      const roundScore = baseScore + clueBonus;
      setScore(score + roundScore);
    }

    setTimeout(() => {
      if (round < maxRounds) {
        setRound(round + 1);
        startNewRound();
      } else {
        setGameComplete(true);
        onComplete(score);
      }
    }, 3000);
  };

  const getClueIcon = (type: MysteryClue['type']) => {
    switch (type) {
      case 'landmark': return '🏛️';
      case 'culture': return '🎭';
      case 'geography': return '🗺️';
      case 'history': return '📚';
      case 'notable-figure': return '👤';
      default: return '💡';
    }
  };

  if (!currentCountry) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">🕵️ Mystery Land</h2>
        <div className="flex justify-center space-x-6 mb-4">
          <div className="text-yellow-400">Round: {round}/{maxRounds}</div>
          <div className="text-yellow-400">Score: {score}</div>
        </div>
        <p className="text-gray-300">
          Use the clues to guess which African country I am describing!
        </p>
      </div>

      {/* Game Board */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Clues Section */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">🔍 Clues</h3>
          
          <div className="space-y-4">
            {clues.slice(0, revealedClues).map((clue, index) => (
              <div
                key={index}
                className="bg-gray-700 border border-gray-600 rounded-lg p-4 animate-slide-up"
              >
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{getClueIcon(clue.type)}</span>
                  <div>
                    <div className="text-white">{clue.text}</div>
                    <div className="text-gray-400 text-xs mt-1 capitalize">
                      {clue.type.replace('-', ' ')} clue
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Reveal Next Clue Button */}
          {revealedClues < clues.length && !selectedAnswer && (
            <button
              onClick={revealNextClue}
              className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-500 transition-colors"
            >
              💡 Reveal Next Clue ({revealedClues}/{clues.length})
            </button>
          )}

          {/* Clue Progress */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-400 mb-1">
              <span>Clues Revealed</span>
              <span>{revealedClues}/{clues.length}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(revealedClues / clues.length) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Answer Section */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">🤔 Your Guess</h3>
          
          <div className="space-y-3">
            {options.map((option, index) => {
              const isSelected = selectedAnswer === option;
              const isCorrect = option === currentCountry.name && showResult;
              const isWrong = isSelected && !isCorrect && showResult;
              
              let buttonClass = 'w-full p-4 rounded-lg border-2 transition-all duration-200 text-left';
              
              if (showResult) {
                if (isCorrect) {
                  buttonClass += ' bg-green-500 bg-opacity-20 border-green-400 text-green-300';
                } else if (isWrong) {
                  buttonClass += ' bg-red-500 bg-opacity-20 border-red-400 text-red-300';
                } else {
                  buttonClass += ' bg-gray-700 border-gray-600 text-gray-400';
                }
              } else {
                buttonClass += ' bg-gray-700 border-gray-600 text-white hover:border-yellow-400 cursor-pointer';
              }

              return (
                <button
                  key={index}
                  className={buttonClass}
                  onClick={() => handleGuess(option)}
                  disabled={!!selectedAnswer}
                >
                  <div className="flex items-center justify-between">
                    <span>{option}</span>
                    {showResult && isCorrect && <span>✓</span>}
                    {showResult && isWrong && <span>✗</span>}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Score Calculation */}
          {showResult && (
            <div className="mt-6 p-4 bg-gray-700 rounded-lg">
              <h4 className="text-white font-semibold mb-2">
                {isCorrect ? '🎉 Correct!' : '❌ Wrong Answer'}
              </h4>
              {isCorrect ? (
                <div className="text-green-300">
                  <p>Base Score: 20 points</p>
                  <p>Clue Bonus: +{Math.max(0, (clues.length - revealedClues) * 5)} points</p>
                  <p className="font-bold">Round Score: {20 + Math.max(0, (clues.length - revealedClues) * 5)} points</p>
                </div>
              ) : (
                <div className="text-red-300">
                  <p>The correct answer was: <strong>{currentCountry.name}</strong></p>
                  <p>No points earned this round.</p>
                </div>
              )}
            </div>
          )}

          {/* Hint */}
          {!selectedAnswer && (
            <div className="mt-6 p-4 bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg">
              <p className="text-yellow-400 text-sm">
                💡 <strong>Tip:</strong> Guess early for bonus points! Each unused clue gives you +5 points.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {score >= 150 ? '🏆' : score >= 100 ? '🎉' : '🕵️'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Mystery Solved!
                </h3>
                <p className="text-gray-300 mb-2">
                  You completed all {maxRounds} rounds!
                </p>
                <p className="text-2xl text-yellow-400 font-bold">
                  Final Score: {score} points
                </p>
                <p className="text-gray-400 text-sm mt-2">
                  {score >= 150 ? 'Master Detective!' : 
                   score >= 100 ? 'Great Detective Work!' : 
                   'Keep Investigating!'}
                </p>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={() => window.location.reload()}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MysteryLand;
