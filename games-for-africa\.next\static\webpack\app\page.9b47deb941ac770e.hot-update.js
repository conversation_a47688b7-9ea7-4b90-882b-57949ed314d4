"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 || currentRound && currentRound.matches === getDifficultySettings(difficulty).pairs) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        currentRound\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 278,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-2\",\n                                                    children: flag.country.flagUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl mb-2\",\n                            children: lastMatchedCountry.flagUrl\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\\'s Up!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound.matches\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    settings.pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            Math.round(currentRound.matches / Math.max(currentRound.attempts, 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 32\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"lK7sZ8u6dyR8Xte60Z2D2JGS/bI=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ })

});