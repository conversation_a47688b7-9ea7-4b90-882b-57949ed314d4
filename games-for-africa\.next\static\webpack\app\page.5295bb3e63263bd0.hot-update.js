"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n            // Check if puzzle is complete\n            if (placedPieces.length + 1 === pieces.length) {\n                setGameComplete(true);\n                const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n                const moveBonus = Math.max(0, 50 - moves);\n                const finalScore = score + 10 + timeBonus + moveBonus;\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"\\uD83C\\uDFC6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Puzzle Master!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Time: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 505,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"XIwMd1ScSmMWPsknuzPjUjEGO44=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});