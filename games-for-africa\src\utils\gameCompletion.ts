// Game completion utilities and animations
import { GameType } from '@/types';

export interface GameCompletionData {
  gameType: GameType;
  score: number;
  timeRemaining: number;
  totalTime: number;
  perfectScore: boolean;
  difficulty: 'easy' | 'medium' | 'hard';
  completionTime: number;
}

export interface CompletionResult {
  finalScore: number;
  timeBonus: number;
  xpGained: number;
  achievements: string[];
  message: string;
  celebrationType: 'perfect' | 'excellent' | 'good' | 'complete';
}

// Calculate time bonus based on remaining time
export const calculateTimeBonus = (timeRemaining: number, totalTime: number, difficulty: 'easy' | 'medium' | 'hard'): number => {
  if (timeRemaining <= 0) return 0;
  
  const timePercentage = timeRemaining / totalTime;
  const difficultyMultiplier = {
    easy: 1,
    medium: 1.5,
    hard: 2
  }[difficulty];
  
  // Base time bonus: up to 50 points for completing with full time remaining
  const baseBonus = Math.floor(timePercentage * 50);
  return Math.floor(baseBonus * difficultyMultiplier);
};

// Calculate XP based on performance
export const calculateXP = (data: GameCompletionData): number => {
  const baseXP = 20; // Base XP for completion
  const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points
  const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);
  const difficultyBonus = {
    easy: 0,
    medium: 10,
    hard: 20
  }[data.difficulty];
  
  const perfectBonus = data.perfectScore ? 30 : 0;
  
  return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;
};

// Determine completion message and type
export const getCompletionMessage = (data: GameCompletionData): { message: string; type: 'perfect' | 'excellent' | 'good' | 'complete' } => {
  const timePercentage = data.timeRemaining / data.totalTime;
  
  if (data.perfectScore && timePercentage > 0.7) {
    return { message: 'Perfect! Outstanding performance!', type: 'perfect' };
  } else if (data.perfectScore && timePercentage > 0.4) {
    return { message: 'Excellent! Great job!', type: 'excellent' };
  } else if (data.perfectScore) {
    return { message: 'Perfect Score! Well done!', type: 'excellent' };
  } else if (timePercentage > 0.5) {
    return { message: 'Great work! Fast completion!', type: 'good' };
  } else {
    return { message: 'Game Complete! Nice job!', type: 'complete' };
  }
};

// Process game completion
export const processGameCompletion = (data: GameCompletionData): CompletionResult => {
  const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);
  const finalScore = data.score + timeBonus;
  const xpGained = calculateXP(data);
  const { message, type } = getCompletionMessage(data);
  
  // Determine achievements based on performance
  const achievements: string[] = [];
  const timePercentage = data.timeRemaining / data.totalTime;
  
  if (data.perfectScore) {
    achievements.push('Perfect Score');
  }
  
  if (timePercentage > 0.8) {
    achievements.push('Speed Demon');
  }
  
  if (data.difficulty === 'hard' && data.perfectScore) {
    achievements.push('Master Player');
  }
  
  if (timeBonus > 30) {
    achievements.push('Time Master');
  }
  
  return {
    finalScore,
    timeBonus,
    xpGained,
    achievements,
    message,
    celebrationType: type
  };
};

// Game-specific completion checkers
export const checkGameCompletion = {
  'flag-matching': (matches: number, totalPairs: number): boolean => {
    return matches >= totalPairs;
  },
  
  'country-name-scramble': (currentRound: number, totalRounds: number, roundComplete: boolean): boolean => {
    return currentRound >= totalRounds - 1 && roundComplete;
  },
  
  'jigsaw-puzzle': (placedPieces: number, totalPieces: number): boolean => {
    return placedPieces >= totalPieces;
  },
  
  'quiz': (answeredQuestions: number, totalQuestions: number): boolean => {
    return answeredQuestions >= totalQuestions;
  },
  
  'memory-grid': (matchedPairs: number, totalPairs: number): boolean => {
    return matchedPairs >= totalPairs;
  },
  
  'matching': (matchedPairs: number, totalPairs: number): boolean => {
    return matchedPairs >= totalPairs;
  },
  
  'country-explorer': (visitedCountries: number, targetCountries: number): boolean => {
    return visitedCountries >= targetCountries;
  },
  
  'speed-challenge': (answeredQuestions: number, targetQuestions: number): boolean => {
    return answeredQuestions >= targetQuestions;
  },
  
  'mystery-land': (correctGuesses: number, totalRounds: number): boolean => {
    return correctGuesses >= totalRounds;
  },
  
  'timeline-builder': (placedEvents: number, totalEvents: number): boolean => {
    return placedEvents >= totalEvents;
  },
  
  'where-in-africa': (completedRounds: number, totalRounds: number): boolean => {
    return completedRounds >= totalRounds;
  },
  
  'dress-character': (completedOutfits: number, targetOutfits: number): boolean => {
    return completedOutfits >= targetOutfits;
  }
};

// Celebration animation configurations
export const celebrationConfigs = {
  perfect: {
    duration: 3000,
    emoji: '🏆',
    colors: ['#FFD700', '#FFA500', '#FF6B6B'],
    particles: 50
  },
  excellent: {
    duration: 2500,
    emoji: '🎉',
    colors: ['#4ECDC4', '#45B7D1', '#96CEB4'],
    particles: 40
  },
  good: {
    duration: 2000,
    emoji: '👏',
    colors: ['#FECA57', '#48CAE4', '#A8E6CF'],
    particles: 30
  },
  complete: {
    duration: 1500,
    emoji: '✅',
    colors: ['#6C5CE7', '#A29BFE', '#74B9FF'],
    particles: 20
  }
};

// Auto-progression logic
export const getNextAction = (gameType: GameType, difficulty: 'easy' | 'medium' | 'hard'): { action: 'next-difficulty' | 'menu' | 'replay'; nextDifficulty?: 'easy' | 'medium' | 'hard' } => {
  // If completed on easy, progress to medium
  if (difficulty === 'easy') {
    return { action: 'next-difficulty', nextDifficulty: 'medium' };
  }

  // If completed on medium, progress to hard
  if (difficulty === 'medium') {
    return { action: 'next-difficulty', nextDifficulty: 'hard' };
  }

  // If completed on hard, return to menu
  return { action: 'menu' };
};

// Enhanced completion result with auto-progression data
export interface EnhancedCompletionResult extends CompletionResult {
  autoProgression: {
    action: 'next-difficulty' | 'menu' | 'replay';
    nextDifficulty?: 'easy' | 'medium' | 'hard';
    delay: number; // milliseconds before auto-progression
  };
}

// Process game completion with auto-progression
export const processGameCompletionWithProgression = (data: GameCompletionData): EnhancedCompletionResult => {
  const baseResult = processGameCompletion(data);
  const progression = getNextAction(data.gameType, data.difficulty);

  // Determine delay based on celebration type
  const delay = {
    perfect: 3000,
    excellent: 2500,
    good: 2000,
    complete: 1500
  }[baseResult.celebrationType];

  return {
    ...baseResult,
    autoProgression: {
      ...progression,
      delay
    }
  };
};
