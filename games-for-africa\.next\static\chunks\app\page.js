/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTUVFSyUyMEVERU4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDR0FNRVMlMjBGT1IlMjBBUklDQSU1QyU1Q2dhbWVzLWZvci1hZnJpY2ElNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Interactive Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Country Matching',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'memory',\n        name: 'Memory Cards',\n        description: 'Find matching pairs of African landmarks and cultural symbols',\n        icon: '🃏',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_3__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = (score)=>{\n        console.log('Game completed with score:', score);\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    if (currentGame === 'quiz') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            countries: countries,\n                            category: selectedCategory,\n                            difficulty: selectedDifficulty,\n                            onComplete: handleGameComplete\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-accent-gold font-medium\",\n                                children: \"Score: 0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border border-gray-700 rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer\",\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: game.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⏱️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    game.estimatedTime,\n                                                                    \"min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-4 flex-grow\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                        children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            game.maxScore,\n                                                            \" pts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                children: \"▶️ Play Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                }, game.type, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"g4JRP421rujMtt/9zwXe7LlkLmM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/games/QuizGame.tsx":
/*!*******************************************!*\
  !*** ./src/components/games/QuizGame.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QuizGame = (param)=>{\n    let { countries, category, difficulty, onComplete } = param;\n    var _currentQuestion_options;\n    _s();\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestion: 0,\n        questions: [],\n        selectedAnswers: [],\n        score: 0,\n        timeRemaining: 300,\n        isComplete: false,\n        showExplanation: false\n    });\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize game\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizGame.useEffect\": ()=>{\n            const questions = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateQuizQuestions)(countries, category, difficulty, 10);\n            const initialState = {\n                ...gameState,\n                questions,\n                selectedAnswers: new Array(questions.length).fill(null)\n            };\n            setGameState(initialState);\n        }\n    }[\"QuizGame.useEffect\"], [\n        countries,\n        category,\n        difficulty\n    ]);\n    // Timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizGame.useEffect\": ()=>{\n            if (gameState.timeRemaining > 0 && !gameState.isComplete) {\n                const timer = setTimeout({\n                    \"QuizGame.useEffect.timer\": ()=>{\n                        setGameState({\n                            \"QuizGame.useEffect.timer\": (prev)=>({\n                                    ...prev,\n                                    timeRemaining: prev.timeRemaining - 1\n                                })\n                        }[\"QuizGame.useEffect.timer\"]);\n                    }\n                }[\"QuizGame.useEffect.timer\"], 1000);\n                return ({\n                    \"QuizGame.useEffect\": ()=>clearTimeout(timer)\n                })[\"QuizGame.useEffect\"];\n            } else if (gameState.timeRemaining === 0) {\n                handleGameComplete();\n            }\n        }\n    }[\"QuizGame.useEffect\"], [\n        gameState.timeRemaining,\n        gameState.isComplete\n    ]);\n    const handleAnswerSelect = (answer)=>{\n        const newAnswers = [\n            ...gameState.selectedAnswers\n        ];\n        newAnswers[gameState.currentQuestion] = answer;\n        setGameState((prev)=>({\n                ...prev,\n                selectedAnswers: newAnswers,\n                showExplanation: true\n            }));\n    };\n    const handleNextQuestion = ()=>{\n        if (gameState.currentQuestion < gameState.questions.length - 1) {\n            setGameState((prev)=>({\n                    ...prev,\n                    currentQuestion: prev.currentQuestion + 1,\n                    showExplanation: false\n                }));\n        } else {\n            handleGameComplete();\n        }\n    };\n    const handleGameComplete = ()=>{\n        const correctAnswers = gameState.selectedAnswers.filter((answer, index)=>{\n            var _gameState_questions_index;\n            return answer === ((_gameState_questions_index = gameState.questions[index]) === null || _gameState_questions_index === void 0 ? void 0 : _gameState_questions_index.correctAnswer);\n        }).length;\n        const finalScore = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.calculateScore)(correctAnswers, gameState.questions.length, 0, difficulty === 'easy' ? 1 : difficulty === 'medium' ? 1.5 : 2);\n        setGameState((prev)=>({\n                ...prev,\n                score: finalScore,\n                isComplete: true\n            }));\n        setShowResults(true);\n        onComplete(finalScore);\n    };\n    const handleRestart = ()=>{\n        const questions = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateQuizQuestions)(countries, category, difficulty, 10);\n        setGameState({\n            currentQuestion: 0,\n            questions,\n            selectedAnswers: new Array(questions.length).fill(null),\n            score: 0,\n            timeRemaining: 300,\n            isComplete: false,\n            showExplanation: false\n        });\n        setShowResults(false);\n    };\n    const currentQuestion = gameState.questions[gameState.currentQuestion];\n    const selectedAnswer = gameState.selectedAnswers[gameState.currentQuestion];\n    const progress = (gameState.currentQuestion + 1) / gameState.questions.length * 100;\n    if (!currentQuestion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-gold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: [\n                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                    \" Quiz\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono\",\n                                                children: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(gameState.timeRemaining)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            gameState.currentQuestion + 1,\n                                            \" / \",\n                                            gameState.questions.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-400 h-2 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-white mb-6\",\n                            children: currentQuestion.question\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-3\",\n                            children: (_currentQuestion_options = currentQuestion.options) === null || _currentQuestion_options === void 0 ? void 0 : _currentQuestion_options.map((option, index)=>{\n                                const isSelected = selectedAnswer === option;\n                                const isCorrect = option === currentQuestion.correctAnswer;\n                                const isIncorrect = isSelected && !isCorrect && gameState.showExplanation;\n                                let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';\n                                if (gameState.showExplanation) {\n                                    if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';\n                                    else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';\n                                } else if (isSelected) {\n                                    optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: optionClass,\n                                    onClick: ()=>!gameState.showExplanation && handleAnswerSelect(option),\n                                    disabled: gameState.showExplanation,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-left text-white\",\n                                                children: option\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            gameState.showExplanation && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            gameState.showExplanation && isIncorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: \"✗\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined),\n                        gameState.showExplanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-900 rounded-lg border border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300\",\n                                    children: currentQuestion.explanation\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextQuestion,\n                                        className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg font-medium hover:bg-yellow-300 transition-colors\",\n                                        children: gameState.currentQuestion < gameState.questions.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"Next Question →\"\n                                        }, void 0, false) : 'Complete Quiz'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, gameState.currentQuestion, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            showResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: gameState.score >= 80 ? '🎉' : gameState.score >= 60 ? '👏' : '💪'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: [\n                                            gameState.score,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"You got \",\n                                            gameState.selectedAnswers.filter((answer, index)=>{\n                                                var _gameState_questions_index;\n                                                return answer === ((_gameState_questions_index = gameState.questions[index]) === null || _gameState_questions_index === void 0 ? void 0 : _gameState_questions_index.correctAnswer);\n                                            }).length,\n                                            \" out of \",\n                                            gameState.questions.length,\n                                            \" questions correct!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowResults(false),\n                                        className: \"bg-gray-700 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRestart,\n                                        className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                        children: \"\\uD83D\\uDD04 Play Again\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuizGame, \"G8126pXD5idJ6CAUz7VKztjyoIs=\");\n_c = QuizGame;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizGame);\nvar _c;\n$RefreshReg$(_c, \"QuizGame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/QuizGame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/countries.json":
/*!*********************************!*\
  !*** ./src/data/countries.json ***!
  \*********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"countries":[{"id":"nigeria","name":"Nigeria","capital":"Abuja","flagUrl":"🇳🇬","currency":"Nigerian Naira","languages":["English","Hausa","Yoruba","Igbo"],"population":218541000,"region":"West Africa","independence":"1960-10-01","exports":["Crude oil","Natural gas","Cocoa","Cashew nuts"],"landmarks":["Zuma Rock","Olumo Rock","Yankari National Park"],"wildlife":["African elephant","Chimpanzee","Hippopotamus","Crocodile"],"culturalElements":{"traditionalClothing":["Agbada","Dashiki","Gele","Iro and Buba"],"cuisine":["Jollof rice","Suya","Egusi soup","Pounded yam"],"music":["Afrobeat","Highlife","Fuji","Juju"],"dances":["Bata","Ekombi","Swange","Koroso"]},"notableFigures":[{"name":"Chinua Achebe","field":"Literature","achievement":"Author of \'Things Fall Apart\', one of the most widely read African novels"},{"name":"Wole Soyinka","field":"Literature","achievement":"First African to win the Nobel Prize in Literature (1986)"}]},{"id":"kenya","name":"Kenya","capital":"Nairobi","flagUrl":"🇰🇪","currency":"Kenyan Shilling","languages":["English","Swahili"],"population":54027000,"region":"East Africa","independence":"1963-12-12","exports":["Tea","Coffee","Flowers","Vegetables"],"landmarks":["Mount Kenya","Maasai Mara","Lake Victoria","Amboseli National Park"],"wildlife":["Lion","Elephant","Giraffe","Zebra","Wildebeest"],"culturalElements":{"traditionalClothing":["Kanga","Kitenge","Maasai Shuka"],"cuisine":["Ugali","Nyama choma","Sukuma wiki","Chapati"],"music":["Benga","Taarab","Ohangla"],"dances":["Mugithi","Chakacha","Adungu"]},"notableFigures":[{"name":"Wangari Maathai","field":"Environmental activism","achievement":"First African woman to win Nobel Peace Prize (2004) for environmental conservation"},{"name":"Eliud Kipchoge","field":"Athletics","achievement":"Marathon world record holder and first person to run sub-2-hour marathon"}]},{"id":"south-africa","name":"South Africa","capital":"Cape Town","flagUrl":"🇿🇦","currency":"South African Rand","languages":["Afrikaans","English","Zulu","Xhosa","Sotho"],"population":60414000,"region":"Southern Africa","independence":"1910-05-31","exports":["Gold","Diamonds","Platinum","Wine"],"landmarks":["Table Mountain","Kruger National Park","Robben Island","Drakensberg Mountains"],"wildlife":["Big Five","Penguin","Great white shark","Cheetah"],"culturalElements":{"traditionalClothing":["Shweshwe","Ndebele beadwork","Zulu traditional dress"],"cuisine":["Braai","Bobotie","Biltong","Boerewors"],"music":["Kwaito","Amapiano","Maskandi","Marabi"],"dances":["Gumboot dance","Zulu dance","Pantsula"]},"notableFigures":[{"name":"Nelson Mandela","field":"Politics","achievement":"Anti-apartheid leader and first Black president of South Africa"},{"name":"Desmond Tutu","field":"Human rights","achievement":"Nobel Peace Prize winner and anti-apartheid activist"}]},{"id":"egypt","name":"Egypt","capital":"Cairo","flagUrl":"🇪🇬","currency":"Egyptian Pound","languages":["Arabic"],"population":104258000,"region":"North Africa","independence":"1922-02-28","exports":["Petroleum","Natural gas","Cotton","Textiles"],"landmarks":["Pyramids of Giza","Sphinx","Valley of the Kings","Abu Simbel"],"wildlife":["Nile crocodile","Egyptian cobra","Fennec fox","Ibis"],"culturalElements":{"traditionalClothing":["Galabeya","Hijab","Tarboosh"],"cuisine":["Ful medames","Koshari","Molokhia","Baklava"],"music":["Shaabi","Classical Arabic","Mahraganat"],"dances":["Raqs sharqi","Saidi","Baladi"]},"notableFigures":[{"name":"Naguib Mahfouz","field":"Literature","achievement":"Nobel Prize in Literature winner (1988) for Arabic literature"},{"name":"Mohamed Salah","field":"Football","achievement":"Premier League and Champions League winner with Liverpool"}]},{"id":"ghana","name":"Ghana","capital":"Accra","flagUrl":"🇬🇭","currency":"Ghanaian Cedi","languages":["English","Akan","Ewe","Ga"],"population":********,"region":"West Africa","independence":"1957-03-06","exports":["Gold","Cocoa","Oil","Timber"],"landmarks":["Cape Coast Castle","Kakum National Park","Lake Volta"],"wildlife":["Forest elephant","Bongo antelope","Colobus monkey"],"culturalElements":{"traditionalClothing":["Kente cloth","Adinkra symbols","Smock"],"cuisine":["Jollof rice","Banku","Fufu","Kelewele"],"music":["Highlife","Hiplife","Azonto"],"dances":["Adowa","Kpanlogo","Bamaya"]},"notableFigures":[{"name":"Kwame Nkrumah","field":"Politics","achievement":"First President of Ghana and Pan-African leader"},{"name":"Kofi Annan","field":"Diplomacy","achievement":"Former UN Secretary-General and Nobel Peace Prize winner"}]}]}');

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Array shuffling utility\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(Math.random() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(Math.random() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(Math.random() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(Math.random() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(Math.random() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(Math.random() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(Math.random() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMEEK%20EDEN%5C%5CDocuments%5C%5Caugment-projects%5C%5CGAMES%20FOR%20ARICA%5C%5Cgames-for-africa%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);