"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_FlagMatching_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [showMismatchMessage, setShowMismatchMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mismatchedPairs, setMismatchedPairs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false,\n                isEliminated: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false,\n                isEliminated: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (currentRound && gameStarted && !gameComplete && !showVictory) {\n                const settings = getDifficultySettings(difficulty);\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['flag-matching'](currentRound.matches, settings.pairs)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = (newDifficulty)=>{\n        const targetDifficulty = newDifficulty || difficulty;\n        setDifficulty(targetDifficulty);\n        const settings = getDifficultySettings(targetDifficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n        setShowMismatchMessage(false);\n        setMismatchedPairs([]);\n    };\n    const handleImmediateCompletion = ()=>{\n        if (!currentRound) return;\n        const settings = getDifficultySettings(difficulty);\n        const totalTime = settings.timeLimit;\n        const completionTime = (Date.now() - gameStartTime) / 1000;\n        const gameData = {\n            gameType: 'flag-matching',\n            score,\n            timeRemaining: timeLeft,\n            totalTime,\n            perfectScore: currentRound.matches === settings.pairs && currentRound.attempts === settings.pairs,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletionWithProgression)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched || flag.isEliminated) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched || name.isEliminated) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - eliminate both items and show mismatch message\n                setStreak(0);\n                setShowMismatchMessage(true);\n                // Add to mismatched pairs for tracking\n                setMismatchedPairs((prev)=>[\n                        ...prev,\n                        {\n                            flagId: currentRound.selectedFlag,\n                            nameId: nameId\n                        }\n                    ]);\n                // Eliminate both selected items\n                const eliminatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isEliminated: f.id === currentRound.selectedFlag ? true : f.isEliminated,\n                        isSelected: false\n                    }));\n                const eliminatedNames = newRound.names.map((n)=>({\n                        ...n,\n                        isEliminated: n.id === nameId ? true : n.isEliminated,\n                        isSelected: false\n                    }));\n                setCurrentRound({\n                    ...newRound,\n                    flags: eliminatedFlags,\n                    names: eliminatedNames,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n                // Hide mismatch message after 2 seconds\n                setTimeout(()=>setShowMismatchMessage(false), 2000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        if (!showVictory) {\n            // Time ran out - no victory animation, just complete\n            setGameComplete(true);\n            setTimeout(()=>onComplete(score), 1000);\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const handleAutoProgress = (action, nextDifficulty)=>{\n        if (action === 'next-difficulty' && nextDifficulty) {\n            // Start the same game with next difficulty\n            startGame(nextDifficulty);\n        } else {\n            // Return to menu\n            if (completionData) {\n                onComplete(completionData.finalScore);\n            }\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 367,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched || flag.isEliminated,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isEliminated ? 'bg-red-500 bg-opacity-20 border-red-400 cursor-not-allowed opacity-50' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        countryId: flag.country.id,\n                                                        size: \"xl\",\n                                                        className: \"mx-auto \".concat(flag.isEliminated ? 'grayscale opacity-50' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                flag.isEliminated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-red-400 text-sm font-medium\",\n                                                    children: \"✗ Eliminated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched || name.isEliminated,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isEliminated ? 'bg-red-500 bg-opacity-20 border-red-400 cursor-not-allowed opacity-50' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(name.isEliminated ? 'text-gray-400 line-through' : 'text-white'),\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                name.isEliminated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400\",\n                                                    children: \"✗\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                countryId: lastMatchedCountry.id,\n                                size: \"large\",\n                                className: \"mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, undefined),\n            showMismatchMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 left-1/2 transform -translate-x-1/2 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-500 bg-opacity-90 rounded-lg p-4 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"❌ Incorrect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-100 text-sm\",\n                            children: \"Both items have been eliminated\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete,\n                onAutoProgress: handleAutoProgress\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 535,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && !showVictory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"⏰\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Time's Up!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: (currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    getDifficultySettings(difficulty).pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 545,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"M3DaW9IR7GwtmtK4ONlVEWgI+xM=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(app-pages-browser)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagImage = (param)=>{\n    let { countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick } = param;\n    _s();\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = \"\".concat(flagImageUrl, \"?cache=1\");\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    object-cover \\n    rounded-sm \\n    border \\n    border-gray-300 \\n    shadow-sm\\n    \").concat(onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : '', \"\\n    \").concat(className, \"\\n  \");\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-200 animate-pulse flex items-center justify-center\"),\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          \".concat(sizeClasses[size], \" \\n          flex \\n          items-center \\n          justify-center \\n          bg-gray-100 \\n          rounded-sm \\n          border \\n          border-gray-300\\n          \").concat(onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : '', \"\\n          \").concat(className, \"\\n        \"),\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(baseClasses, \" bg-gray-100 flex items-center justify-center\"),\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagImage, \"mgIVjR2Tvb/QtAQzLx0hN0fqtHE=\");\n_c = FlagImage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\nvar _c;\n$RefreshReg$(_c, \"FlagImage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0ZsYWdJbWFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVtRDtBQUN3QjtBQVczRSxNQUFNTSxZQUFzQztRQUFDLEVBQzNDQyxTQUFTLEVBQ1RDLE9BQU8sUUFBUSxFQUNmQyxTQUFTLEtBQUssRUFDZEMsWUFBWSxFQUFFLEVBQ2RDLGVBQWUsSUFBSSxFQUNuQkMsT0FBTyxFQUNSOztJQUNDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHYiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNjLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFFM0MsZ0VBQWdFO0lBQ2hFLE1BQU1rQixlQUFlaEIsOERBQVlBLENBQUNJLFdBQVdFO0lBQzdDLE1BQU1XLFlBQVloQiw4REFBWUEsQ0FBQ0c7SUFDL0IsTUFBTWMsVUFBVWhCLDREQUFVQSxDQUFDRTtJQUUzQixzQkFBc0I7SUFDdEIsTUFBTWUsY0FBYztRQUNsQkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsYUFBYTtRQUNqQkosT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsSUFBSTtJQUNOO0lBRUF4QixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNpQixjQUFjO2dCQUNqQkgsY0FBYztnQkFDZEUsYUFBYTtnQkFDYjtZQUNGO1lBRUFKLGVBQWU7WUFDZkUsY0FBYztZQUNkRSxhQUFhO1lBRWIsOENBQThDO1lBQzlDLE1BQU1VLE1BQU0sSUFBSUM7WUFDaEJELElBQUlFLFdBQVcsR0FBRyxhQUFhLGlDQUFpQztZQUNoRUYsSUFBSUcsT0FBTyxHQUFHLFNBQVMsdUNBQXVDO1lBRTlESCxJQUFJSSxNQUFNO3VDQUFHO29CQUNYbEIsZUFBZTtvQkFDZkUsY0FBYztvQkFDZEUsYUFBYTtnQkFDZjs7WUFFQVUsSUFBSUssT0FBTzt1Q0FBRztvQkFDWm5CLGVBQWU7b0JBQ2ZFLGNBQWM7b0JBQ2RFLGFBQWE7Z0JBQ2Y7O1lBRUEsZ0RBQWdEO1lBQ2hELE1BQU1nQixZQUFZLEdBQWdCLE9BQWJmLGNBQWE7WUFDbENTLElBQUlPLEdBQUcsR0FBR0Q7WUFFVjt1Q0FBTztvQkFDTE4sSUFBSUksTUFBTSxHQUFHO29CQUNiSixJQUFJSyxPQUFPLEdBQUc7Z0JBQ2hCOztRQUNGOzhCQUFHO1FBQUNkO0tBQWE7SUFFakIsTUFBTWlCLGNBQWMsU0FPaEJ4QixPQU5BVSxXQUFXLENBQUNkLEtBQUssRUFBQyxpR0FPbEJFLE9BREFFLFVBQVUscURBQXFELElBQUcsVUFDeEQsT0FBVkYsV0FBVTtJQUdkLHFCQUFxQjtJQUNyQixJQUFJTyxhQUFhRSxjQUFjO1FBQzdCLHFCQUNFLDhEQUFDa0I7WUFDQzNCLFdBQVcsR0FBZSxPQUFaMEIsYUFBWTtZQUMxQnhCLFNBQVNBO3NCQUVULDRFQUFDeUI7Z0JBQUkzQixXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLHlDQUF5QztJQUN6QyxJQUFJRyxlQUFlTSxnQkFBZ0IsQ0FBQ0osWUFBWTtRQUM5QyxxQkFDRSw4REFBQ2E7WUFDQ08sS0FBS2hCO1lBQ0xtQixLQUFLakI7WUFDTFgsV0FBVzBCO1lBQ1h4QixTQUFTQTtZQUNUMkIsU0FBUyxJQUFNdkIsY0FBYzs7Ozs7O0lBR25DO0lBRUEsc0VBQXNFO0lBQ3RFLElBQUlMLGdCQUFnQkksWUFBWTtRQUM5QixxQkFDRSw4REFBQ3NCO1lBQ0MzQixXQUFXLGVBU1BFLE9BUkFVLFdBQVcsQ0FBQ2QsS0FBSyxFQUFDLG1MQVNsQkUsT0FEQUUsVUFBVSx1REFBdUQsSUFBRyxnQkFDMUQsT0FBVkYsV0FBVTtZQUVkRSxTQUFTQTtZQUNUNEIsT0FBT25CO3NCQUVQLDRFQUFDb0I7Z0JBQUsvQixXQUFXaUIsVUFBVSxDQUFDbkIsS0FBSzswQkFBR1k7Ozs7Ozs7Ozs7O0lBRzFDO0lBRUEsMEJBQTBCO0lBQzFCLHFCQUNFLDhEQUFDaUI7UUFDQzNCLFdBQVcsR0FBZSxPQUFaMEIsYUFBWTtRQUMxQnhCLFNBQVNBO2tCQUVULDRFQUFDNkI7WUFBSy9CLFdBQVU7c0JBQXdCOzs7Ozs7Ozs7OztBQUc5QztHQTNJTUo7S0FBQUE7QUE2SU4saUVBQWVBLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFxjb21wb25lbnRzXFx1aVxcRmxhZ0ltYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0RmxhZ0ltYWdlLCBnZXRGbGFnRW1vamksIGdldEZsYWdBbHQgfSBmcm9tICdAL3V0aWxzL2ZsYWdVdGlscyc7XG5cbmludGVyZmFjZSBGbGFnSW1hZ2VQcm9wcyB7XG4gIGNvdW50cnlJZDogc3RyaW5nO1xuICBzaXplPzogJ3NtYWxsJyB8ICdtZWRpdW0nIHwgJ2xhcmdlJyB8ICd4bCc7XG4gIGZvcm1hdD86ICdzdmcnIHwgJ3BuZyc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgc2hvd0ZhbGxiYWNrPzogYm9vbGVhbjtcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IEZsYWdJbWFnZTogUmVhY3QuRkM8RmxhZ0ltYWdlUHJvcHM+ID0gKHtcbiAgY291bnRyeUlkLFxuICBzaXplID0gJ21lZGl1bScsXG4gIGZvcm1hdCA9ICdzdmcnLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgc2hvd0ZhbGxiYWNrID0gdHJ1ZSxcbiAgb25DbGljayxcbn0pID0+IHtcbiAgY29uc3QgW2ltYWdlTG9hZGVkLCBzZXRJbWFnZUxvYWRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpbWFnZUVycm9yLCBzZXRJbWFnZUVycm9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIFRyeSB0byBnZXQgZmxhZyBpbWFnZSBVUkwsIGZhbGxiYWNrIHRvIGVtb2ppIGlmIG5vdCBhdmFpbGFibGVcbiAgY29uc3QgZmxhZ0ltYWdlVXJsID0gZ2V0RmxhZ0ltYWdlKGNvdW50cnlJZCwgZm9ybWF0KTtcbiAgY29uc3QgZmxhZ0Vtb2ppID0gZ2V0RmxhZ0Vtb2ppKGNvdW50cnlJZCk7XG4gIGNvbnN0IGZsYWdBbHQgPSBnZXRGbGFnQWx0KGNvdW50cnlJZCk7XG5cbiAgLy8gU2l6ZSBjb25maWd1cmF0aW9uc1xuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbWFsbDogJ3ctOCBoLTYnLFxuICAgIG1lZGl1bTogJ3ctMTIgaC05JyxcbiAgICBsYXJnZTogJ3ctMTYgaC0xMicsXG4gICAgeGw6ICd3LTI0IGgtMTgnLFxuICB9O1xuXG4gIGNvbnN0IGVtb2ppU2l6ZXMgPSB7XG4gICAgc21hbGw6ICd0ZXh0LWxnJyxcbiAgICBtZWRpdW06ICd0ZXh0LTJ4bCcsXG4gICAgbGFyZ2U6ICd0ZXh0LTN4bCcsXG4gICAgeGw6ICd0ZXh0LTV4bCcsXG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWZsYWdJbWFnZVVybCkge1xuICAgICAgc2V0SW1hZ2VFcnJvcih0cnVlKTtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SW1hZ2VMb2FkZWQoZmFsc2UpO1xuICAgIHNldEltYWdlRXJyb3IoZmFsc2UpO1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcblxuICAgIC8vIFByZWxvYWQgdGhlIGltYWdlIHdpdGggY2FjaGluZyBvcHRpbWl6YXRpb25cbiAgICBjb25zdCBpbWcgPSBuZXcgSW1hZ2UoKTtcbiAgICBpbWcuY3Jvc3NPcmlnaW4gPSAnYW5vbnltb3VzJzsgLy8gRW5hYmxlIENPUlMgZm9yIGJldHRlciBjYWNoaW5nXG4gICAgaW1nLmxvYWRpbmcgPSAnZWFnZXInOyAvLyBQcmlvcml0aXplIGxvYWRpbmcgZm9yIHZpc2libGUgZmxhZ3NcblxuICAgIGltZy5vbmxvYWQgPSAoKSA9PiB7XG4gICAgICBzZXRJbWFnZUxvYWRlZCh0cnVlKTtcbiAgICAgIHNldEltYWdlRXJyb3IoZmFsc2UpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9O1xuXG4gICAgaW1nLm9uZXJyb3IgPSAoKSA9PiB7XG4gICAgICBzZXRJbWFnZUxvYWRlZChmYWxzZSk7XG4gICAgICBzZXRJbWFnZUVycm9yKHRydWUpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9O1xuXG4gICAgLy8gQWRkIGNhY2hlLWJ1c3RpbmcgcHJldmVudGlvbiBhbmQgb3B0aW1pemF0aW9uXG4gICAgY29uc3QgY2FjaGVkVXJsID0gYCR7ZmxhZ0ltYWdlVXJsfT9jYWNoZT0xYDtcbiAgICBpbWcuc3JjID0gY2FjaGVkVXJsO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGltZy5vbmxvYWQgPSBudWxsO1xuICAgICAgaW1nLm9uZXJyb3IgPSBudWxsO1xuICAgIH07XG4gIH0sIFtmbGFnSW1hZ2VVcmxdKTtcblxuICBjb25zdCBiYXNlQ2xhc3NlcyA9IGBcbiAgICAke3NpemVDbGFzc2VzW3NpemVdfSBcbiAgICBvYmplY3QtY292ZXIgXG4gICAgcm91bmRlZC1zbSBcbiAgICBib3JkZXIgXG4gICAgYm9yZGVyLWdyYXktMzAwIFxuICAgIHNoYWRvdy1zbVxuICAgICR7b25DbGljayA/ICdjdXJzb3ItcG9pbnRlciBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3cnIDogJyd9XG4gICAgJHtjbGFzc05hbWV9XG4gIGA7XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHN0YXRlXG4gIGlmIChpc0xvYWRpbmcgJiYgZmxhZ0ltYWdlVXJsKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgXG4gICAgICAgIGNsYXNzTmFtZT17YCR7YmFzZUNsYXNzZXN9IGJnLWdyYXktMjAwIGFuaW1hdGUtcHVsc2UgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfVxuICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLWdyYXktNDAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBTaG93IGZsYWcgaW1hZ2UgaWYgbG9hZGVkIHN1Y2Nlc3NmdWxseVxuICBpZiAoaW1hZ2VMb2FkZWQgJiYgZmxhZ0ltYWdlVXJsICYmICFpbWFnZUVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbWdcbiAgICAgICAgc3JjPXtmbGFnSW1hZ2VVcmx9XG4gICAgICAgIGFsdD17ZmxhZ0FsdH1cbiAgICAgICAgY2xhc3NOYW1lPXtiYXNlQ2xhc3Nlc31cbiAgICAgICAgb25DbGljaz17b25DbGlja31cbiAgICAgICAgb25FcnJvcj17KCkgPT4gc2V0SW1hZ2VFcnJvcih0cnVlKX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuXG4gIC8vIFNob3cgZW1vamkgZmFsbGJhY2sgaWYgaW1hZ2UgZmFpbGVkIHRvIGxvYWQgb3Igc2hvd0ZhbGxiYWNrIGlzIHRydWVcbiAgaWYgKHNob3dGYWxsYmFjayB8fCBpbWFnZUVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgXG4gICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICR7c2l6ZUNsYXNzZXNbc2l6ZV19IFxuICAgICAgICAgIGZsZXggXG4gICAgICAgICAgaXRlbXMtY2VudGVyIFxuICAgICAgICAgIGp1c3RpZnktY2VudGVyIFxuICAgICAgICAgIGJnLWdyYXktMTAwIFxuICAgICAgICAgIHJvdW5kZWQtc20gXG4gICAgICAgICAgYm9yZGVyIFxuICAgICAgICAgIGJvcmRlci1ncmF5LTMwMFxuICAgICAgICAgICR7b25DbGljayA/ICdjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWNvbG9ycycgOiAnJ31cbiAgICAgICAgICAke2NsYXNzTmFtZX1cbiAgICAgICAgYH1cbiAgICAgICAgb25DbGljaz17b25DbGlja31cbiAgICAgICAgdGl0bGU9e2ZsYWdBbHR9XG4gICAgICA+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT17ZW1vamlTaXplc1tzaXplXX0+e2ZsYWdFbW9qaX08L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gRmFsbGJhY2sgdG8gZW1wdHkgc3RhdGVcbiAgcmV0dXJuIChcbiAgICA8ZGl2IFxuICAgICAgY2xhc3NOYW1lPXtgJHtiYXNlQ2xhc3Nlc30gYmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfVxuICAgICAgb25DbGljaz17b25DbGlja31cbiAgICA+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj7wn4+z77iPPC9zcGFuPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRmxhZ0ltYWdlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJnZXRGbGFnSW1hZ2UiLCJnZXRGbGFnRW1vamkiLCJnZXRGbGFnQWx0IiwiRmxhZ0ltYWdlIiwiY291bnRyeUlkIiwic2l6ZSIsImZvcm1hdCIsImNsYXNzTmFtZSIsInNob3dGYWxsYmFjayIsIm9uQ2xpY2siLCJpbWFnZUxvYWRlZCIsInNldEltYWdlTG9hZGVkIiwiaW1hZ2VFcnJvciIsInNldEltYWdlRXJyb3IiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJmbGFnSW1hZ2VVcmwiLCJmbGFnRW1vamkiLCJmbGFnQWx0Iiwic2l6ZUNsYXNzZXMiLCJzbWFsbCIsIm1lZGl1bSIsImxhcmdlIiwieGwiLCJlbW9qaVNpemVzIiwiaW1nIiwiSW1hZ2UiLCJjcm9zc09yaWdpbiIsImxvYWRpbmciLCJvbmxvYWQiLCJvbmVycm9yIiwiY2FjaGVkVXJsIiwic3JjIiwiYmFzZUNsYXNzZXMiLCJkaXYiLCJhbHQiLCJvbkVycm9yIiwidGl0bGUiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FlagImage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/VictoryAnimation.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VictoryAnimation = (param)=>{\n    let { isVisible, completionData, onComplete, onAutoProgress } = param;\n    _s();\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationPhase, setAnimationPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('enter');\n    const [progressCountdown, setProgressCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProgressing, setIsProgressing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__.celebrationConfigs[completionData.celebrationType];\n    const progressDelay = completionData.autoProgression.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VictoryAnimation.useEffect\": ()=>{\n            if (!isVisible) return;\n            setShowConfetti(true);\n            setAnimationPhase('enter');\n            setIsProgressing(false);\n            // Start countdown for auto-progression\n            const countdownStart = Math.ceil(progressDelay / 1000);\n            setProgressCountdown(countdownStart);\n            const timeline = [\n                // Enter phase\n                {\n                    delay: 0,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('enter')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Display phase\n                {\n                    delay: 300,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('display')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Start countdown\n                {\n                    delay: 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            const countdownInterval = setInterval({\n                                \"VictoryAnimation.useEffect.countdownInterval\": ()=>{\n                                    setProgressCountdown({\n                                        \"VictoryAnimation.useEffect.countdownInterval\": (prev)=>{\n                                            if (prev <= 1) {\n                                                clearInterval(countdownInterval);\n                                                return 0;\n                                            }\n                                            return prev - 1;\n                                        }\n                                    }[\"VictoryAnimation.useEffect.countdownInterval\"]);\n                                }\n                            }[\"VictoryAnimation.useEffect.countdownInterval\"], 1000);\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Exit phase\n                {\n                    delay: progressDelay - 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('exit')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Auto-progress\n                {\n                    delay: progressDelay,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            setShowConfetti(false);\n                            setIsProgressing(true);\n                            if (onAutoProgress) {\n                                onAutoProgress(completionData.autoProgression.action, completionData.autoProgression.nextDifficulty);\n                            } else {\n                                onComplete();\n                            }\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                }\n            ];\n            const timeouts = timeline.map({\n                \"VictoryAnimation.useEffect.timeouts\": (param)=>{\n                    let { delay, action } = param;\n                    return setTimeout(action, delay);\n                }\n            }[\"VictoryAnimation.useEffect.timeouts\"]);\n            return ({\n                \"VictoryAnimation.useEffect\": ()=>{\n                    timeouts.forEach(clearTimeout);\n                }\n            })[\"VictoryAnimation.useEffect\"];\n        }\n    }[\"VictoryAnimation.useEffect\"], [\n        isVisible,\n        progressDelay,\n        onComplete,\n        onAutoProgress,\n        completionData.autoProgression\n    ]);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: config.particles\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute animate-bounce\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                            animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full\",\n                            style: {\n                                backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],\n                                transform: \"rotate(\".concat(Math.random() * 360, \"deg)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          bg-gradient-to-br from-yellow-400 to-orange-500 \\n          rounded-2xl p-8 text-center max-w-md mx-4 \\n          transform transition-all duration-500 ease-out\\n          \".concat(animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : '', \"\\n          \").concat(animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : '', \"\\n          \").concat(animationPhase === 'exit' ? 'scale-110 opacity-90' : '', \"\\n        \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-8xl mb-4 animate-pulse\",\n                        children: config.emoji\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: completionData.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completionData.finalScore\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Final Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                \"+\",\n                                                completionData.timeBonus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Time Bonus\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-blue-900 font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: [\n                                        \"+\",\n                                        completionData.xpGained\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    children: \"XP Gained!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    completionData.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-900 font-semibold text-sm\",\n                                children: \"Achievements Unlocked:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            completionData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm\",\n                                    style: {\n                                        animationDelay: \"\".concat(index * 200, \"ms\")\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 \",\n                                        achievement\n                                    ]\n                                }, achievement, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-300 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out\",\n                                    style: {\n                                        width: animationPhase === 'display' ? \"\".concat(100 - progressCountdown / Math.ceil(progressDelay / 1000) * 100, \"%\") : '0%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-700 text-xs mt-2\",\n                                children: isProgressing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 font-semibold\",\n                                    children: \"Progressing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined) : progressCountdown > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: completionData.autoProgression.action === 'next-difficulty' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Starting \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold capitalize\",\n                                                children: completionData.autoProgression.nextDifficulty\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 30\n                                            }, undefined),\n                                            \" difficulty in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Returning to menu in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-semibold\",\n                                    children: \"Ready!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-yellow-300 text-2xl animate-ping\",\n                            style: {\n                                left: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                top: \"\".concat(20 + Math.random() * 60, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 2, \"s\"),\n                                animationDuration: \"\".concat(2 + Math.random() * 2, \"s\")\n                            },\n                            children: \"⭐\"\n                        }, \"star-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from({\n                        length: 12\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-white text-lg animate-bounce\",\n                            style: {\n                                left: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                top: \"\".concat(10 + Math.random() * 80, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(1 + Math.random() * 2, \"s\")\n                            },\n                            children: \"✨\"\n                        }, \"sparkle-\".concat(i), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            animationPhase === 'display' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-white animate-bounce\",\n                    children: [\n                        completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊',\n                        completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟',\n                        completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏',\n                        completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VictoryAnimation, \"Zn45cHR9HVHvnYlvCAY/3AjTlSY=\");\n_c = VictoryAnimation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VictoryAnimation);\nvar _c;\n$RefreshReg$(_c, \"VictoryAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/VictoryAnimation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = function(countryId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'svg';\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : \"Flag of \".concat(countryId);\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/flagUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/gameCompletion.ts":
/*!*************************************!*\
  !*** ./src/utils/gameCompletion.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   calculateXP: () => (/* binding */ calculateXP),\n/* harmony export */   celebrationConfigs: () => (/* binding */ celebrationConfigs),\n/* harmony export */   checkGameCompletion: () => (/* binding */ checkGameCompletion),\n/* harmony export */   getCompletionMessage: () => (/* binding */ getCompletionMessage),\n/* harmony export */   getNextAction: () => (/* binding */ getNextAction),\n/* harmony export */   processGameCompletion: () => (/* binding */ processGameCompletion),\n/* harmony export */   processGameCompletionWithProgression: () => (/* binding */ processGameCompletionWithProgression)\n/* harmony export */ });\n// Game completion utilities and animations\n// Calculate time bonus based on remaining time\nconst calculateTimeBonus = (timeRemaining, totalTime, difficulty)=>{\n    if (timeRemaining <= 0) return 0;\n    const timePercentage = timeRemaining / totalTime;\n    const difficultyMultiplier = {\n        easy: 1,\n        medium: 1.5,\n        hard: 2\n    }[difficulty];\n    // Base time bonus: up to 50 points for completing with full time remaining\n    const baseBonus = Math.floor(timePercentage * 50);\n    return Math.floor(baseBonus * difficultyMultiplier);\n};\n// Calculate XP based on performance\nconst calculateXP = (data)=>{\n    const baseXP = 20; // Base XP for completion\n    const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const difficultyBonus = {\n        easy: 0,\n        medium: 10,\n        hard: 20\n    }[data.difficulty];\n    const perfectBonus = data.perfectScore ? 30 : 0;\n    return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;\n};\n// Determine completion message and type\nconst getCompletionMessage = (data)=>{\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore && timePercentage > 0.7) {\n        return {\n            message: 'Perfect! Outstanding performance!',\n            type: 'perfect'\n        };\n    } else if (data.perfectScore && timePercentage > 0.4) {\n        return {\n            message: 'Excellent! Great job!',\n            type: 'excellent'\n        };\n    } else if (data.perfectScore) {\n        return {\n            message: 'Perfect Score! Well done!',\n            type: 'excellent'\n        };\n    } else if (timePercentage > 0.5) {\n        return {\n            message: 'Great work! Fast completion!',\n            type: 'good'\n        };\n    } else {\n        return {\n            message: 'Game Complete! Nice job!',\n            type: 'complete'\n        };\n    }\n};\n// Process game completion\nconst processGameCompletion = (data)=>{\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const finalScore = data.score + timeBonus;\n    const xpGained = calculateXP(data);\n    const { message, type } = getCompletionMessage(data);\n    // Determine achievements based on performance\n    const achievements = [];\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore) {\n        achievements.push('Perfect Score');\n    }\n    if (timePercentage > 0.8) {\n        achievements.push('Speed Demon');\n    }\n    if (data.difficulty === 'hard' && data.perfectScore) {\n        achievements.push('Master Player');\n    }\n    if (timeBonus > 30) {\n        achievements.push('Time Master');\n    }\n    return {\n        finalScore,\n        timeBonus,\n        xpGained,\n        achievements,\n        message,\n        celebrationType: type\n    };\n};\n// Game-specific completion checkers\nconst checkGameCompletion = {\n    'flag-matching': (matches, totalPairs)=>{\n        return matches >= totalPairs;\n    },\n    'country-name-scramble': (currentRound, totalRounds, roundComplete)=>{\n        return currentRound >= totalRounds - 1 && roundComplete;\n    },\n    'jigsaw-puzzle': (placedPieces, totalPieces)=>{\n        return placedPieces >= totalPieces;\n    },\n    'quiz': (answeredQuestions, totalQuestions)=>{\n        return answeredQuestions >= totalQuestions;\n    },\n    'memory-grid': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'matching': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'country-explorer': (visitedCountries, targetCountries)=>{\n        return visitedCountries >= targetCountries;\n    },\n    'speed-challenge': (answeredQuestions, targetQuestions)=>{\n        return answeredQuestions >= targetQuestions;\n    },\n    'mystery-land': (correctGuesses, totalRounds)=>{\n        return correctGuesses >= totalRounds;\n    },\n    'timeline-builder': (placedEvents, totalEvents)=>{\n        return placedEvents >= totalEvents;\n    },\n    'where-in-africa': (completedRounds, totalRounds)=>{\n        return completedRounds >= totalRounds;\n    },\n    'dress-character': (completedOutfits, targetOutfits)=>{\n        return completedOutfits >= targetOutfits;\n    }\n};\n// Celebration animation configurations\nconst celebrationConfigs = {\n    perfect: {\n        duration: 3000,\n        emoji: '🏆',\n        colors: [\n            '#FFD700',\n            '#FFA500',\n            '#FF6B6B'\n        ],\n        particles: 50\n    },\n    excellent: {\n        duration: 2500,\n        emoji: '🎉',\n        colors: [\n            '#4ECDC4',\n            '#45B7D1',\n            '#96CEB4'\n        ],\n        particles: 40\n    },\n    good: {\n        duration: 2000,\n        emoji: '👏',\n        colors: [\n            '#FECA57',\n            '#48CAE4',\n            '#A8E6CF'\n        ],\n        particles: 30\n    },\n    complete: {\n        duration: 1500,\n        emoji: '✅',\n        colors: [\n            '#6C5CE7',\n            '#A29BFE',\n            '#74B9FF'\n        ],\n        particles: 20\n    }\n};\n// Auto-progression logic\nconst getNextAction = (gameType, difficulty)=>{\n    // If completed on easy, progress to medium\n    if (difficulty === 'easy') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'medium'\n        };\n    }\n    // If completed on medium, progress to hard\n    if (difficulty === 'medium') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'hard'\n        };\n    }\n    // If completed on hard, return to menu\n    return {\n        action: 'menu'\n    };\n};\n// Process game completion with auto-progression\nconst processGameCompletionWithProgression = (data)=>{\n    const baseResult = processGameCompletion(data);\n    const progression = getNextAction(data.gameType, data.difficulty);\n    // Determine delay based on celebration type\n    const delay = {\n        perfect: 3000,\n        excellent: 2500,\n        good: 2000,\n        complete: 1500\n    }[baseResult.celebrationType];\n    return {\n        ...baseResult,\n        autoProgression: {\n            ...progression,\n            delay\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/gameCompletion.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ })

}]);