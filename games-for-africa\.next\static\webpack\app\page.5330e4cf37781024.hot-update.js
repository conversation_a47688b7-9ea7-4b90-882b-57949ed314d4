"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(app-pages-browser)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst FlagImage = (param)=>{\n    let { countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick } = param;\n    _s();\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image\n            const img = new Image();\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.src = flagImageUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    object-cover \\n    rounded-sm \\n    border \\n    border-gray-300 \\n    shadow-sm\\n    \").concat(onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : '', \"\\n    \").concat(className, \"\\n  \");\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-200 animate-pulse flex items-center justify-center\"),\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          \".concat(sizeClasses[size], \" \\n          flex \\n          items-center \\n          justify-center \\n          bg-gray-100 \\n          rounded-sm \\n          border \\n          border-gray-300\\n          \").concat(onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : '', \"\\n          \").concat(className, \"\\n        \"),\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(baseClasses, \" bg-gray-100 flex items-center justify-center\"),\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagImage, \"mgIVjR2Tvb/QtAQzLx0hN0fqtHE=\");\n_c = FlagImage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\nvar _c;\n$RefreshReg$(_c, \"FlagImage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FlagImage.tsx\n"));

/***/ })

});