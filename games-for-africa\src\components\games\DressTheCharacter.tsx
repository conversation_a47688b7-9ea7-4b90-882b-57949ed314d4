'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { getRandomItems, shuffleArray } from '@/utils';

interface DressTheCharacterProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface ClothingItem {
  id: string;
  name: string;
  country: string;
  countryId: string;
  type: 'head' | 'body' | 'accessory';
  emoji: string;
}

interface Character {
  head: ClothingItem | null;
  body: ClothingItem | null;
  accessory: ClothingItem | null;
}

const DressTheCharacter: React.FC<DressTheCharacterProps> = ({ countries, onComplete }) => {
  const [targetCountry, setTargetCountry] = useState<Country | null>(null);
  const [availableClothing, setAvailableClothing] = useState<ClothingItem[]>([]);
  const [character, setCharacter] = useState<Character>({ head: null, body: null, accessory: null });
  const [score, setScore] = useState(0);
  const [round, setRound] = useState(1);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const maxRounds = 5;

  const generateClothingItems = (targetCountry: Country, otherCountries: Country[]): ClothingItem[] => {
    const items: ClothingItem[] = [];
    
    // Target country items (correct answers)
    targetCountry.culturalElements.traditionalClothing.forEach((clothing, index) => {
      const types: Array<'head' | 'body' | 'accessory'> = ['head', 'body', 'accessory'];
      items.push({
        id: `${targetCountry.id}-${index}`,
        name: clothing,
        country: targetCountry.name,
        countryId: targetCountry.id,
        type: types[index % 3],
        emoji: index % 3 === 0 ? '👑' : index % 3 === 1 ? '👘' : '📿',
      });
    });

    // Other countries' items (wrong answers)
    otherCountries.forEach((country) => {
      country.culturalElements.traditionalClothing.forEach((clothing, index) => {
        const types: Array<'head' | 'body' | 'accessory'> = ['head', 'body', 'accessory'];
        items.push({
          id: `${country.id}-${index}`,
          name: clothing,
          country: country.name,
          countryId: country.id,
          type: types[index % 3],
          emoji: index % 3 === 0 ? '🎩' : index % 3 === 1 ? '👔' : '⌚',
        });
      });
    });

    return shuffleArray(items);
  };

  const startNewRound = () => {
    const selectedCountry = getRandomItems(countries, 1)[0];
    const otherCountries = getRandomItems(
      countries.filter(c => c.id !== selectedCountry.id),
      3
    );
    
    setTargetCountry(selectedCountry);
    setAvailableClothing(generateClothingItems(selectedCountry, otherCountries));
    setCharacter({ head: null, body: null, accessory: null });
    setShowResult(false);
  };

  const startGame = () => {
    setScore(0);
    setRound(1);
    setGameComplete(false);
    setGameStarted(true);
    startNewRound();
  };

  const handleClothingSelect = (item: ClothingItem) => {
    setCharacter(prev => ({
      ...prev,
      [item.type]: item,
    }));
  };

  const removeClothing = (type: 'head' | 'body' | 'accessory') => {
    setCharacter(prev => ({
      ...prev,
      [type]: null,
    }));
  };

  const submitOutfit = () => {
    if (!targetCountry) return;

    let correctItems = 0;
    let totalItems = 0;

    Object.values(character).forEach(item => {
      if (item) {
        totalItems++;
        if (item.countryId === targetCountry.id) {
          correctItems++;
        }
      }
    });

    const roundScore = totalItems > 0 ? Math.round((correctItems / totalItems) * 20) : 0;
    setScore(score + roundScore);
    setShowResult(true);

    setTimeout(() => {
      if (round < maxRounds) {
        setRound(round + 1);
        startNewRound();
      } else {
        setGameComplete(true);
        setTimeout(() => onComplete(score + roundScore), 1000);
      }
    }, 3000);
  };

  const isOutfitComplete = () => {
    return character.head || character.body || character.accessory;
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🎭 Dress the Character</h2>
          <div className="text-6xl mb-6">👗</div>
          <p className="text-xl text-gray-300 mb-6">
            Dress characters in traditional African clothing from different countries!
          </p>
          
          <div className="space-y-4 text-gray-300 mb-8">
            <p>• {maxRounds} rounds of fashion challenges</p>
            <p>• Choose traditional clothing from the target country</p>
            <p>• Mix and match head wear, body clothing, and accessories</p>
            <p>• Score points for authentic cultural outfits</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Styling
          </button>
        </div>
      </div>
    );
  }

  if (!targetCountry) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🎭 Dress the Character</h2>
          <div className="text-yellow-400 text-xl font-bold">
            Round {round}/{maxRounds}
          </div>
        </div>
        
        <div className="text-center mb-4">
          <div className="text-3xl mb-2">{targetCountry.flagUrl}</div>
          <h3 className="text-xl font-semibold text-white">
            Dress in traditional {targetCountry.name} clothing
          </h3>
          <p className="text-gray-300">Score: {score} points</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Character Display */}
        <div className="lg:col-span-1">
          <h3 className="text-xl font-semibold text-white mb-4">👤 Character</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="text-center space-y-4">
              {/* Head */}
              <div className="relative">
                <div className="text-6xl">😊</div>
                {character.head && (
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <button
                      onClick={() => removeClothing('head')}
                      className="bg-blue-500 bg-opacity-20 border border-blue-400 rounded-lg px-3 py-1 text-blue-300 text-sm hover:bg-blue-500 hover:bg-opacity-30"
                    >
                      {character.head.emoji} {character.head.name}
                    </button>
                  </div>
                )}
              </div>

              {/* Body */}
              <div className="relative">
                <div className="text-8xl">🧍</div>
                {character.body && (
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <button
                      onClick={() => removeClothing('body')}
                      className="bg-green-500 bg-opacity-20 border border-green-400 rounded-lg px-3 py-1 text-green-300 text-sm hover:bg-green-500 hover:bg-opacity-30"
                    >
                      {character.body.emoji} {character.body.name}
                    </button>
                  </div>
                )}
              </div>

              {/* Accessory */}
              <div className="relative">
                {character.accessory && (
                  <div className="mb-2">
                    <button
                      onClick={() => removeClothing('accessory')}
                      className="bg-purple-500 bg-opacity-20 border border-purple-400 rounded-lg px-3 py-1 text-purple-300 text-sm hover:bg-purple-500 hover:bg-opacity-30"
                    >
                      {character.accessory.emoji} {character.accessory.name}
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-6">
              <button
                onClick={submitOutfit}
                disabled={!isOutfitComplete() || showResult}
                className={`w-full py-3 px-4 rounded-lg font-bold transition-colors ${
                  isOutfitComplete() && !showResult
                    ? 'bg-yellow-400 text-gray-900 hover:bg-yellow-300'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                {showResult ? '⏳ Next Round...' : '✨ Complete Outfit'}
              </button>
            </div>
          </div>
        </div>

        {/* Clothing Options */}
        <div className="lg:col-span-2">
          <h3 className="text-xl font-semibold text-white mb-4">👗 Clothing Options</h3>
          
          {/* Head Wear */}
          <div className="mb-6">
            <h4 className="text-lg font-medium text-blue-300 mb-3">👑 Head Wear</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableClothing.filter(item => item.type === 'head').map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleClothingSelect(item)}
                  disabled={showResult}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-blue-400 transition-all duration-200 ${
                    character.head?.id === item.id ? 'border-blue-400 bg-blue-500 bg-opacity-20' : ''
                  } ${showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="text-2xl mb-1">{item.emoji}</div>
                  <div className="text-white text-sm font-medium">{item.name}</div>
                  <div className="text-gray-400 text-xs">{item.country}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Body Clothing */}
          <div className="mb-6">
            <h4 className="text-lg font-medium text-green-300 mb-3">👘 Body Clothing</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableClothing.filter(item => item.type === 'body').map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleClothingSelect(item)}
                  disabled={showResult}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-green-400 transition-all duration-200 ${
                    character.body?.id === item.id ? 'border-green-400 bg-green-500 bg-opacity-20' : ''
                  } ${showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="text-2xl mb-1">{item.emoji}</div>
                  <div className="text-white text-sm font-medium">{item.name}</div>
                  <div className="text-gray-400 text-xs">{item.country}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Accessories */}
          <div>
            <h4 className="text-lg font-medium text-purple-300 mb-3">📿 Accessories</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableClothing.filter(item => item.type === 'accessory').map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleClothingSelect(item)}
                  disabled={showResult}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-purple-400 transition-all duration-200 ${
                    character.accessory?.id === item.id ? 'border-purple-400 bg-purple-500 bg-opacity-20' : ''
                  } ${showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="text-2xl mb-1">{item.emoji}</div>
                  <div className="text-white text-sm font-medium">{item.name}</div>
                  <div className="text-gray-400 text-xs">{item.country}</div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Result Display */}
      {showResult && (
        <div className="mt-6 bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-4">Round {round} Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(character).map(([type, item]) => {
                if (!item) return null;
                const isCorrect = item.countryId === targetCountry.id;
                return (
                  <div
                    key={type}
                    className={`p-3 rounded-lg border ${
                      isCorrect 
                        ? 'bg-green-500 bg-opacity-20 border-green-400' 
                        : 'bg-red-500 bg-opacity-20 border-red-400'
                    }`}
                  >
                    <div className="text-2xl mb-1">{item.emoji}</div>
                    <div className="text-white font-medium">{item.name}</div>
                    <div className="text-gray-300 text-sm">{item.country}</div>
                    <div className={`text-sm mt-1 ${isCorrect ? 'text-green-300' : 'text-red-300'}`}>
                      {isCorrect ? '✓ Correct!' : '✗ Wrong country'}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {score >= 80 ? '🏆' : score >= 60 ? '🎉' : '🎭'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Fashion Show Complete!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span> points</p>
                  <p className="text-sm">
                    {score >= 80 ? 'Fashion Expert!' : 
                     score >= 60 ? 'Style Star!' : 
                     'Keep Learning!'}
                  </p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Style Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DressTheCharacter;
