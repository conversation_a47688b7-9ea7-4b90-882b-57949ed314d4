import { UserProfile, GameProgress, Achievement, LEVEL_SYSTEM, ACHIEVEMENTS_CATALOG, XP_REWARDS } from '@/types/progress';

const STORAGE_KEY = 'games_for_africa_profile';

class ProgressService {
  private profile: UserProfile | null = null;

  constructor() {
    this.loadProfile();
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private createDefaultProfile(): UserProfile {
    return {
      id: this.generateId(),
      username: 'Explorer',
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString(),
      totalXP: 0,
      level: 1,
      gamesProgress: {},
      achievements: ACHIEVEMENTS_CATALOG.map(achievement => ({
        ...achievement,
        progress: 0,
      })),
      preferences: {
        theme: 'dark',
        difficulty: 'intermediate',
        soundEnabled: true,
        hintsEnabled: true,
        accessibilityMode: false,
      },
      statistics: {
        totalGamesPlayed: 0,
        totalGamesCompleted: 0,
        averageScore: 0,
        totalPlayTime: 0,
        favoriteGames: [],
        streakDays: 0,
        lastStreakDate: '',
      },
    };
  }

  loadProfile(): UserProfile {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        this.profile = JSON.parse(stored);
        // Ensure all new achievements are present
        this.migrateAchievements();
      } else {
        this.profile = this.createDefaultProfile();
        this.saveProfile();
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      this.profile = this.createDefaultProfile();
    }
    return this.profile!;
  }

  private migrateAchievements(): void {
    if (!this.profile) return;

    const existingIds = new Set(this.profile.achievements.map(a => a.id));
    const newAchievements = ACHIEVEMENTS_CATALOG.filter(a => !existingIds.has(a.id));
    
    newAchievements.forEach(achievement => {
      this.profile!.achievements.push({
        ...achievement,
        progress: 0,
      });
    });
  }

  saveProfile(): void {
    if (!this.profile) return;
    
    this.profile.lastActive = new Date().toISOString();
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.profile));
    } catch (error) {
      console.error('Error saving profile:', error);
    }
  }

  getProfile(): UserProfile {
    return this.profile || this.loadProfile();
  }

  updateUsername(username: string): void {
    if (this.profile) {
      this.profile.username = username;
      this.saveProfile();
    }
  }

  recordGameCompletion(
    gameId: string,
    score: number,
    completionTime: number,
    difficulty: 'beginner' | 'intermediate' | 'advanced',
    isPerfectScore: boolean = false
  ): { xpGained: number; levelUp: boolean; newAchievements: Achievement[] } {
    if (!this.profile) this.loadProfile();

    const existingProgress = this.profile!.gamesProgress[gameId];
    const isFirstCompletion = !existingProgress?.completed;
    const isNewBestScore = !existingProgress || score > existingProgress.bestScore;

    // Update game progress
    this.profile!.gamesProgress[gameId] = {
      gameId,
      completed: true,
      bestScore: Math.max(score, existingProgress?.bestScore || 0),
      totalAttempts: (existingProgress?.totalAttempts || 0) + 1,
      completionTime: isNewBestScore ? completionTime : existingProgress?.completionTime,
      difficulty,
      lastPlayed: new Date().toISOString(),
      perfectScore: isPerfectScore || existingProgress?.perfectScore || false,
    };

    // Update statistics
    this.profile!.statistics.totalGamesPlayed++;
    if (isFirstCompletion) {
      this.profile!.statistics.totalGamesCompleted++;
    }

    // Calculate XP gained
    let xpGained = 0;
    if (isFirstCompletion) {
      xpGained += XP_REWARDS.GAME_COMPLETION;
    }
    if (isPerfectScore) {
      xpGained += XP_REWARDS.PERFECT_SCORE;
    }
    if (completionTime < 120) { // Under 2 minutes
      xpGained += XP_REWARDS.SPEED_BONUS;
    }
    xpGained += XP_REWARDS.DIFFICULTY_BONUS[difficulty];

    const oldLevel = this.profile!.level;
    this.profile!.totalXP += xpGained;

    // Check for level up
    const newLevel = this.calculateLevel(this.profile!.totalXP);
    const levelUp = newLevel > oldLevel;
    this.profile!.level = newLevel;

    // Check for new achievements
    const newAchievements = this.checkAchievements(gameId, score, completionTime, difficulty, isPerfectScore);

    // Update average score
    const allScores = Object.values(this.profile!.gamesProgress).map(p => p.bestScore);
    this.profile!.statistics.averageScore = allScores.reduce((a, b) => a + b, 0) / allScores.length;

    // Update streak
    this.updateStreak();

    this.saveProfile();

    return { xpGained, levelUp, newAchievements };
  }

  private calculateLevel(totalXP: number): number {
    for (let i = LEVEL_SYSTEM.length - 1; i >= 0; i--) {
      if (totalXP >= LEVEL_SYSTEM[i].xpRequired) {
        return LEVEL_SYSTEM[i].level;
      }
    }
    return 1;
  }

  private checkAchievements(
    gameId: string,
    score: number,
    completionTime: number,
    difficulty: 'beginner' | 'intermediate' | 'advanced',
    isPerfectScore: boolean
  ): Achievement[] {
    if (!this.profile) return [];

    const newAchievements: Achievement[] = [];
    const now = new Date().toISOString();

    this.profile.achievements.forEach(achievement => {
      if (achievement.unlockedAt) return; // Already unlocked

      let shouldUnlock = false;
      let progressIncrement = 0;

      switch (achievement.id) {
        case 'first_game':
          shouldUnlock = true;
          break;
        case 'perfect_score':
          shouldUnlock = isPerfectScore;
          break;
        case 'speed_demon':
          shouldUnlock = completionTime < 120;
          break;
        case 'all_games_completed':
          progressIncrement = 1;
          shouldUnlock = this.profile.statistics.totalGamesCompleted >= 10;
          break;
        case 'advanced_difficulty':
          shouldUnlock = difficulty === 'advanced';
          break;
        case 'cultural_scholar':
          progressIncrement = 1; // Simplified - would need more complex tracking
          shouldUnlock = (achievement.progress || 0) + progressIncrement >= 25;
          break;
      }

      if (progressIncrement > 0) {
        achievement.progress = (achievement.progress || 0) + progressIncrement;
      }

      if (shouldUnlock && !achievement.unlockedAt) {
        achievement.unlockedAt = now;
        newAchievements.push(achievement);
        this.profile!.totalXP += XP_REWARDS.ACHIEVEMENT_UNLOCK;
      }
    });

    return newAchievements;
  }

  private updateStreak(): void {
    if (!this.profile) return;

    const today = new Date().toDateString();
    const lastStreakDate = this.profile.statistics.lastStreakDate;
    
    if (lastStreakDate !== today) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (lastStreakDate === yesterday.toDateString()) {
        // Continue streak
        this.profile.statistics.streakDays++;
      } else if (lastStreakDate !== today) {
        // Start new streak
        this.profile.statistics.streakDays = 1;
      }
      
      this.profile.statistics.lastStreakDate = today;
      this.profile.totalXP += XP_REWARDS.DAILY_STREAK;
    }
  }

  getGameProgress(gameId: string): GameProgress | null {
    return this.profile?.gamesProgress[gameId] || null;
  }

  updatePreferences(preferences: Partial<UserProfile['preferences']>): void {
    if (this.profile) {
      this.profile.preferences = { ...this.profile.preferences, ...preferences };
      this.saveProfile();
    }
  }

  addFavoriteGame(gameId: string): void {
    if (this.profile && !this.profile.statistics.favoriteGames.includes(gameId)) {
      this.profile.statistics.favoriteGames.push(gameId);
      this.saveProfile();
    }
  }

  removeFavoriteGame(gameId: string): void {
    if (this.profile) {
      this.profile.statistics.favoriteGames = this.profile.statistics.favoriteGames.filter(id => id !== gameId);
      this.saveProfile();
    }
  }

  resetProgress(): void {
    this.profile = this.createDefaultProfile();
    this.saveProfile();
  }

  exportProgress(): string {
    return JSON.stringify(this.profile, null, 2);
  }

  importProgress(data: string): boolean {
    try {
      const imported = JSON.parse(data);
      // Validate the structure
      if (imported.id && imported.username && imported.gamesProgress) {
        this.profile = imported;
        this.migrateAchievements();
        this.saveProfile();
        return true;
      }
    } catch (error) {
      console.error('Error importing progress:', error);
    }
    return false;
  }
}

export const progressService = new ProgressService();
