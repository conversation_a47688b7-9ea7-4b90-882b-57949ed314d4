"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/WhereInAfrica.tsx":
/*!************************************************!*\
  !*** ./src/components/games/WhereInAfrica.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst WhereInAfrica = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnswer, setSelectedAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [correctAnswers, setCorrectAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalRounds = 10;\n    const generateClue = (country)=>{\n        const clueTypes = [\n            {\n                type: 'landmark',\n                items: country.landmarks\n            },\n            {\n                type: 'food',\n                items: country.culturalElements.cuisine\n            },\n            {\n                type: 'culture',\n                items: country.culturalElements.music\n            },\n            {\n                type: 'wildlife',\n                items: country.wildlife\n            },\n            {\n                type: 'export',\n                items: country.exports\n            }\n        ];\n        const availableClues = clueTypes.filter((ct)=>ct.items.length > 0);\n        const selectedClueType = availableClues[Math.floor(Math.random() * availableClues.length)];\n        const selectedItem = selectedClueType.items[Math.floor(Math.random() * selectedClueType.items.length)];\n        return {\n            type: selectedClueType.type,\n            clue: selectedItem\n        };\n    };\n    const generateRounds = ()=>{\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, totalRounds);\n        const gameRounds = [];\n        selectedCountries.forEach((country)=>{\n            const { type, clue } = generateClue(country);\n            // Generate wrong options\n            const wrongCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries.filter((c)=>c.id !== country.id), 3);\n            const options = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)([\n                country.name,\n                ...wrongCountries.map((c)=>c.name)\n            ]);\n            gameRounds.push({\n                country,\n                clueType: type,\n                clue,\n                options,\n                correctAnswer: country.name\n            });\n        });\n        return gameRounds;\n    };\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setCorrectAnswers(0);\n        setSelectedAnswer(null);\n        setShowResult(false);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleAnswerSelect = (answer)=>{\n        if (selectedAnswer) return;\n        setSelectedAnswer(answer);\n        setShowResult(true);\n        const isCorrect = answer === rounds[currentRound].correctAnswer;\n        if (isCorrect) {\n            setCorrectAnswers(correctAnswers + 1);\n            setScore(score + 10);\n        }\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setSelectedAnswer(null);\n                setShowResult(false);\n            } else {\n                setGameComplete(true);\n                const finalScore = Math.round((correctAnswers + (isCorrect ? 1 : 0)) / totalRounds * 100);\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        }, 2000);\n    };\n    const getClueIcon = (type)=>{\n        switch(type){\n            case 'landmark':\n                return '🏛️';\n            case 'food':\n                return '🍽️';\n            case 'culture':\n                return '🎵';\n            case 'wildlife':\n                return '🦁';\n            case 'export':\n                return '📦';\n            default:\n                return '❓';\n        }\n    };\n    const getClueDescription = (type)=>{\n        switch(type){\n            case 'landmark':\n                return 'Famous landmark';\n            case 'food':\n                return 'Traditional dish';\n            case 'culture':\n                return 'Musical style';\n            case 'wildlife':\n                return 'Native animal';\n            case 'export':\n                return 'Major export';\n            default:\n                return 'Clue';\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDDFA️ Where in Africa?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Guess the African country from clues about its landmarks, food, culture, and more!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    totalRounds,\n                                    \" rounds of cultural clues\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Clues include landmarks, food, music, wildlife, and exports\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Multiple choice answers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Score points for correct guesses\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Adventure\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined);\n    }\n    const round = rounds[currentRound];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDDFA️ Where in Africa?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    \"Round \",\n                                    currentRound + 1,\n                                    \"/\",\n                                    totalRounds\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: correctAnswers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Correct\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Current\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: [\n                                            Math.round(correctAnswers / Math.max(currentRound, 1) * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Accuracy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: getClueIcon(round.clueType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Which African country is known for this?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-yellow-400 text-sm font-medium mb-1\",\n                                        children: getClueDescription(round.clueType)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white text-xl font-bold\",\n                                        children: [\n                                            '\"',\n                                            round.clue,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: round.options.map((option, index)=>{\n                            const isSelected = selectedAnswer === option;\n                            const isCorrect = option === round.correctAnswer;\n                            const isIncorrect = isSelected && !isCorrect && showResult;\n                            let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';\n                            if (showResult) {\n                                if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';\n                                else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';\n                            } else if (isSelected) {\n                                optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: optionClass,\n                                onClick: ()=>handleAnswerSelect(option),\n                                disabled: showResult,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-left text-white font-medium\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        showResult && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 text-xl\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        showResult && isIncorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400 text-xl\",\n                                            children: \"✗\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-gray-700 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl\",\n                                    children: round.country.flagUrl\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: round.country.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-2\",\n                                            children: [\n                                                \"Capital: \",\n                                                round.country.capital,\n                                                \" | Region: \",\n                                                round.country.region\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                round.clueType === 'landmark' && \"\".concat(round.clue, \" is one of the famous landmarks in \").concat(round.country.name, \".\"),\n                                                round.clueType === 'food' && \"\".concat(round.clue, \" is a traditional dish from \").concat(round.country.name, \".\"),\n                                                round.clueType === 'culture' && \"\".concat(round.clue, \" is a popular music style in \").concat(round.country.name, \".\"),\n                                                round.clueType === 'wildlife' && \"\".concat(round.clue, \" can be found in the wild areas of \").concat(round.country.name, \".\"),\n                                                round.clueType === 'export' && \"\".concat(round.clue, \" is one of the major exports of \").concat(round.country.name, \".\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: correctAnswers >= 8 ? '🏆' : correctAnswers >= 6 ? '🎉' : '🗺️'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Adventure Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Correct Answers: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: correctAnswers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 39\n                                                    }, undefined),\n                                                    \"/\",\n                                                    totalRounds\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: [\n                                                            Math.round(correctAnswers / totalRounds * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: correctAnswers >= 8 ? 'Africa Expert!' : correctAnswers >= 6 ? 'Great Explorer!' : 'Keep Exploring!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Explore Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WhereInAfrica, \"lvhAXDRQ5hRGSfYZZm7qSjJkFns=\");\n_c = WhereInAfrica;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhereInAfrica);\nvar _c;\n$RefreshReg$(_c, \"WhereInAfrica\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/WhereInAfrica.tsx\n"));

/***/ })

});