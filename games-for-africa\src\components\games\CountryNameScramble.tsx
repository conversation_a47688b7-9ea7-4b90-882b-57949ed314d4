'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray } from '@/utils';

interface CountryNameScrambleProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface ScrambledLetter {
  id: string;
  letter: string;
  originalIndex: number;
  isUsed: boolean;
}

interface GameRound {
  country: Country;
  scrambledLetters: ScrambledLetter[];
  userAnswer: string;
  isComplete: boolean;
  timeSpent: number;
}

type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

const CountryNameScramble: React.FC<CountryNameScrambleProps> = ({ countries, onComplete }) => {
  const [currentRound, setCurrentRound] = useState(0);
  const [rounds, setRounds] = useState<GameRound[]>([]);
  const [selectedLetters, setSelectedLetters] = useState<ScrambledLetter[]>([]);
  const [userAnswer, setUserAnswer] = useState('');
  const [timeLeft, setTimeLeft] = useState(60);
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('beginner');
  const [gameStarted, setGameStarted] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [roundStartTime, setRoundStartTime] = useState(Date.now());

  const totalRounds = 10;

  const getDifficultyMultiplier = (diff: DifficultyLevel): number => {
    switch (diff) {
      case 'beginner': return 1;
      case 'intermediate': return 1.5;
      case 'advanced': return 2;
    }
  };

  const filterCountriesByDifficulty = (countries: Country[], difficulty: DifficultyLevel): Country[] => {
    return countries.filter(country => {
      const nameLength = country.name.replace(/\s+/g, '').length;
      switch (difficulty) {
        case 'beginner': return nameLength >= 4 && nameLength <= 6;
        case 'intermediate': return nameLength >= 7 && nameLength <= 10;
        case 'advanced': return nameLength >= 11;
        default: return true;
      }
    });
  };

  const scrambleCountryName = (name: string): ScrambledLetter[] => {
    const cleanName = name.replace(/\s+/g, '').toUpperCase();
    const letters = cleanName.split('').map((letter, index) => ({
      id: `letter-${index}-${Math.random()}`,
      letter,
      originalIndex: index,
      isUsed: false,
    }));
    return shuffleArray(letters);
  };

  const generateRounds = (): GameRound[] => {
    const filteredCountries = filterCountriesByDifficulty(countries, difficulty);
    const selectedCountries = shuffleArray(filteredCountries).slice(0, totalRounds);
    
    return selectedCountries.map(country => ({
      country,
      scrambledLetters: scrambleCountryName(country.name),
      userAnswer: '',
      isComplete: false,
      timeSpent: 0,
    }));
  };

  useEffect(() => {
    if (gameStarted && timeLeft > 0 && !gameComplete) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      handleGameEnd();
    }
  }, [gameStarted, timeLeft, gameComplete]);

  const startGame = () => {
    const newRounds = generateRounds();
    setRounds(newRounds);
    setCurrentRound(0);
    setScore(0);
    setStreak(0);
    setTimeLeft(60);
    setUserAnswer('');
    setSelectedLetters([]);
    setGameStarted(true);
    setGameComplete(false);
    setShowResult(false);
    setRoundStartTime(Date.now());
  };

  const handleLetterClick = (letter: ScrambledLetter) => {
    if (letter.isUsed || showResult) return;

    const newSelectedLetters = [...selectedLetters, { ...letter, isUsed: true }];
    setSelectedLetters(newSelectedLetters);
    setUserAnswer(newSelectedLetters.map(l => l.letter).join(''));

    // Update the scrambled letters to mark this one as used
    const currentRoundData = rounds[currentRound];
    const updatedScrambledLetters = currentRoundData.scrambledLetters.map(l =>
      l.id === letter.id ? { ...l, isUsed: true } : l
    );
    
    const updatedRounds = [...rounds];
    updatedRounds[currentRound] = {
      ...currentRoundData,
      scrambledLetters: updatedScrambledLetters,
    };
    setRounds(updatedRounds);
  };

  const handleClearAnswer = () => {
    if (showResult) return;

    setSelectedLetters([]);
    setUserAnswer('');
    
    // Reset all letters to unused
    const currentRoundData = rounds[currentRound];
    const resetScrambledLetters = currentRoundData.scrambledLetters.map(l => ({ ...l, isUsed: false }));
    
    const updatedRounds = [...rounds];
    updatedRounds[currentRound] = {
      ...currentRoundData,
      scrambledLetters: resetScrambledLetters,
    };
    setRounds(updatedRounds);
  };

  const handleSubmitAnswer = () => {
    if (!userAnswer || showResult) return;

    const currentCountry = rounds[currentRound].country;
    const correctAnswer = currentCountry.name.replace(/\s+/g, '').toUpperCase();
    const userAnswerClean = userAnswer.replace(/\s+/g, '').toUpperCase();
    const correct = userAnswerClean === correctAnswer;
    
    setIsCorrect(correct);
    setShowResult(true);

    const timeSpent = (Date.now() - roundStartTime) / 1000;
    
    if (correct) {
      const basePoints = correctAnswer.length;
      const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));
      const streakMultiplier = 1 + (streak * 0.1);
      const difficultyMultiplier = getDifficultyMultiplier(difficulty);
      
      const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);
      setScore(score + roundScore);
      setStreak(streak + 1);
    } else {
      setStreak(0);
    }

    // Update round data
    const updatedRounds = [...rounds];
    updatedRounds[currentRound] = {
      ...updatedRounds[currentRound],
      userAnswer,
      isComplete: true,
      timeSpent,
    };
    setRounds(updatedRounds);

    setTimeout(() => {
      if (currentRound < totalRounds - 1) {
        setCurrentRound(currentRound + 1);
        setUserAnswer('');
        setSelectedLetters([]);
        setShowResult(false);
        setShowHint(false);
        setRoundStartTime(Date.now());
        
        // Reset letters for next round
        const nextRoundData = updatedRounds[currentRound + 1];
        const resetLetters = nextRoundData.scrambledLetters.map(l => ({ ...l, isUsed: false }));
        updatedRounds[currentRound + 1] = {
          ...nextRoundData,
          scrambledLetters: resetLetters,
        };
        setRounds(updatedRounds);
      } else {
        handleGameEnd();
      }
    }, 3000);
  };

  const handleGameEnd = () => {
    setGameComplete(true);
    setTimeout(() => onComplete(score), 1000);
  };

  const toggleHint = () => {
    setShowHint(!showHint);
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🔤 Country Name Scramble</h2>
          <div className="text-6xl mb-6">🧩</div>
          <p className="text-xl text-gray-300 mb-6">
            Unscramble the letters to form African country names!
          </p>
          
          {/* Difficulty Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Choose Difficulty:</h3>
            <div className="flex justify-center gap-4">
              {(['beginner', 'intermediate', 'advanced'] as DifficultyLevel[]).map((diff) => (
                <button
                  key={diff}
                  onClick={() => setDifficulty(diff)}
                  className={`px-6 py-3 rounded-lg transition-colors ${
                    difficulty === diff
                      ? 'bg-yellow-400 text-gray-900'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  <div className="text-center">
                    <div className="font-semibold capitalize">{diff}</div>
                    <div className="text-sm">
                      {diff === 'beginner' && '4-6 letters'}
                      {diff === 'intermediate' && '7-10 letters'}
                      {diff === 'advanced' && '11+ letters'}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Unscramble letters to form country names</p>
            <p>• Longer names = more points</p>
            <p>• Build streaks for bonus multipliers</p>
            <p>• Use hints to see flags and regions</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Scrambling
          </button>
        </div>
      </div>
    );
  }

  if (!rounds[currentRound]) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  const currentCountry = rounds[currentRound].country;

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🔤 Country Name Scramble</h2>
          <div className="text-yellow-400 text-xl font-bold">
            {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{streak}</div>
            <div className="text-gray-400 text-sm">Streak</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{currentRound + 1}</div>
            <div className="text-gray-400 text-sm">Round</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{totalRounds}</div>
            <div className="text-gray-400 text-sm">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-400 capitalize">{difficulty}</div>
            <div className="text-gray-400 text-sm">Difficulty</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentRound + 1) / totalRounds) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Game Area */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-white mb-4">
            Unscramble this African country name:
          </h3>
          
          {/* Hint Section */}
          <div className="mb-4">
            <button
              onClick={toggleHint}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              {showHint ? 'Hide Hint' : 'Show Hint'} 💡
            </button>
            
            {showHint && (
              <div className="mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4">
                <div className="flex items-center justify-center space-x-4">
                  <div className="text-4xl">{currentCountry.flagUrl}</div>
                  <div className="text-left">
                    <div className="text-blue-400 font-semibold">Region: {currentCountry.region}</div>
                    <div className="text-gray-300 text-sm">Capital: {currentCountry.capital}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Scrambled Letters */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-white mb-4 text-center">Available Letters:</h4>
          <div className="flex flex-wrap justify-center gap-2">
            {rounds[currentRound].scrambledLetters.map((letter) => (
              <button
                key={letter.id}
                onClick={() => handleLetterClick(letter)}
                disabled={letter.isUsed || showResult}
                className={`w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 ${
                  letter.isUsed 
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                    : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'
                }`}
              >
                {letter.letter}
              </button>
            ))}
          </div>
        </div>

        {/* User Answer */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-white mb-4 text-center">Your Answer:</h4>
          <div className="bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center">
            <div className="text-2xl font-bold text-white tracking-wider">
              {userAnswer || 'Click letters to build your answer...'}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={handleClearAnswer}
            disabled={showResult}
            className="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
          <button
            onClick={handleSubmitAnswer}
            disabled={!userAnswer || showResult}
            className="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Submit Answer
          </button>
        </div>

        {/* Result Display */}
        {showResult && (
          <div className={`mt-6 p-4 rounded-lg border ${
            isCorrect 
              ? 'bg-green-500 bg-opacity-10 border-green-400' 
              : 'bg-red-500 bg-opacity-10 border-red-400'
          }`}>
            <div className="text-center">
              <div className={`text-2xl font-bold mb-2 ${isCorrect ? 'text-green-400' : 'text-red-400'}`}>
                {isCorrect ? '✓ Correct!' : '✗ Incorrect'}
              </div>
              <div className="text-white text-lg mb-2">
                The answer was: <strong>{currentCountry.name}</strong>
              </div>
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="text-3xl">{currentCountry.flagUrl}</div>
                <div className="text-left">
                  <div className="text-white font-semibold">Capital: {currentCountry.capital}</div>
                  <div className="text-gray-300">Region: {currentCountry.region}</div>
                  <div className="text-gray-300">Population: {currentCountry.population.toLocaleString()}</div>
                </div>
              </div>
              {isCorrect && (
                <div className="text-yellow-400 font-semibold">
                  +{Math.floor((currentCountry.name.replace(/\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + (streak * 0.1)) * getDifficultyMultiplier(difficulty))} points!
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Game Complete!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                  <p>Best Streak: <span className="text-green-400">{Math.max(...rounds.map((_, i) => i <= currentRound ? streak : 0))}</span></p>
                  <p>Difficulty: <span className="text-purple-400 capitalize">{difficulty}</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CountryNameScramble;
