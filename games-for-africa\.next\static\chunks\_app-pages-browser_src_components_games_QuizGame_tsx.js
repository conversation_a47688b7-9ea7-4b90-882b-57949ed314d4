"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_QuizGame_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/QuizGame.tsx":
/*!*******************************************!*\
  !*** ./src/components/games/QuizGame.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_calculateScore_formatTime_generateQuizQuestions_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=calculateScore,formatTime,generateQuizQuestions!=!@/utils */ \"(app-pages-browser)/__barrel_optimize__?names=calculateScore,formatTime,generateQuizQuestions!=!./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QuizGame = (param)=>{\n    let { countries, category, difficulty, onComplete } = param;\n    var _currentQuestion_options;\n    _s();\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestion: 0,\n        questions: [],\n        selectedAnswers: [],\n        score: 0,\n        timeRemaining: 300,\n        isComplete: false,\n        showExplanation: false\n    });\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize game\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizGame.useEffect\": ()=>{\n            const questions = (0,_barrel_optimize_names_calculateScore_formatTime_generateQuizQuestions_utils__WEBPACK_IMPORTED_MODULE_2__.generateQuizQuestions)(countries, category, difficulty, 10);\n            const initialState = {\n                ...gameState,\n                questions,\n                selectedAnswers: new Array(questions.length).fill(null)\n            };\n            setGameState(initialState);\n        }\n    }[\"QuizGame.useEffect\"], [\n        countries,\n        category,\n        difficulty\n    ]);\n    // Timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizGame.useEffect\": ()=>{\n            if (gameState.timeRemaining > 0 && !gameState.isComplete) {\n                const timer = setTimeout({\n                    \"QuizGame.useEffect.timer\": ()=>{\n                        setGameState({\n                            \"QuizGame.useEffect.timer\": (prev)=>({\n                                    ...prev,\n                                    timeRemaining: prev.timeRemaining - 1\n                                })\n                        }[\"QuizGame.useEffect.timer\"]);\n                    }\n                }[\"QuizGame.useEffect.timer\"], 1000);\n                return ({\n                    \"QuizGame.useEffect\": ()=>clearTimeout(timer)\n                })[\"QuizGame.useEffect\"];\n            } else if (gameState.timeRemaining === 0) {\n                handleGameComplete();\n            }\n        }\n    }[\"QuizGame.useEffect\"], [\n        gameState.timeRemaining,\n        gameState.isComplete\n    ]);\n    const handleAnswerSelect = (answer)=>{\n        const newAnswers = [\n            ...gameState.selectedAnswers\n        ];\n        newAnswers[gameState.currentQuestion] = answer;\n        setGameState((prev)=>({\n                ...prev,\n                selectedAnswers: newAnswers,\n                showExplanation: true\n            }));\n    };\n    const handleNextQuestion = ()=>{\n        if (gameState.currentQuestion < gameState.questions.length - 1) {\n            setGameState((prev)=>({\n                    ...prev,\n                    currentQuestion: prev.currentQuestion + 1,\n                    showExplanation: false\n                }));\n        } else {\n            handleGameComplete();\n        }\n    };\n    const handleGameComplete = ()=>{\n        const correctAnswers = gameState.selectedAnswers.filter((answer, index)=>{\n            var _gameState_questions_index;\n            return answer === ((_gameState_questions_index = gameState.questions[index]) === null || _gameState_questions_index === void 0 ? void 0 : _gameState_questions_index.correctAnswer);\n        }).length;\n        const finalScore = (0,_barrel_optimize_names_calculateScore_formatTime_generateQuizQuestions_utils__WEBPACK_IMPORTED_MODULE_2__.calculateScore)(correctAnswers, gameState.questions.length, 0, difficulty === 'easy' ? 1 : difficulty === 'medium' ? 1.5 : 2);\n        setGameState((prev)=>({\n                ...prev,\n                score: finalScore,\n                isComplete: true\n            }));\n        setShowResults(true);\n        onComplete(finalScore);\n    };\n    const handleRestart = ()=>{\n        const questions = (0,_barrel_optimize_names_calculateScore_formatTime_generateQuizQuestions_utils__WEBPACK_IMPORTED_MODULE_2__.generateQuizQuestions)(countries, category, difficulty, 10);\n        setGameState({\n            currentQuestion: 0,\n            questions,\n            selectedAnswers: new Array(questions.length).fill(null),\n            score: 0,\n            timeRemaining: 300,\n            isComplete: false,\n            showExplanation: false\n        });\n        setShowResults(false);\n    };\n    const currentQuestion = gameState.questions[gameState.currentQuestion];\n    const selectedAnswer = gameState.selectedAnswers[gameState.currentQuestion];\n    const progress = (gameState.currentQuestion + 1) / gameState.questions.length * 100;\n    if (!currentQuestion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-gold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: [\n                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                    \" Quiz\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono\",\n                                                children: (0,_barrel_optimize_names_calculateScore_formatTime_generateQuizQuestions_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(gameState.timeRemaining)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            gameState.currentQuestion + 1,\n                                            \" / \",\n                                            gameState.questions.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-400 h-2 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-white mb-6\",\n                            children: currentQuestion.question\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-3\",\n                            children: (_currentQuestion_options = currentQuestion.options) === null || _currentQuestion_options === void 0 ? void 0 : _currentQuestion_options.map((option, index)=>{\n                                const isSelected = selectedAnswer === option;\n                                const isCorrect = option === currentQuestion.correctAnswer;\n                                const isIncorrect = isSelected && !isCorrect && gameState.showExplanation;\n                                let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';\n                                if (gameState.showExplanation) {\n                                    if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';\n                                    else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';\n                                } else if (isSelected) {\n                                    optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: optionClass,\n                                    onClick: ()=>!gameState.showExplanation && handleAnswerSelect(option),\n                                    disabled: gameState.showExplanation,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-left text-white\",\n                                                children: option\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            gameState.showExplanation && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            gameState.showExplanation && isIncorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: \"✗\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined),\n                        gameState.showExplanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-900 rounded-lg border border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300\",\n                                    children: currentQuestion.explanation\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextQuestion,\n                                        className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg font-medium hover:bg-yellow-300 transition-colors\",\n                                        children: gameState.currentQuestion < gameState.questions.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"Next Question →\"\n                                        }, void 0, false) : 'Complete Quiz'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, gameState.currentQuestion, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            showResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: gameState.score >= 80 ? '🎉' : gameState.score >= 60 ? '👏' : '💪'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: [\n                                            gameState.score,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"You got \",\n                                            gameState.selectedAnswers.filter((answer, index)=>{\n                                                var _gameState_questions_index;\n                                                return answer === ((_gameState_questions_index = gameState.questions[index]) === null || _gameState_questions_index === void 0 ? void 0 : _gameState_questions_index.correctAnswer);\n                                            }).length,\n                                            \" out of \",\n                                            gameState.questions.length,\n                                            \" questions correct!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowResults(false),\n                                        className: \"bg-gray-700 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRestart,\n                                        className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                        children: \"\\uD83D\\uDD04 Play Again\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\QuizGame.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuizGame, \"G8126pXD5idJ6CAUz7VKztjyoIs=\");\n_c = QuizGame;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizGame);\nvar _c;\n$RefreshReg$(_c, \"QuizGame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/QuizGame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=calculateScore,formatTime,generateQuizQuestions!=!./src/utils/index.ts":
/*!********************************************************************************************************!*\
  !*** __barrel_optimize__?names=calculateScore,formatTime,generateQuizQuestions!=!./src/utils/index.ts ***!
  \********************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   formatScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatScore),\n/* harmony export */   formatTime: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.shuffleArray),\n/* harmony export */   validateAnswer: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.validateAnswer)\n/* harmony export */ });\n/* harmony import */ var C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/index.ts */ \"(app-pages-browser)/./src/utils/index.ts\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPWNhbGN1bGF0ZVNjb3JlLGZvcm1hdFRpbWUsZ2VuZXJhdGVRdWl6UXVlc3Rpb25zIT0hLi9zcmMvdXRpbHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFx1dGlsc1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkM6XFxcXFVzZXJzXFxcXE1FRUsgRURFTlxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxHQU1FUyBGT1IgQVJJQ0FcXFxcZ2FtZXMtZm9yLWFmcmljYVxcXFxzcmNcXFxcdXRpbHNcXFxcaW5kZXgudHNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=calculateScore,formatTime,generateQuizQuestions!=!./src/utils/index.ts\n"));

/***/ })

}]);