"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_7__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = (score)=>{\n        console.log('Game completed with score:', score);\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-accent-gold font-medium\",\n                                children: \"Score: 0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border border-gray-700 rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer\",\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: game.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⏱️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    game.estimatedTime,\n                                                                    \"min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-4 flex-grow\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                        children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            game.maxScore,\n                                                            \" pts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                children: \"▶️ Play Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, game.type, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"g4JRP421rujMtt/9zwXe7LlkLmM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});