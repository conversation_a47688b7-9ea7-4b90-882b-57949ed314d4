import { Country, QuizQuestion, GameType } from '@/types';

// Seeded random number generator for consistent results
let seed = 1;
const seededRandom = () => {
  const x = Math.sin(seed++) * 10000;
  return x - Math.floor(x);
};

// Reset seed function for consistent shuffling
export const resetSeed = (newSeed: number = 1) => {
  seed = newSeed;
};

// Array shuffling utility with seeded random
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(seededRandom() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Random selection utility
export const getRandomItems = <T>(array: T[], count: number): T[] => {
  const shuffled = shuffleArray(array);
  return shuffled.slice(0, Math.min(count, array.length));
};

// Score calculation utilities
export const calculateScore = (
  correct: number,
  total: number,
  timeBonus: number = 0,
  difficultyMultiplier: number = 1,
  streakBonus: number = 0
): number => {
  const baseScore = (correct / total) * 100;
  const bonusScore = timeBonus + streakBonus;
  return Math.round((baseScore + bonusScore) * difficultyMultiplier);
};

export const calculateTimeBonus = (timeSpent: number, maxTime: number): number => {
  if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time
  if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time
  return 0;
};

export const calculateStreakBonus = (streak: number): number => {
  return Math.min(streak * 2, 20); // Max 20 bonus points
};

// Quiz question generation
export const generateQuizQuestions = (
  countries: Country[],
  category: string,
  difficulty: 'easy' | 'medium' | 'hard',
  count: number = 10
): QuizQuestion[] => {
  const questions: QuizQuestion[] = [];
  const selectedCountries = getRandomItems(countries, count);

  selectedCountries.forEach((country, index) => {
    switch (category) {
      case 'geography':
        questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));
        break;
      case 'history':
        questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));
        break;
      case 'culture':
        questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));
        break;
      case 'wildlife':
        questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));
        break;
      case 'notable-figures':
        questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));
        break;
    }
  });

  return shuffleArray(questions);
};

const generateGeographyQuestion = (
  country: Country,
  allCountries: Country[],
  difficulty: string,
  id: string
): QuizQuestion => {
  const questionTypes = ['capital', 'currency', 'region'];
  const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];

  switch (type) {
    case 'capital':
      return {
        id,
        type: 'multiple-choice',
        category: 'geography',
        question: `What is the capital of ${country.name}?`,
        options: generateCapitalOptions(country, allCountries),
        correctAnswer: country.capital,
        explanation: `${country.capital} is the capital city of ${country.name}.`,
        difficulty: difficulty as any,
        countryId: country.id,
      };
    case 'currency':
      return {
        id,
        type: 'multiple-choice',
        category: 'geography',
        question: `What is the currency of ${country.name}?`,
        options: generateCurrencyOptions(country, allCountries),
        correctAnswer: country.currency,
        explanation: `The currency of ${country.name} is ${country.currency}.`,
        difficulty: difficulty as any,
        countryId: country.id,
      };
    default:
      return {
        id,
        type: 'multiple-choice',
        category: 'geography',
        question: `Which region is ${country.name} located in?`,
        options: generateRegionOptions(country, allCountries),
        correctAnswer: country.region,
        explanation: `${country.name} is located in ${country.region}.`,
        difficulty: difficulty as any,
        countryId: country.id,
      };
  }
};

const generateHistoryQuestion = (
  country: Country,
  allCountries: Country[],
  difficulty: string,
  id: string
): QuizQuestion => {
  return {
    id,
    type: 'multiple-choice',
    category: 'history',
    question: `When did ${country.name} gain independence?`,
    options: generateIndependenceOptions(country, allCountries),
    correctAnswer: new Date(country.independence).getFullYear().toString(),
    explanation: `${country.name} gained independence in ${new Date(country.independence).getFullYear()}.`,
    difficulty: difficulty as any,
    countryId: country.id,
  };
};

const generateCultureQuestion = (
  country: Country,
  allCountries: Country[],
  difficulty: string,
  id: string
): QuizQuestion => {
  const culturalAspects = ['cuisine', 'music', 'dances'];
  const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];
  const item = country.culturalElements[aspect as keyof typeof country.culturalElements][0];

  return {
    id,
    type: 'multiple-choice',
    category: 'culture',
    question: `Which country is known for ${item}?`,
    options: generateCountryOptions(country, allCountries),
    correctAnswer: country.name,
    explanation: `${item} is a traditional ${aspect.slice(0, -1)} from ${country.name}.`,
    difficulty: difficulty as any,
    countryId: country.id,
  };
};

const generateWildlifeQuestion = (
  country: Country,
  allCountries: Country[],
  difficulty: string,
  id: string
): QuizQuestion => {
  const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];

  return {
    id,
    type: 'multiple-choice',
    category: 'wildlife',
    question: `Which country is home to ${animal}?`,
    options: generateCountryOptions(country, allCountries),
    correctAnswer: country.name,
    explanation: `${animal} can be found in ${country.name}.`,
    difficulty: difficulty as any,
    countryId: country.id,
  };
};

const generateNotableFiguresQuestion = (
  country: Country,
  allCountries: Country[],
  difficulty: string,
  id: string
): QuizQuestion => {
  const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];

  return {
    id,
    type: 'multiple-choice',
    category: 'notable-figures',
    question: `${figure.name} is from which country?`,
    options: generateCountryOptions(country, allCountries),
    correctAnswer: country.name,
    explanation: `${figure.name} is from ${country.name}. ${figure.achievement}`,
    difficulty: difficulty as any,
    countryId: country.id,
  };
};

// Helper functions for generating options
const generateCapitalOptions = (country: Country, allCountries: Country[]): string[] => {
  const options = [country.capital];
  const otherCapitals = allCountries
    .filter(c => c.id !== country.id)
    .map(c => c.capital);
  
  while (options.length < 4) {
    const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];
    if (!options.includes(randomCapital)) {
      options.push(randomCapital);
    }
  }
  
  return shuffleArray(options);
};

const generateCurrencyOptions = (country: Country, allCountries: Country[]): string[] => {
  const options = [country.currency];
  const otherCurrencies = allCountries
    .filter(c => c.id !== country.id)
    .map(c => c.currency);
  
  while (options.length < 4) {
    const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];
    if (!options.includes(randomCurrency)) {
      options.push(randomCurrency);
    }
  }
  
  return shuffleArray(options);
};

const generateRegionOptions = (country: Country, allCountries: Country[]): string[] => {
  const regions = ['North Africa', 'West Africa', 'East Africa', 'Central Africa', 'Southern Africa'];
  const options = [country.region];
  
  regions.forEach(region => {
    if (region !== country.region && options.length < 4) {
      options.push(region);
    }
  });
  
  return shuffleArray(options);
};

const generateIndependenceOptions = (country: Country, allCountries: Country[]): string[] => {
  const year = new Date(country.independence).getFullYear();
  const options = [year.toString()];
  
  // Generate nearby years
  const nearbyYears = [year - 10, year + 5, year - 5];
  nearbyYears.forEach(y => {
    if (options.length < 4) {
      options.push(y.toString());
    }
  });
  
  return shuffleArray(options);
};

const generateCountryOptions = (country: Country, allCountries: Country[]): string[] => {
  const options = [country.name];
  const otherCountries = allCountries
    .filter(c => c.id !== country.id)
    .map(c => c.name);
  
  while (options.length < 4) {
    const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];
    if (!options.includes(randomCountry)) {
      options.push(randomCountry);
    }
  }
  
  return shuffleArray(options);
};

// Format utilities
export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const formatScore = (score: number): string => {
  return `${score}%`;
};

export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num);
};

// Validation utilities
export const validateAnswer = (userAnswer: string, correctAnswer: string): boolean => {
  return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
};

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

export const loadFromLocalStorage = (key: string): any => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.error('Error loading from localStorage:', error);
    return null;
  }
};
