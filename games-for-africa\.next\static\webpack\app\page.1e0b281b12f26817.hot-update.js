"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst JigsawPuzzle = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: \"piece-\".concat(country.id),\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n            // Check if puzzle is complete\n            if (placedPieces.length + 1 === pieces.length) {\n                setGameComplete(true);\n                const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n                const moveBonus = Math.max(0, 50 - moves);\n                const finalScore = score + 10 + timeBonus + moveBonus;\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: \"\".concat(count, \" countries\")\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: \"p-4 rounded-lg transition-all duration-200 border-2 \".concat(selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(selectedRegion === region ? 'text-gray-700' : 'text-gray-400'),\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 \".concat(selectedRegion === region ? 'text-gray-600' : 'text-gray-500'),\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : \"\".concat(selectedRegion, \" Map\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2 min-h-[400px]\",\n                                        style: {\n                                            gridTemplateColumns: \"repeat(\".concat(getGridSize().cols, \", minmax(0, 1fr))\"),\n                                            gridTemplateRows: \"repeat(\".concat(getGridSize().rows, \", minmax(0, 1fr))\")\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 \".concat(placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'),\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, \"\".concat(x, \"-\").concat(y), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : \"Place all \".concat(regionCounts[selectedRegion] || 0, \" countries in \").concat(selectedRegion)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedPiece === piece.id ? 'opacity-50' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: \"\\uD83C\\uDFC6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Puzzle Master!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Time: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JigsawPuzzle, \"akLakEg/BK2nvyLbrgu3pV4cVRk=\");\n_c = JigsawPuzzle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\nvar _c;\n$RefreshReg$(_c, \"JigsawPuzzle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\n"));

/***/ })

});