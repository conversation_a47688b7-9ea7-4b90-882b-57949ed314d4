"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/FlagMatching.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/FlagMatching.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FlagMatching = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('easy');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showCelebration, setShowCelebration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastMatchedCountry, setLastMatchedCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const getDifficultySettings = (diff)=>{\n        switch(diff){\n            case 'easy':\n                return {\n                    pairs: 6,\n                    timeLimit: 120,\n                    multiplier: 1\n                };\n            case 'medium':\n                return {\n                    pairs: 8,\n                    timeLimit: 100,\n                    multiplier: 1.5\n                };\n            case 'hard':\n                return {\n                    pairs: 10,\n                    timeLimit: 80,\n                    multiplier: 2\n                };\n        }\n    };\n    const generateRound = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, settings.pairs);\n        const flags = selectedCountries.map((country)=>({\n                id: \"flag-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        const names = selectedCountries.map((country)=>({\n                id: \"name-\".concat(country.id),\n                country,\n                isMatched: false,\n                isSelected: false\n            }));\n        return {\n            flags: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(flags),\n            names: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(names),\n            selectedFlag: null,\n            selectedName: null,\n            matches: 0,\n            attempts: 0\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"FlagMatching.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"FlagMatching.useEffect.timer\"], 1000);\n                return ({\n                    \"FlagMatching.useEffect\": ()=>clearTimeout(timer)\n                })[\"FlagMatching.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagMatching.useEffect\": ()=>{\n            if (currentRound && gameStarted && !gameComplete && !showVictory) {\n                const settings = getDifficultySettings(difficulty);\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.checkGameCompletion['flag-matching'](currentRound.matches, settings.pairs)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"FlagMatching.useEffect\"], [\n        currentRound === null || currentRound === void 0 ? void 0 : currentRound.matches,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = ()=>{\n        const settings = getDifficultySettings(difficulty);\n        const newRound = generateRound();\n        setCurrentRound(newRound);\n        setScore(0);\n        setStreak(0);\n        setTotalMatches(0);\n        setTimeLeft(settings.timeLimit);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowCelebration(false);\n    };\n    const handleFlagClick = (flagId)=>{\n        if (!currentRound || gameComplete) return;\n        const flag = currentRound.flags.find((f)=>f.id === flagId);\n        if (!flag || flag.isMatched) return;\n        // Clear previous selections\n        const updatedFlags = currentRound.flags.map((f)=>({\n                ...f,\n                isSelected: f.id === flagId\n            }));\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: false\n            }));\n        setCurrentRound({\n            ...currentRound,\n            flags: updatedFlags,\n            names: updatedNames,\n            selectedFlag: flagId,\n            selectedName: null\n        });\n    };\n    const handleNameClick = (nameId)=>{\n        if (!currentRound || gameComplete) return;\n        const name = currentRound.names.find((n)=>n.id === nameId);\n        if (!name || name.isMatched) return;\n        const updatedNames = currentRound.names.map((n)=>({\n                ...n,\n                isSelected: n.id === nameId\n            }));\n        const newRound = {\n            ...currentRound,\n            names: updatedNames,\n            selectedName: nameId,\n            attempts: currentRound.attempts + 1\n        };\n        // Check for match if both flag and name are selected\n        if (currentRound.selectedFlag) {\n            const selectedFlag = currentRound.flags.find((f)=>f.id === currentRound.selectedFlag);\n            const selectedName = name;\n            if (selectedFlag && selectedName && selectedFlag.country.id === selectedName.country.id) {\n                // Match found!\n                const updatedFlags = newRound.flags.map((f)=>({\n                        ...f,\n                        isMatched: f.id === currentRound.selectedFlag ? true : f.isMatched,\n                        isSelected: false\n                    }));\n                const updatedNamesMatched = newRound.names.map((n)=>({\n                        ...n,\n                        isMatched: n.id === nameId ? true : n.isMatched,\n                        isSelected: false\n                    }));\n                const settings = getDifficultySettings(difficulty);\n                const basePoints = 10;\n                const timeBonus = Math.floor(timeLeft / 10);\n                const streakBonus = streak * 2;\n                const roundScore = Math.floor((basePoints + timeBonus + streakBonus) * settings.multiplier);\n                setScore(score + roundScore);\n                setStreak(streak + 1);\n                setTotalMatches(totalMatches + 1);\n                setLastMatchedCountry(selectedFlag.country);\n                setShowCelebration(true);\n                setTimeout(()=>setShowCelebration(false), 2000);\n                setCurrentRound({\n                    ...newRound,\n                    flags: updatedFlags,\n                    names: updatedNamesMatched,\n                    matches: newRound.matches + 1,\n                    selectedFlag: null,\n                    selectedName: null\n                });\n            } else {\n                // No match - reset selections after brief delay\n                setStreak(0);\n                setTimeout(()=>{\n                    if (currentRound) {\n                        const resetFlags = newRound.flags.map((f)=>({\n                                ...f,\n                                isSelected: false\n                            }));\n                        const resetNames = newRound.names.map((n)=>({\n                                ...n,\n                                isSelected: false\n                            }));\n                        setCurrentRound({\n                            ...newRound,\n                            flags: resetFlags,\n                            names: resetNames,\n                            selectedFlag: null,\n                            selectedName: null\n                        });\n                    }\n                }, 1000);\n            }\n        } else {\n            setCurrentRound(newRound);\n        }\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Match African country flags with their names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'easy',\n                                    'medium',\n                                    'hard'\n                                ].map((diff)=>{\n                                    const settings = getDifficultySettings(diff);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        settings.pairs,\n                                                        \" pairs • \",\n                                                        settings.timeLimit,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Click a flag, then click the matching country name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Complete all pairs before time runs out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about all 50+ African countries\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Matching\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!currentRound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, undefined);\n    }\n    const settings = getDifficultySettings(difficulty);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFC1 Flag Matching Game\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeLeft)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound.matches\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Matches\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: settings.pairs\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat(currentRound.matches / settings.pairs * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300\",\n                    children: currentRound.selectedFlag ? \"Now click the matching country name!\" : \"Click a flag to start matching!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83C\\uDFC1 Flags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                children: currentRound.flags.map((flag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleFlagClick(flag.id),\n                                        disabled: flag.isMatched,\n                                        className: \"p-4 rounded-lg border-2 transition-all duration-200 \".concat(flag.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : flag.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400 transform scale-105' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        countryId: flag.country.id,\n                                                        size: \"xl\",\n                                                        className: \"mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                flag.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 text-sm font-medium\",\n                                                    children: \"✓ Matched\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, flag.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 text-center\",\n                                children: \"\\uD83D\\uDCDD Country Names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: currentRound.names.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNameClick(name.id),\n                                        disabled: name.isMatched,\n                                        className: \"w-full p-3 rounded-lg border-2 transition-all duration-200 text-left \".concat(name.isMatched ? 'bg-green-500 bg-opacity-20 border-green-400 cursor-not-allowed' : name.isSelected ? 'bg-yellow-400 bg-opacity-20 border-yellow-400' : 'bg-gray-700 border-gray-600 hover:border-gray-500 hover:bg-gray-600 cursor-pointer'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: name.country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                name.isMatched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, name.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            showCelebration && lastMatchedCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 bg-opacity-90 rounded-lg p-6 text-center animate-bounce\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                countryId: lastMatchedCountry.id,\n                                size: \"large\",\n                                className: \"mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"Perfect Match!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-100\",\n                            children: lastMatchedCountry.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 422,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: currentRound.matches === settings.pairs ? '🏆' : timeLeft === 0 ? '⏰' : '🏁'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: currentRound.matches === settings.pairs ? 'Perfect Match!' : 'Time\\'s Up!'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Matches: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: currentRound.matches\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    \"/\",\n                                                    settings.pairs\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: streak\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Accuracy: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: [\n                                                            Math.round(currentRound.matches / Math.max(currentRound.attempts, 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 32\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n                lineNumber: 439,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\FlagMatching.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlagMatching, \"ToZimn3Niwr/0+VRb4yYWuUDEx0=\");\n_c = FlagMatching;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagMatching);\nvar _c;\n$RefreshReg$(_c, \"FlagMatching\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/FlagMatching.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/gameCompletion.ts":
/*!*************************************!*\
  !*** ./src/utils/gameCompletion.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   calculateXP: () => (/* binding */ calculateXP),\n/* harmony export */   celebrationConfigs: () => (/* binding */ celebrationConfigs),\n/* harmony export */   checkGameCompletion: () => (/* binding */ checkGameCompletion),\n/* harmony export */   getCompletionMessage: () => (/* binding */ getCompletionMessage),\n/* harmony export */   getNextAction: () => (/* binding */ getNextAction),\n/* harmony export */   processGameCompletion: () => (/* binding */ processGameCompletion)\n/* harmony export */ });\n// Game completion utilities and animations\n// Calculate time bonus based on remaining time\nconst calculateTimeBonus = (timeRemaining, totalTime, difficulty)=>{\n    if (timeRemaining <= 0) return 0;\n    const timePercentage = timeRemaining / totalTime;\n    const difficultyMultiplier = {\n        easy: 1,\n        medium: 1.5,\n        hard: 2\n    }[difficulty];\n    // Base time bonus: up to 50 points for completing with full time remaining\n    const baseBonus = Math.floor(timePercentage * 50);\n    return Math.floor(baseBonus * difficultyMultiplier);\n};\n// Calculate XP based on performance\nconst calculateXP = (data)=>{\n    const baseXP = 20; // Base XP for completion\n    const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const difficultyBonus = {\n        easy: 0,\n        medium: 10,\n        hard: 20\n    }[data.difficulty];\n    const perfectBonus = data.perfectScore ? 30 : 0;\n    return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;\n};\n// Determine completion message and type\nconst getCompletionMessage = (data)=>{\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore && timePercentage > 0.7) {\n        return {\n            message: 'Perfect! Outstanding performance!',\n            type: 'perfect'\n        };\n    } else if (data.perfectScore && timePercentage > 0.4) {\n        return {\n            message: 'Excellent! Great job!',\n            type: 'excellent'\n        };\n    } else if (data.perfectScore) {\n        return {\n            message: 'Perfect Score! Well done!',\n            type: 'excellent'\n        };\n    } else if (timePercentage > 0.5) {\n        return {\n            message: 'Great work! Fast completion!',\n            type: 'good'\n        };\n    } else {\n        return {\n            message: 'Game Complete! Nice job!',\n            type: 'complete'\n        };\n    }\n};\n// Process game completion\nconst processGameCompletion = (data)=>{\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const finalScore = data.score + timeBonus;\n    const xpGained = calculateXP(data);\n    const { message, type } = getCompletionMessage(data);\n    // Determine achievements based on performance\n    const achievements = [];\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore) {\n        achievements.push('Perfect Score');\n    }\n    if (timePercentage > 0.8) {\n        achievements.push('Speed Demon');\n    }\n    if (data.difficulty === 'hard' && data.perfectScore) {\n        achievements.push('Master Player');\n    }\n    if (timeBonus > 30) {\n        achievements.push('Time Master');\n    }\n    return {\n        finalScore,\n        timeBonus,\n        xpGained,\n        achievements,\n        message,\n        celebrationType: type\n    };\n};\n// Game-specific completion checkers\nconst checkGameCompletion = {\n    'flag-matching': (matches, totalPairs)=>{\n        return matches >= totalPairs;\n    },\n    'country-name-scramble': (currentRound, totalRounds, roundComplete)=>{\n        return currentRound >= totalRounds - 1 && roundComplete;\n    },\n    'jigsaw-puzzle': (placedPieces, totalPieces)=>{\n        return placedPieces >= totalPieces;\n    },\n    'quiz': (answeredQuestions, totalQuestions)=>{\n        return answeredQuestions >= totalQuestions;\n    },\n    'memory-grid': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'matching': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'country-explorer': (visitedCountries, targetCountries)=>{\n        return visitedCountries >= targetCountries;\n    },\n    'speed-challenge': (answeredQuestions, targetQuestions)=>{\n        return answeredQuestions >= targetQuestions;\n    },\n    'mystery-land': (correctGuesses, totalRounds)=>{\n        return correctGuesses >= totalRounds;\n    },\n    'timeline-builder': (placedEvents, totalEvents)=>{\n        return placedEvents >= totalEvents;\n    },\n    'where-in-africa': (completedRounds, totalRounds)=>{\n        return completedRounds >= totalRounds;\n    },\n    'dress-character': (completedOutfits, targetOutfits)=>{\n        return completedOutfits >= targetOutfits;\n    }\n};\n// Celebration animation configurations\nconst celebrationConfigs = {\n    perfect: {\n        duration: 3000,\n        emoji: '🏆',\n        colors: [\n            '#FFD700',\n            '#FFA500',\n            '#FF6B6B'\n        ],\n        particles: 50\n    },\n    excellent: {\n        duration: 2500,\n        emoji: '🎉',\n        colors: [\n            '#4ECDC4',\n            '#45B7D1',\n            '#96CEB4'\n        ],\n        particles: 40\n    },\n    good: {\n        duration: 2000,\n        emoji: '👏',\n        colors: [\n            '#FECA57',\n            '#48CAE4',\n            '#A8E6CF'\n        ],\n        particles: 30\n    },\n    complete: {\n        duration: 1500,\n        emoji: '✅',\n        colors: [\n            '#6C5CE7',\n            '#A29BFE',\n            '#74B9FF'\n        ],\n        particles: 20\n    }\n};\n// Auto-progression logic\nconst getNextAction = (gameType, difficulty)=>{\n    // If completed on easy or medium, suggest next difficulty\n    if (difficulty === 'easy') return 'next-difficulty';\n    if (difficulty === 'medium') return 'next-difficulty';\n    // If completed on hard, return to menu\n    return 'menu';\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/gameCompletion.ts\n"));

/***/ })

});