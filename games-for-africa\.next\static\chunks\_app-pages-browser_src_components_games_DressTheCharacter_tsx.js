"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_games_DressTheCharacter_tsx"],{

/***/ "(app-pages-browser)/./src/components/games/DressTheCharacter.tsx":
/*!****************************************************!*\
  !*** ./src/components/games/DressTheCharacter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DressTheCharacter = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [targetCountry, setTargetCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availableClothing, setAvailableClothing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        head: null,\n        body: null,\n        accessory: null\n    });\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [round, setRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const maxRounds = 5;\n    const generateClothingItems = (targetCountry, otherCountries)=>{\n        const items = [];\n        // Target country items (correct answers)\n        targetCountry.culturalElements.traditionalClothing.forEach((clothing, index)=>{\n            const types = [\n                'head',\n                'body',\n                'accessory'\n            ];\n            items.push({\n                id: \"\".concat(targetCountry.id, \"-\").concat(index),\n                name: clothing,\n                country: targetCountry.name,\n                countryId: targetCountry.id,\n                type: types[index % 3],\n                emoji: index % 3 === 0 ? '👑' : index % 3 === 1 ? '👘' : '📿'\n            });\n        });\n        // Other countries' items (wrong answers)\n        otherCountries.forEach((country)=>{\n            country.culturalElements.traditionalClothing.forEach((clothing, index)=>{\n                const types = [\n                    'head',\n                    'body',\n                    'accessory'\n                ];\n                items.push({\n                    id: \"\".concat(country.id, \"-\").concat(index),\n                    name: clothing,\n                    country: country.name,\n                    countryId: country.id,\n                    type: types[index % 3],\n                    emoji: index % 3 === 0 ? '🎩' : index % 3 === 1 ? '👔' : '⌚'\n                });\n            });\n        });\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(items);\n    };\n    const startNewRound = ()=>{\n        const selectedCountry = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, 1)[0];\n        const otherCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries.filter((c)=>c.id !== selectedCountry.id), 3);\n        setTargetCountry(selectedCountry);\n        setAvailableClothing(generateClothingItems(selectedCountry, otherCountries));\n        setCharacter({\n            head: null,\n            body: null,\n            accessory: null\n        });\n        setShowResult(false);\n    };\n    const startGame = ()=>{\n        setScore(0);\n        setRound(1);\n        setGameComplete(false);\n        setGameStarted(true);\n        startNewRound();\n    };\n    const handleClothingSelect = (item)=>{\n        setCharacter((prev)=>({\n                ...prev,\n                [item.type]: item\n            }));\n    };\n    const removeClothing = (type)=>{\n        setCharacter((prev)=>({\n                ...prev,\n                [type]: null\n            }));\n    };\n    const submitOutfit = ()=>{\n        if (!targetCountry) return;\n        let correctItems = 0;\n        let totalItems = 0;\n        Object.values(character).forEach((item)=>{\n            if (item) {\n                totalItems++;\n                if (item.countryId === targetCountry.id) {\n                    correctItems++;\n                }\n            }\n        });\n        const roundScore = totalItems > 0 ? Math.round(correctItems / totalItems * 20) : 0;\n        setScore(score + roundScore);\n        setShowResult(true);\n        setTimeout(()=>{\n            if (round < maxRounds) {\n                setRound(round + 1);\n                startNewRound();\n            } else {\n                setGameComplete(true);\n                setTimeout(()=>onComplete(score + roundScore), 1000);\n            }\n        }, 3000);\n    };\n    const isOutfitComplete = ()=>{\n        return character.head || character.body || character.accessory;\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83C\\uDFAD Dress the Character\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDC57\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Dress characters in traditional African clothing from different countries!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    maxRounds,\n                                    \" rounds of fashion challenges\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Choose traditional clothing from the target country\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Mix and match head wear, body clothing, and accessories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Score points for authentic cultural outfits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Styling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!targetCountry) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83C\\uDFAD Dress the Character\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    \"Round \",\n                                    round,\n                                    \"/\",\n                                    maxRounds\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl mb-2\",\n                                children: targetCountry.flagUrl\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Dress in traditional \",\n                                    targetCountry.name,\n                                    \" clothing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: [\n                                    \"Score: \",\n                                    score,\n                                    \" points\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDC64 Character\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl\",\n                                                        children: \"\\uD83D\\uDE0A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    character.head && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeClothing('head'),\n                                                            className: \"bg-blue-500 bg-opacity-20 border border-blue-400 rounded-lg px-3 py-1 text-blue-300 text-sm hover:bg-blue-500 hover:bg-opacity-30\",\n                                                            children: [\n                                                                character.head.emoji,\n                                                                \" \",\n                                                                character.head.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-8xl\",\n                                                        children: \"\\uD83E\\uDDCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    character.body && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeClothing('body'),\n                                                            className: \"bg-green-500 bg-opacity-20 border border-green-400 rounded-lg px-3 py-1 text-green-300 text-sm hover:bg-green-500 hover:bg-opacity-30\",\n                                                            children: [\n                                                                character.body.emoji,\n                                                                \" \",\n                                                                character.body.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: character.accessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeClothing('accessory'),\n                                                        className: \"bg-purple-500 bg-opacity-20 border border-purple-400 rounded-lg px-3 py-1 text-purple-300 text-sm hover:bg-purple-500 hover:bg-opacity-30\",\n                                                        children: [\n                                                            character.accessory.emoji,\n                                                            \" \",\n                                                            character.accessory.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: submitOutfit,\n                                            disabled: !isOutfitComplete() || showResult,\n                                            className: \"w-full py-3 px-4 rounded-lg font-bold transition-colors \".concat(isOutfitComplete() && !showResult ? 'bg-yellow-400 text-gray-900 hover:bg-yellow-300' : 'bg-gray-600 text-gray-400 cursor-not-allowed'),\n                                            children: showResult ? '⏳ Next Round...' : '✨ Complete Outfit'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDC57 Clothing Options\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-blue-300 mb-3\",\n                                        children: \"\\uD83D\\uDC51 Head Wear\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'head').map((item)=>{\n                                            var _character_head;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-blue-400 transition-all duration-200 \".concat(((_character_head = character.head) === null || _character_head === void 0 ? void 0 : _character_head.id) === item.id ? 'border-blue-400 bg-blue-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-green-300 mb-3\",\n                                        children: \"\\uD83D\\uDC58 Body Clothing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'body').map((item)=>{\n                                            var _character_body;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-green-400 transition-all duration-200 \".concat(((_character_body = character.body) === null || _character_body === void 0 ? void 0 : _character_body.id) === item.id ? 'border-green-400 bg-green-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-purple-300 mb-3\",\n                                        children: \"\\uD83D\\uDCFF Accessories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: availableClothing.filter((item)=>item.type === 'accessory').map((item)=>{\n                                            var _character_accessory;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleClothingSelect(item),\n                                                disabled: showResult,\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 hover:border-purple-400 transition-all duration-200 \".concat(((_character_accessory = character.accessory) === null || _character_accessory === void 0 ? void 0 : _character_accessory.id) === item.id ? 'border-purple-400 bg-purple-500 bg-opacity-20' : '', \" \").concat(showResult ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-1\",\n                                                        children: item.emoji\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: item.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-gray-800 border border-gray-700 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-4\",\n                            children: [\n                                \"Round \",\n                                round,\n                                \" Results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: Object.entries(character).map((param)=>{\n                                let [type, item] = param;\n                                if (!item) return null;\n                                const isCorrect = item.countryId === targetCountry.id;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-20 border-green-400' : 'bg-red-500 bg-opacity-20 border-red-400'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-1\",\n                                            children: item.emoji\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-medium\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: item.country\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm mt-1 \".concat(isCorrect ? 'text-green-300' : 'text-red-300'),\n                                            children: isCorrect ? '✓ Correct!' : '✗ Wrong country'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 80 ? '🏆' : score >= 60 ? '🎉' : '🎭'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Fashion Show Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 35\n                                                    }, undefined),\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: score >= 80 ? 'Fashion Expert!' : score >= 60 ? 'Style Star!' : 'Keep Learning!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Style Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n                lineNumber: 369,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\DressTheCharacter.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DressTheCharacter, \"BnZM+SaSRtauKLp0mOx0dvcn9vM=\");\n_c = DressTheCharacter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DressTheCharacter);\nvar _c;\n$RefreshReg$(_c, \"DressTheCharacter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = function() {\n    let newSeed = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = function(correct, total) {\n    let timeBonus = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, difficultyMultiplier = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, streakBonus = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = function(countries, category, difficulty) {\n    let count = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 10;\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the capital of \".concat(country.name, \"?\"),\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: \"\".concat(country.capital, \" is the capital city of \").concat(country.name, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"What is the currency of \".concat(country.name, \"?\"),\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: \"The currency of \".concat(country.name, \" is \").concat(country.currency, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: \"Which region is \".concat(country.name, \" located in?\"),\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: \"\".concat(country.name, \" is located in \").concat(country.region, \".\"),\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: \"When did \".concat(country.name, \" gain independence?\"),\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: \"\".concat(country.name, \" gained independence in \").concat(new Date(country.independence).getFullYear(), \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: \"Which country is known for \".concat(item, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(item, \" is a traditional \").concat(aspect.slice(0, -1), \" from \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: \"Which country is home to \".concat(animal, \"?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(animal, \" can be found in \").concat(country.name, \".\"),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: \"\".concat(figure.name, \" is from which country?\"),\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: \"\".concat(figure.name, \" is from \").concat(country.name, \". \").concat(figure.achievement),\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n};\nconst formatScore = (score)=>{\n    return \"\".concat(score, \"%\");\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/index.ts\n"));

/***/ })

}]);