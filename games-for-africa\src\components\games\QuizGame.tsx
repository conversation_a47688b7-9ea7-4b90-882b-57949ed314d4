'use client';

import React, { useState, useEffect } from 'react';
import { QuizGameState, QuizQuestion, Country } from '@/types';
import { generateQuizQuestions, calculateScore, formatTime } from '@/utils';

interface QuizGameProps {
  countries: Country[];
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  onComplete: (score: number) => void;
}

const QuizGame: React.FC<QuizGameProps> = ({
  countries,
  category,
  difficulty,
  onComplete,
}) => {
  const [gameState, setGameState] = useState<QuizGameState>({
    currentQuestion: 0,
    questions: [],
    selectedAnswers: [],
    score: 0,
    timeRemaining: 300, // 5 minutes
    isComplete: false,
    showExplanation: false,
  });
  const [showResults, setShowResults] = useState(false);

  // Initialize game
  useEffect(() => {
    const questions = generateQuizQuestions(countries, category, difficulty, 10);
    const initialState = {
      ...gameState,
      questions,
      selectedAnswers: new Array(questions.length).fill(null),
    };
    setGameState(initialState);
  }, [countries, category, difficulty]);

  // Timer
  useEffect(() => {
    if (gameState.timeRemaining > 0 && !gameState.isComplete) {
      const timer = setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          timeRemaining: prev.timeRemaining - 1,
        }));
      }, 1000);
      return () => clearTimeout(timer);
    } else if (gameState.timeRemaining === 0) {
      handleGameComplete();
    }
  }, [gameState.timeRemaining, gameState.isComplete]);

  const handleAnswerSelect = (answer: string) => {
    const newAnswers = [...gameState.selectedAnswers];
    newAnswers[gameState.currentQuestion] = answer;
    
    setGameState(prev => ({
      ...prev,
      selectedAnswers: newAnswers,
      showExplanation: true,
    }));
  };

  const handleNextQuestion = () => {
    if (gameState.currentQuestion < gameState.questions.length - 1) {
      setGameState(prev => ({
        ...prev,
        currentQuestion: prev.currentQuestion + 1,
        showExplanation: false,
      }));
    } else {
      handleGameComplete();
    }
  };

  const handleGameComplete = () => {
    const correctAnswers = gameState.selectedAnswers.filter(
      (answer, index) => answer === gameState.questions[index]?.correctAnswer
    ).length;

    const finalScore = calculateScore(
      correctAnswers,
      gameState.questions.length,
      0, // No time bonus for now
      difficulty === 'easy' ? 1 : difficulty === 'medium' ? 1.5 : 2
    );

    setGameState(prev => ({
      ...prev,
      score: finalScore,
      isComplete: true,
    }));

    setShowResults(true);
    onComplete(finalScore);
  };

  const handleRestart = () => {
    const questions = generateQuizQuestions(countries, category, difficulty, 10);
    setGameState({
      currentQuestion: 0,
      questions,
      selectedAnswers: new Array(questions.length).fill(null),
      score: 0,
      timeRemaining: 300,
      isComplete: false,
      showExplanation: false,
    });
    setShowResults(false);
  };

  const currentQuestion = gameState.questions[gameState.currentQuestion];
  const selectedAnswer = gameState.selectedAnswers[gameState.currentQuestion];
  const progress = ((gameState.currentQuestion + 1) / gameState.questions.length) * 100;

  if (!currentQuestion) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-gold"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">
            {category.charAt(0).toUpperCase() + category.slice(1)} Quiz
          </h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-gray-300">
              <span>⏰</span>
              <span className="font-mono">{formatTime(gameState.timeRemaining)}</span>
            </div>
            <div className="text-gray-300">
              {gameState.currentQuestion + 1} / {gameState.questions.length}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-6">
          <div
            className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Question */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div key={gameState.currentQuestion}>
          <h3 className="text-xl font-semibold text-white mb-6">
            {currentQuestion.question}
          </h3>

          {/* Options */}
          <div className="grid gap-3">
            {currentQuestion.options?.map((option, index) => {
              const isSelected = selectedAnswer === option;
              const isCorrect = option === currentQuestion.correctAnswer;
              const isIncorrect = isSelected && !isCorrect && gameState.showExplanation;

              let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';
              if (gameState.showExplanation) {
                if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';
                else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';
              } else if (isSelected) {
                optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';
              }

              return (
                <button
                  key={index}
                  className={optionClass}
                  onClick={() => !gameState.showExplanation && handleAnswerSelect(option)}
                  disabled={gameState.showExplanation}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-left text-white">{option}</span>
                    {gameState.showExplanation && isCorrect && (
                      <span className="text-green-400">✓</span>
                    )}
                    {gameState.showExplanation && isIncorrect && (
                      <span className="text-red-400">✗</span>
                    )}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Explanation */}
          {gameState.showExplanation && (
            <div className="mt-6 p-4 bg-gray-900 rounded-lg border border-gray-600">
              <p className="text-gray-300">{currentQuestion.explanation}</p>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={handleNextQuestion}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg font-medium hover:bg-yellow-300 transition-colors"
                >
                  {gameState.currentQuestion < gameState.questions.length - 1 ? (
                    <>Next Question →</>
                  ) : (
                    'Complete Quiz'
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Results Modal */}
      {showResults && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {gameState.score >= 80 ? '🎉' : gameState.score >= 60 ? '👏' : '💪'}
              </div>

              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  {gameState.score}%
                </h3>
                <p className="text-gray-300">
                  You got {gameState.selectedAnswers.filter(
                    (answer, index) => answer === gameState.questions[index]?.correctAnswer
                  ).length} out of {gameState.questions.length} questions correct!
                </p>
              </div>

              <div className="flex space-x-4 justify-center">
                <button
                  onClick={() => setShowResults(false)}
                  className="bg-gray-700 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={handleRestart}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizGame;
