'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, CheckCircle, XCircle, ArrowRight, RotateCcw } from 'lucide-react';
import { QuizGameState, QuizQuestion, Country } from '@/types';
import { generateQuizQuestions, calculateScore, formatTime } from '@/utils';
import { useGameStore } from '@/hooks/useGameStore';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Modal from '@/components/ui/Modal';

interface QuizGameProps {
  countries: Country[];
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  onComplete: (score: number) => void;
}

const QuizGame: React.FC<QuizGameProps> = ({
  countries,
  category,
  difficulty,
  onComplete,
}) => {
  const { updateGameState } = useGameStore();
  const [gameState, setGameState] = useState<QuizGameState>({
    currentQuestion: 0,
    questions: [],
    selectedAnswers: [],
    score: 0,
    timeRemaining: 300, // 5 minutes
    isComplete: false,
    showExplanation: false,
  });
  const [showResults, setShowResults] = useState(false);

  // Initialize game
  useEffect(() => {
    const questions = generateQuizQuestions(countries, category, difficulty, 10);
    const initialState = {
      ...gameState,
      questions,
      selectedAnswers: new Array(questions.length).fill(null),
    };
    setGameState(initialState);
    updateGameState(initialState);
  }, [countries, category, difficulty]);

  // Timer
  useEffect(() => {
    if (gameState.timeRemaining > 0 && !gameState.isComplete) {
      const timer = setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          timeRemaining: prev.timeRemaining - 1,
        }));
      }, 1000);
      return () => clearTimeout(timer);
    } else if (gameState.timeRemaining === 0) {
      handleGameComplete();
    }
  }, [gameState.timeRemaining, gameState.isComplete]);

  const handleAnswerSelect = (answer: string) => {
    const newAnswers = [...gameState.selectedAnswers];
    newAnswers[gameState.currentQuestion] = answer;
    
    setGameState(prev => ({
      ...prev,
      selectedAnswers: newAnswers,
      showExplanation: true,
    }));
  };

  const handleNextQuestion = () => {
    if (gameState.currentQuestion < gameState.questions.length - 1) {
      setGameState(prev => ({
        ...prev,
        currentQuestion: prev.currentQuestion + 1,
        showExplanation: false,
      }));
    } else {
      handleGameComplete();
    }
  };

  const handleGameComplete = () => {
    const correctAnswers = gameState.selectedAnswers.filter(
      (answer, index) => answer === gameState.questions[index]?.correctAnswer
    ).length;

    const finalScore = calculateScore(
      correctAnswers,
      gameState.questions.length,
      0, // No time bonus for now
      difficulty === 'easy' ? 1 : difficulty === 'medium' ? 1.5 : 2
    );

    setGameState(prev => ({
      ...prev,
      score: finalScore,
      isComplete: true,
    }));

    setShowResults(true);
    onComplete(finalScore);
  };

  const handleRestart = () => {
    const questions = generateQuizQuestions(countries, category, difficulty, 10);
    setGameState({
      currentQuestion: 0,
      questions,
      selectedAnswers: new Array(questions.length).fill(null),
      score: 0,
      timeRemaining: 300,
      isComplete: false,
      showExplanation: false,
    });
    setShowResults(false);
  };

  const currentQuestion = gameState.questions[gameState.currentQuestion];
  const selectedAnswer = gameState.selectedAnswers[gameState.currentQuestion];
  const progress = ((gameState.currentQuestion + 1) / gameState.questions.length) * 100;

  if (!currentQuestion) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-gold"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-text-primary">
            {category.charAt(0).toUpperCase() + category.slice(1)} Quiz
          </h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-text-secondary">
              <Clock className="w-5 h-5" />
              <span className="font-mono">{formatTime(gameState.timeRemaining)}</span>
            </div>
            <div className="text-text-secondary">
              {gameState.currentQuestion + 1} / {gameState.questions.length}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="progress-bar">
          <motion.div
            className="progress-fill"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Question */}
      <Card className="mb-6">
        <motion.div
          key={gameState.currentQuestion}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-xl font-semibold text-text-primary mb-6">
            {currentQuestion.question}
          </h3>

          {/* Options */}
          <div className="grid gap-3">
            {currentQuestion.options?.map((option, index) => {
              const isSelected = selectedAnswer === option;
              const isCorrect = option === currentQuestion.correctAnswer;
              const isIncorrect = isSelected && !isCorrect && gameState.showExplanation;
              
              let optionClass = 'quiz-option';
              if (gameState.showExplanation) {
                if (isCorrect) optionClass += ' correct';
                else if (isIncorrect) optionClass += ' incorrect';
              } else if (isSelected) {
                optionClass += ' selected';
              }

              return (
                <motion.button
                  key={index}
                  className={optionClass}
                  onClick={() => !gameState.showExplanation && handleAnswerSelect(option)}
                  disabled={gameState.showExplanation}
                  whileHover={{ scale: gameState.showExplanation ? 1 : 1.02 }}
                  whileTap={{ scale: gameState.showExplanation ? 1 : 0.98 }}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-left">{option}</span>
                    {gameState.showExplanation && isCorrect && (
                      <CheckCircle className="w-5 h-5 text-accent-green" />
                    )}
                    {gameState.showExplanation && isIncorrect && (
                      <XCircle className="w-5 h-5 text-accent-crimson" />
                    )}
                  </div>
                </motion.button>
              );
            })}
          </div>

          {/* Explanation */}
          <AnimatePresence>
            {gameState.showExplanation && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-6 p-4 bg-primary-dark rounded-lg border border-gray-600"
              >
                <p className="text-text-secondary">{currentQuestion.explanation}</p>
                <div className="mt-4 flex justify-end">
                  <Button onClick={handleNextQuestion}>
                    {gameState.currentQuestion < gameState.questions.length - 1 ? (
                      <>
                        Next Question
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    ) : (
                      'Complete Quiz'
                    )}
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </Card>

      {/* Results Modal */}
      <Modal
        isOpen={showResults}
        onClose={() => setShowResults(false)}
        title="Quiz Complete!"
        size="md"
      >
        <div className="text-center space-y-6">
          <div className="text-6xl">
            {gameState.score >= 80 ? '🎉' : gameState.score >= 60 ? '👏' : '💪'}
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-accent-gold mb-2">
              {gameState.score}%
            </h3>
            <p className="text-text-secondary">
              You got {gameState.selectedAnswers.filter(
                (answer, index) => answer === gameState.questions[index]?.correctAnswer
              ).length} out of {gameState.questions.length} questions correct!
            </p>
          </div>

          <div className="flex space-x-4 justify-center">
            <Button variant="secondary" onClick={() => setShowResults(false)}>
              Close
            </Button>
            <Button onClick={handleRestart}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Play Again
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default QuizGame;
