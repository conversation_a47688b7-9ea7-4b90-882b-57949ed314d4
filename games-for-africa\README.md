# Games for Africa 🌍

An educational gaming website that teaches users about African countries, cultures, and achievements through interactive games and challenges.

## 🎮 Features

- **Interactive Quiz System**: Test your knowledge about African geography, history, culture, wildlife, and notable figures
- **Country Database**: Comprehensive information about 54 African countries
- **Responsive Design**: Mobile-first design that works on all devices
- **Progress Tracking**: Track your scores and achievements
- **Educational Content**: Learn about African cultures, landmarks, and achievements

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd games-for-africa
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🎯 Available Games

### 1. Interactive Quiz
- Multiple choice questions about African countries
- Categories: Geography, History, Culture, Wildlife, Notable Figures
- Difficulty levels: Easy, Medium, Hard
- Real-time scoring and explanations

### 2. Country Matching (Coming Soon)
- Match countries with capitals, flags, and currencies
- Drag-and-drop interface

### 3. Memory Cards (Coming Soon)
- Find matching pairs of African landmarks and cultural symbols

### 4. Map Puzzle (Coming Soon)
- Place African countries in their correct locations

### 5. Timed Challenge (Coming Soon)
- Answer as many questions as possible in 60 seconds

### 6. Cultural Photo Quiz (Coming Soon)
- Identify African cultures through images

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js app directory
├── components/          # React components
│   ├── games/          # Game components
│   ├── ui/             # Reusable UI components
│   └── layout/         # Layout components
├── data/               # JSON data files
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Development**: ESLint, Prettier

## 🎨 Design System

### Color Palette
- **Primary Dark**: #1a1a1a (main background)
- **Primary Light**: #2d2d2d (card backgrounds)
- **Accent Gold**: #FFD700 (highlights and buttons)
- **Accent Green**: #228B22 (success states)
- **Accent Crimson**: #DC143C (error states)
- **Accent Blue**: #4169E1 (info states)

### Typography
- **Primary Font**: Inter
- **Display Font**: Poppins

## 📊 Data Structure

The application uses a comprehensive JSON database containing information about African countries including:

- Basic information (name, capital, currency, languages)
- Demographics and geography
- Cultural elements (clothing, cuisine, music, dances)
- Notable figures and achievements
- Wildlife and landmarks

## 🚀 Deployment

The application can be deployed on Vercel, Netlify, or any platform that supports Next.js:

```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- African Union for official country data
- UNESCO for cultural information
- Various educational resources for accurate content

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.

---

**Built with ❤️ to celebrate African heritage and education**
