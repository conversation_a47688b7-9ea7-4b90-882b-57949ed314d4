'use client';

import React, { Suspense, lazy } from 'react';
import { GameType } from '@/types';
import LoadingSkeleton from './LoadingSkeleton';

// Lazy load all game components
const QuizGame = lazy(() => import('@/components/games/QuizGame'));
const MatchingGame = lazy(() => import('@/components/games/MatchingGame'));
const JigsawPuzzle = lazy(() => import('@/components/games/JigsawPuzzle'));
const MemoryGrid = lazy(() => import('@/components/games/MemoryGrid'));
const SpeedChallenge = lazy(() => import('@/components/games/SpeedChallenge'));
const CountryExplorer = lazy(() => import('@/components/games/CountryExplorer'));
const MysteryLand = lazy(() => import('@/components/games/MysteryLand'));
const TimelineBuilder = lazy(() => import('@/components/games/TimelineBuilder'));
const WhereInAfrica = lazy(() => import('@/components/games/WhereInAfrica'));
const DressTheCharacter = lazy(() => import('@/components/games/DressTheCharacter'));
const CountryNameScramble = lazy(() => import('@/components/games/CountryNameScramble'));
const FlagMatching = lazy(() => import('@/components/games/FlagMatching'));

interface LazyGameLoaderProps {
  gameType: GameType;
  gameProps: any;
}

const LazyGameLoader: React.FC<LazyGameLoaderProps> = ({ gameType, gameProps }) => {
  const renderGame = () => {
    switch (gameType) {
      case 'quiz':
        return <QuizGame {...gameProps} />;
      case 'matching':
        return <MatchingGame {...gameProps} />;
      case 'jigsaw-puzzle':
        return <JigsawPuzzle {...gameProps} />;
      case 'memory-grid':
        return <MemoryGrid {...gameProps} />;
      case 'speed-challenge':
        return <SpeedChallenge {...gameProps} />;
      case 'country-explorer':
        return <CountryExplorer {...gameProps} />;
      case 'mystery-land':
        return <MysteryLand {...gameProps} />;
      case 'timeline-builder':
        return <TimelineBuilder {...gameProps} />;
      case 'where-in-africa':
        return <WhereInAfrica {...gameProps} />;
      case 'dress-character':
        return <DressTheCharacter {...gameProps} />;
      case 'country-name-scramble':
        return <CountryNameScramble {...gameProps} />;
      case 'flag-matching':
        return <FlagMatching {...gameProps} />;
      default:
        return <div>Game not found</div>;
    }
  };

  return (
    <Suspense 
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-6">
          <LoadingSkeleton type="game-content" />
        </div>
      }
    >
      {renderGame()}
    </Suspense>
  );
};

export default LazyGameLoader;
