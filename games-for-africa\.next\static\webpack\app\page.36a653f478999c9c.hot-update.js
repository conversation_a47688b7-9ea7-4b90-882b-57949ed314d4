"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setRoundStartTime(Date.now());\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            countryId: currentCountry.id,\n                                            size: \"large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 454,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"cT4d+D4snhqfIe6om8d5KTnT0/g=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ })

});