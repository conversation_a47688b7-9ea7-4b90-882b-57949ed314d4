'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray, getRandomItems } from '@/utils';

interface TimelineBuilderProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface TimelineEvent {
  id: string;
  country: string;
  event: string;
  year: number;
  description: string;
  placed: boolean;
  position: number;
}

const TimelineBuilder: React.FC<TimelineBuilderProps> = ({ countries, onComplete }) => {
  const [events, setEvents] = useState<TimelineEvent[]>([]);
  const [timeline, setTimeline] = useState<(TimelineEvent | null)[]>([]);
  const [draggedEvent, setDraggedEvent] = useState<string | null>(null);
  const [score, setScore] = useState(0);
  const [moves, setMoves] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [showHints, setShowHints] = useState(false);

  const generateEvents = () => {
    const selectedCountries = getRandomItems(countries, 6);
    const timelineEvents: TimelineEvent[] = [];

    selectedCountries.forEach((country, index) => {
      const independenceYear = new Date(country.independence).getFullYear();
      
      // Skip countries with invalid dates
      if (isNaN(independenceYear) || independenceYear < 1800) return;

      timelineEvents.push({
        id: `event-${country.id}`,
        country: country.name,
        event: 'Independence',
        year: independenceYear,
        description: `${country.name} gained independence`,
        placed: false,
        position: -1,
      });
    });

    // Add some additional historical events
    const additionalEvents = [
      {
        id: 'suez-canal',
        country: 'Egypt',
        event: 'Suez Canal Opens',
        year: 1869,
        description: 'The Suez Canal opened, connecting the Mediterranean and Red Seas',
        placed: false,
        position: -1,
      },
      {
        id: 'apartheid-end',
        country: 'South Africa',
        event: 'End of Apartheid',
        year: 1994,
        description: 'Nelson Mandela became the first Black president of South Africa',
        placed: false,
        position: -1,
      },
      {
        id: 'oau-formation',
        country: 'Ethiopia',
        event: 'OAU Formation',
        year: 1963,
        description: 'Organization of African Unity was formed in Addis Ababa',
        placed: false,
        position: -1,
      },
    ];

    const allEvents = [...timelineEvents, ...additionalEvents].slice(0, 8);
    return shuffleArray(allEvents);
  };

  const startGame = () => {
    const newEvents = generateEvents();
    setEvents(newEvents);
    setTimeline(new Array(newEvents.length).fill(null));
    setScore(0);
    setMoves(0);
    setGameComplete(false);
    setGameStarted(true);
    setShowHints(false);
  };

  const handleDragStart = (e: React.DragEvent, eventId: string) => {
    setDraggedEvent(eventId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, position: number) => {
    e.preventDefault();
    
    if (!draggedEvent) return;

    const event = events.find(e => e.id === draggedEvent);
    if (!event || event.placed) return;

    setMoves(moves + 1);

    // Place the event in the timeline
    const newTimeline = [...timeline];
    
    // Remove event from its current position if it was already placed
    const currentPos = newTimeline.findIndex(e => e?.id === draggedEvent);
    if (currentPos !== -1) {
      newTimeline[currentPos] = null;
    }

    // Place event in new position
    newTimeline[position] = event;
    setTimeline(newTimeline);

    // Update event as placed
    setEvents(prev => prev.map(e => 
      e.id === draggedEvent ? { ...e, placed: true, position } : e
    ));

    setDraggedEvent(null);

    // Check if timeline is complete and correct
    if (newTimeline.every(e => e !== null)) {
      checkTimelineCorrectness(newTimeline as TimelineEvent[]);
    }
  };

  const checkTimelineCorrectness = (completedTimeline: TimelineEvent[]) => {
    const sortedEvents = [...completedTimeline].sort((a, b) => a.year - b.year);
    let correctPlacements = 0;

    completedTimeline.forEach((event, index) => {
      if (event.id === sortedEvents[index].id) {
        correctPlacements++;
      }
    });

    const accuracy = (correctPlacements / completedTimeline.length) * 100;
    const finalScore = Math.round(accuracy + Math.max(0, 50 - moves));
    
    setScore(finalScore);
    setGameComplete(true);
    
    setTimeout(() => onComplete(finalScore), 1500);
  };

  const removeFromTimeline = (position: number) => {
    const event = timeline[position];
    if (!event) return;

    const newTimeline = [...timeline];
    newTimeline[position] = null;
    setTimeline(newTimeline);

    setEvents(prev => prev.map(e => 
      e.id === event.id ? { ...e, placed: false, position: -1 } : e
    ));
  };

  const getSortedEvents = () => {
    return [...events].sort((a, b) => a.year - b.year);
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">📚 Timeline Builder</h2>
          <div className="text-6xl mb-6">⏰</div>
          <p className="text-xl text-gray-300 mb-6">
            Arrange historical events from African countries in chronological order!
          </p>
          
          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Drag events to arrange them chronologically</p>
            <p>• Events include independence dates and major milestones</p>
            <p>• Get the correct order for maximum points</p>
            <p>• Use hints if you need help with dates</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Timeline
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">📚 Timeline Builder</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => setShowHints(!showHints)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                showHints ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
            >
              💡 {showHints ? 'Hide' : 'Show'} Hints
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{moves}</div>
            <div className="text-gray-400 text-sm">Moves</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {timeline.filter(e => e !== null).length}/{events.length}
            </div>
            <div className="text-gray-400 text-sm">Placed</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Timeline */}
        <div className="lg:col-span-2">
          <h3 className="text-xl font-semibold text-white mb-4">⏰ Timeline (Earliest → Latest)</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
            <div className="space-y-3">
              {timeline.map((event, index) => (
                <div
                  key={index}
                  className={`min-h-[80px] border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-200 ${
                    event 
                      ? 'bg-blue-500 bg-opacity-20 border-blue-400' 
                      : 'border-gray-600 hover:border-yellow-400'
                  }`}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  {event ? (
                    <div className="flex items-center justify-between w-full p-4">
                      <div className="flex-1">
                        <div className="text-white font-semibold">{event.event}</div>
                        <div className="text-gray-300 text-sm">{event.country}</div>
                        <div className="text-gray-400 text-xs">{event.description}</div>
                        {showHints && (
                          <div className="text-yellow-400 text-sm mt-1">Year: {event.year}</div>
                        )}
                      </div>
                      <button
                        onClick={() => removeFromTimeline(index)}
                        className="text-red-400 hover:text-red-300 ml-4"
                      >
                        ✕
                      </button>
                    </div>
                  ) : (
                    <div className="text-gray-500 text-center">
                      <div className="text-2xl mb-2">📍</div>
                      <div>Drop event here</div>
                      <div className="text-sm">Position {index + 1}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Events Panel */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4">📋 Historical Events</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[600px] overflow-y-auto">
            <div className="space-y-3">
              {events.filter(e => !e.placed).map((event) => (
                <div
                  key={event.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, event.id)}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 ${
                    draggedEvent === event.id ? 'opacity-50' : ''
                  }`}
                >
                  <div className="text-white font-semibold">{event.event}</div>
                  <div className="text-gray-300 text-sm">{event.country}</div>
                  <div className="text-gray-400 text-xs mt-1">{event.description}</div>
                  {showHints && (
                    <div className="text-yellow-400 text-sm mt-2">📅 {event.year}</div>
                  )}
                </div>
              ))}
            </div>
            
            {events.filter(e => !e.placed).length === 0 && (
              <div className="text-center text-gray-400 py-8">
                <div className="text-4xl mb-2">✅</div>
                <div>All events placed!</div>
              </div>
            )}
          </div>

          {/* Hint Panel */}
          {showHints && (
            <div className="mt-4 bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4">
              <h4 className="text-yellow-400 font-semibold mb-2">💡 Correct Order:</h4>
              <div className="space-y-1 text-sm">
                {getSortedEvents().map((event, index) => (
                  <div key={event.id} className="text-gray-300">
                    {index + 1}. {event.event} ({event.year})
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">
                {score >= 80 ? '🏆' : score >= 60 ? '🎉' : '📚'}
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Timeline Complete!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Moves: <span className="text-blue-400">{moves}</span></p>
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}%</span></p>
                  <p className="text-sm">
                    {score >= 80 ? 'Perfect historian!' : 
                     score >= 60 ? 'Great job!' : 
                     'Keep studying history!'}
                  </p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimelineBuilder;
