// Optimized data loading with caching
import { Country } from '@/types';

// In-memory cache for countries data
let countriesCache: Country[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Preload countries data with caching
export const loadCountriesData = async (): Promise<Country[]> => {
  // Check if we have valid cached data
  const now = Date.now();
  if (countriesCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return countriesCache;
  }

  try {
    // Use dynamic import for better code splitting
    const { default: countriesData } = await import('@/data/countries.json');

    // Extract the countries array from the data structure
    const countriesArray = countriesData.countries || [];

    // Validate and process the data
    const processedCountries = countriesArray.map((country: any) => ({
      ...country,
      // Ensure all required fields are present
      population: country.population || 0,
      exports: country.exports || [],
      landmarks: country.landmarks || [],
      wildlife: country.wildlife || [],
      culturalElements: country.culturalElements || {
        traditionalClothing: [],
        cuisine: [],
        music: [],
        dances: []
      },
      notableFigures: country.notableFigures || []
    }));

    // Update cache
    countriesCache = processedCountries;
    cacheTimestamp = now;

    return processedCountries;
  } catch (error) {
    console.error('Failed to load countries data:', error);
    
    // Return empty array as fallback
    return [];
  }
};

// Preload specific country data by ID
export const getCountryById = async (id: string): Promise<Country | null> => {
  const countries = await loadCountriesData();
  return countries.find(country => country.id === id) || null;
};

// Preload countries by region with caching
const regionCache: Record<string, Country[]> = {};

export const getCountriesByRegion = async (region: string): Promise<Country[]> => {
  // Check region cache first
  if (regionCache[region]) {
    return regionCache[region];
  }

  const countries = await loadCountriesData();
  const filteredCountries = countries.filter(country => country.region === region);
  
  // Cache the result
  regionCache[region] = filteredCountries;
  
  return filteredCountries;
};

// Preload and cache flag images
const flagImageCache = new Map<string, string>();

export const preloadFlagImage = (countryId: string, flagUrl: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if already cached
    if (flagImageCache.has(countryId)) {
      resolve();
      return;
    }

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      flagImageCache.set(countryId, flagUrl);
      resolve();
    };
    
    img.onerror = () => {
      reject(new Error(`Failed to load flag for ${countryId}`));
    };
    
    img.src = flagUrl;
  });
};

// Batch preload flag images for better performance
export const preloadFlagImages = async (countries: Country[]): Promise<void> => {
  const preloadPromises = countries.map(country => 
    preloadFlagImage(country.id, country.flagUrl).catch(() => {
      // Silently fail for individual flags to not block the entire batch
      console.warn(`Failed to preload flag for ${country.name}`);
    })
  );

  try {
    await Promise.allSettled(preloadPromises);
  } catch (error) {
    console.warn('Some flag images failed to preload:', error);
  }
};

// Initialize data loading on app start
export const initializeDataLoading = async (): Promise<void> => {
  try {
    // Start loading countries data
    const countriesPromise = loadCountriesData();
    
    // Wait for countries to load, then preload flag images
    const countries = await countriesPromise;
    
    // Preload flag images in the background (don't wait)
    preloadFlagImages(countries.slice(0, 20)).catch(() => {
      // Silently handle errors
    });
    
  } catch (error) {
    console.error('Failed to initialize data loading:', error);
  }
};

// Clear cache (useful for development or when data updates)
export const clearCache = (): void => {
  countriesCache = null;
  cacheTimestamp = 0;
  Object.keys(regionCache).forEach(key => delete regionCache[key]);
  flagImageCache.clear();
};

// Get cache statistics for debugging
export const getCacheStats = () => {
  return {
    countriesCache: {
      loaded: !!countriesCache,
      count: countriesCache?.length || 0,
      age: Date.now() - cacheTimestamp
    },
    regionCache: {
      regions: Object.keys(regionCache).length,
      totalCountries: Object.values(regionCache).reduce((sum, countries) => sum + countries.length, 0)
    },
    flagImageCache: {
      count: flagImageCache.size
    }
  };
};
