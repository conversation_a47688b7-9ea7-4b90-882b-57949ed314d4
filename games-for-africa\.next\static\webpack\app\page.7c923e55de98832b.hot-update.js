"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useProgressStore */ \"(app-pages-browser)/./src/stores/useProgressStore.ts\");\n/* harmony import */ var _components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserDashboard */ \"(app-pages-browser)/./src/components/UserDashboard.tsx\");\n/* harmony import */ var _components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CelebrationModal */ \"(app-pages-browser)/./src/components/CelebrationModal.tsx\");\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/games/MemoryGrid */ \"(app-pages-browser)/./src/components/games/MemoryGrid.tsx\");\n/* harmony import */ var _components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/games/JigsawPuzzle */ \"(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\");\n/* harmony import */ var _components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/games/TimelineBuilder */ \"(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\");\n/* harmony import */ var _components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/games/WhereInAfrica */ \"(app-pages-browser)/./src/components/games/WhereInAfrica.tsx\");\n/* harmony import */ var _components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/games/DressTheCharacter */ \"(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [celebrationData, setCelebrationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: 'gameComplete',\n        data: {}\n    });\n    const { profile, loadProfile, recordGameCompletion } = (0,_stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsClient(true);\n            loadProfile();\n        }\n    }[\"Home.useEffect\"], [\n        loadProfile\n    ]);\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_15__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = async (score)=>{\n        var _gameConfigs_find;\n        if (!currentGame) return;\n        const gameTitle = ((_gameConfigs_find = gameConfigs.find((g)=>g.type === currentGame)) === null || _gameConfigs_find === void 0 ? void 0 : _gameConfigs_find.name) || currentGame;\n        const completionTime = 300; // This should be passed from the game component\n        const difficulty = (profile === null || profile === void 0 ? void 0 : profile.preferences.difficulty) || 'intermediate';\n        const isPerfectScore = score >= 100; // This should be determined by the game\n        try {\n            const result = await recordGameCompletion(currentGame, score, completionTime, difficulty, isPerfectScore);\n            // Show game completion celebration\n            setCelebrationData({\n                isOpen: true,\n                type: 'gameComplete',\n                data: {\n                    score,\n                    gameTitle,\n                    xpGained: result.xpGained\n                }\n            });\n            // Show level up celebration if applicable\n            if (result.levelUp) {\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'levelUp',\n                        data: {\n                            newLevel: profile === null || profile === void 0 ? void 0 : profile.level,\n                            xpGained: result.xpGained\n                        }\n                    });\n                }, 2000);\n            }\n            // Show achievement celebrations\n            result.newAchievements.forEach((achievement, index)=>{\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'achievement',\n                        data: {\n                            achievement\n                        }\n                    });\n                }, 3000 + index * 2000);\n            });\n        } catch (error) {\n            console.error('Error recording game completion:', error);\n        }\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 16\n                }, this);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 16\n                }, this);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 16\n                }, this);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 16\n                }, this);\n            case 'where-in-africa':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 16\n                }, this);\n            case 'dress-character':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl text-accent-gold\",\n                        children: \"Loading Games for Africa...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-gold font-medium\",\n                                                    children: profile.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"Level \",\n                                                        profile.level,\n                                                        \" • \",\n                                                        profile.totalXP,\n                                                        \" XP\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowDashboard(true),\n                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-gray-900 font-bold hover:scale-105 transition-transform\",\n                                            children: profile.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>{\n                                const gameProgress = profile === null || profile === void 0 ? void 0 : profile.gamesProgress[game.type];\n                                const isCompleted = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.completed) || false;\n                                const bestScore = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.bestScore) || 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer relative \".concat(isCompleted ? 'border-green-400' : 'border-gray-700'),\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: [\n                                        isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl\",\n                                                            children: game.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"⏱️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        game.estimatedTime,\n                                                                        \"min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                    children: game.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4 flex-grow\",\n                                                    children: game.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                            children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                bestScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-400 text-sm font-medium\",\n                                                                    children: [\n                                                                        \"Best: \",\n                                                                        bestScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: [\n                                                                        \"Max: \",\n                                                                        game.maxScore,\n                                                                        \" pts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                    children: \"▶️ Play Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, game.type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            showDashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onClose: ()=>setShowDashboard(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: celebrationData.isOpen,\n                onClose: ()=>setCelebrationData((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        })),\n                type: celebrationData.type,\n                data: celebrationData.data\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 7\n    }, this);\n}\n_s(Home, \"F/q13Zo1gnuX1bo7PfVxtyEnecY=\", false, function() {\n    return [\n        _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});