'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray } from '@/utils';

interface JigsawPuzzleProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface PuzzlePiece {
  id: string;
  countryId: string;
  countryName: string;
  flag: string;
  region: string;
  position: { x: number; y: number };
  placed: boolean;
  correctPosition: { x: number; y: number };
}

const JigsawPuzzle: React.FC<JigsawPuzzleProps> = ({ countries, onComplete }) => {
  const [pieces, setPieces] = useState<PuzzlePiece[]>([]);
  const [draggedPiece, setDraggedPiece] = useState<string | null>(null);
  const [placedPieces, setPlacedPieces] = useState<string[]>([]);
  const [score, setScore] = useState(0);
  const [moves, setMoves] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [selectedRegion, setSelectedRegion] = useState<string>('All');

  const regions = ['All', 'North Africa', 'West Africa', 'East Africa', 'Central Africa', 'Southern Africa'];

  // Define the exact country counts per region
  const regionCounts = {
    'North Africa': 6,
    'West Africa': 16,
    'East Africa': 10,
    'Central Africa': 8,
    'Southern Africa': 10
  };

  useEffect(() => {
    if (gameStarted && !gameComplete) {
      const timer = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [gameStarted, gameComplete]);

  const generatePuzzle = () => {
    let filteredCountries: Country[] = [];
    let gridSize = { cols: 6, rows: 5 }; // Default grid size

    if (selectedRegion === 'All') {
      // Complete Africa mode - select representative countries from each region
      const northAfrica = countries.filter(c => c.region === 'North Africa').slice(0, 3);
      const westAfrica = countries.filter(c => c.region === 'West Africa').slice(0, 6);
      const eastAfrica = countries.filter(c => c.region === 'East Africa').slice(0, 4);
      const centralAfrica = countries.filter(c => c.region === 'Central Africa').slice(0, 3);
      const southernAfrica = countries.filter(c => c.region === 'Southern Africa').slice(0, 4);

      filteredCountries = [...northAfrica, ...westAfrica, ...eastAfrica, ...centralAfrica, ...southernAfrica];
      gridSize = { cols: 8, rows: 4 }; // Larger grid for complete Africa
    } else {
      // Region-specific mode - include all countries from the selected region
      filteredCountries = countries.filter(c => c.region === selectedRegion);

      // Adjust grid size based on region
      switch (selectedRegion) {
        case 'North Africa':
          gridSize = { cols: 3, rows: 2 }; // 6 countries
          break;
        case 'West Africa':
          gridSize = { cols: 4, rows: 4 }; // 16 countries
          break;
        case 'East Africa':
          gridSize = { cols: 5, rows: 2 }; // 10 countries
          break;
        case 'Central Africa':
          gridSize = { cols: 4, rows: 2 }; // 8 countries
          break;
        case 'Southern Africa':
          gridSize = { cols: 5, rows: 2 }; // 10 countries
          break;
      }
    }

    // Generate geographical positions based on actual African geography
    const regionPositions: Record<string, { x: number; y: number }[]> = {
      'North Africa': [
        { x: 0, y: 0 }, // Morocco
        { x: 1, y: 0 }, // Algeria
        { x: 2, y: 0 }, // Tunisia
        { x: 3, y: 0 }, // Libya
        { x: 4, y: 0 }, // Egypt
        { x: 2, y: 1 }, // Sudan
      ],
      'West Africa': [
        { x: 0, y: 0 }, // Mauritania
        { x: 1, y: 0 }, // Mali
        { x: 2, y: 0 }, // Niger
        { x: 0, y: 1 }, // Senegal
        { x: 1, y: 1 }, // Burkina Faso
        { x: 2, y: 1 }, // Nigeria
        { x: 0, y: 2 }, // Guinea-Bissau
        { x: 1, y: 2 }, // Guinea
        { x: 2, y: 2 }, // Ivory Coast
        { x: 3, y: 2 }, // Ghana
        { x: 0, y: 3 }, // Sierra Leone
        { x: 1, y: 3 }, // Liberia
        { x: 2, y: 3 }, // Togo
        { x: 3, y: 3 }, // Benin
        { x: 4, y: 0 }, // Cape Verde (off coast)
        { x: 4, y: 1 }, // Gambia
      ],
      'East Africa': [
        { x: 0, y: 0 }, // Ethiopia
        { x: 1, y: 0 }, // Eritrea
        { x: 2, y: 0 }, // Djibouti
        { x: 3, y: 0 }, // Somalia
        { x: 0, y: 1 }, // South Sudan
        { x: 1, y: 1 }, // Uganda
        { x: 2, y: 1 }, // Kenya
        { x: 3, y: 1 }, // Tanzania
        { x: 4, y: 1 }, // Rwanda
        { x: 4, y: 0 }, // Burundi
      ],
      'Central Africa': [
        { x: 0, y: 0 }, // Chad
        { x: 1, y: 0 }, // Central African Republic
        { x: 2, y: 0 }, // Cameroon
        { x: 0, y: 1 }, // Democratic Republic of Congo
        { x: 1, y: 1 }, // Republic of Congo
        { x: 2, y: 1 }, // Gabon
        { x: 3, y: 1 }, // Equatorial Guinea
        { x: 3, y: 0 }, // São Tomé and Príncipe
      ],
      'Southern Africa': [
        { x: 0, y: 0 }, // Angola
        { x: 1, y: 0 }, // Zambia
        { x: 2, y: 0 }, // Malawi
        { x: 3, y: 0 }, // Mozambique
        { x: 0, y: 1 }, // Namibia
        { x: 1, y: 1 }, // Botswana
        { x: 2, y: 1 }, // Zimbabwe
        { x: 3, y: 1 }, // South Africa
        { x: 4, y: 1 }, // Lesotho
        { x: 4, y: 0 }, // Eswatini
      ]
    };

    const puzzlePieces: PuzzlePiece[] = [];

    filteredCountries.forEach((country, index) => {
      let correctPos: { x: number; y: number };

      if (selectedRegion === 'All') {
        // For complete Africa mode, arrange by regions
        if (country.region === 'North Africa') {
          correctPos = { x: index % 8, y: 0 };
        } else if (country.region === 'West Africa') {
          const westIndex = index - 3; // Offset by North Africa countries
          correctPos = { x: westIndex % 8, y: 1 };
        } else if (country.region === 'East Africa') {
          const eastIndex = index - 9; // Offset by North + West Africa countries
          correctPos = { x: eastIndex % 8, y: 2 };
        } else if (country.region === 'Central Africa') {
          const centralIndex = index - 13; // Offset by previous regions
          correctPos = { x: centralIndex % 8, y: 3 };
        } else {
          const southIndex = index - 16; // Offset by previous regions
          correctPos = { x: southIndex % 8, y: 3 };
        }
      } else {
        // For region-specific mode, use geographical positions
        const regionPositions_array = regionPositions[country.region] || [];
        correctPos = regionPositions_array[index] || { x: index % gridSize.cols, y: Math.floor(index / gridSize.cols) };
      }

      puzzlePieces.push({
        id: `piece-${country.id}`,
        countryId: country.id,
        countryName: country.name,
        flag: country.flagUrl,
        region: country.region,
        position: { x: -1, y: -1 }, // Start unplaced
        placed: false,
        correctPosition: correctPos,
      });
    });

    return shuffleArray(puzzlePieces);
  };

  const startGame = () => {
    const newPieces = generatePuzzle();
    setPieces(newPieces);
    setPlacedPieces([]);
    setScore(0);
    setMoves(0);
    setTimeElapsed(0);
    setGameComplete(false);
    setGameStarted(true);
  };

  const handleDragStart = (e: React.DragEvent, pieceId: string) => {
    setDraggedPiece(pieceId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetX: number, targetY: number) => {
    e.preventDefault();
    
    if (!draggedPiece) return;

    const piece = pieces.find(p => p.id === draggedPiece);
    if (!piece) return;

    setMoves(moves + 1);

    // Check if position is correct
    const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;
    
    if (isCorrect) {
      // Correct placement
      setPieces(prev => prev.map(p => 
        p.id === draggedPiece 
          ? { ...p, position: { x: targetX, y: targetY }, placed: true }
          : p
      ));
      setPlacedPieces(prev => [...prev, draggedPiece]);
      setScore(score + 10);

      // Check if puzzle is complete
      if (placedPieces.length + 1 === pieces.length) {
        setGameComplete(true);
        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));
        const moveBonus = Math.max(0, 50 - moves);
        const finalScore = score + 10 + timeBonus + moveBonus;
        setTimeout(() => onComplete(finalScore), 1000);
      }
    } else {
      // Incorrect placement - piece bounces back
      setTimeout(() => {
        setPieces(prev => prev.map(p => 
          p.id === draggedPiece 
            ? { ...p, position: { x: -1, y: -1 }, placed: false }
            : p
        ));
      }, 500);
    }

    setDraggedPiece(null);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getGridPosition = (x: number, y: number) => {
    return pieces.find(p => p.position.x === x && p.position.y === y && p.placed);
  };

  const getGridSize = () => {
    if (selectedRegion === 'All') {
      return { cols: 8, rows: 4 };
    }

    switch (selectedRegion) {
      case 'North Africa':
        return { cols: 3, rows: 2 };
      case 'West Africa':
        return { cols: 4, rows: 4 };
      case 'East Africa':
        return { cols: 5, rows: 2 };
      case 'Central Africa':
        return { cols: 4, rows: 2 };
      case 'Southern Africa':
        return { cols: 5, rows: 2 };
      default:
        return { cols: 6, rows: 5 };
    }
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🧩 Jigsaw Puzzle Map</h2>
          <div className="text-6xl mb-6">🗺️</div>
          <p className="text-xl text-gray-300 mb-6">
            Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.
          </p>

          {/* Region Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Choose Region:</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto">
              {regions.map((region) => {
                const getRegionInfo = (region: string) => {
                  if (region === 'All') {
                    return { count: 50, description: 'Complete Africa' };
                  }
                  const count = regionCounts[region as keyof typeof regionCounts] || 0;
                  return { count, description: `${count} countries` };
                };

                const { count, description } = getRegionInfo(region);

                return (
                  <button
                    key={region}
                    onClick={() => setSelectedRegion(region)}
                    className={`p-4 rounded-lg transition-all duration-200 border-2 ${
                      selectedRegion === region
                        ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105'
                        : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <div className="text-center">
                      <div className="font-semibold text-lg">{region}</div>
                      <div className={`text-sm ${selectedRegion === region ? 'text-gray-700' : 'text-gray-400'}`}>
                        {description}
                      </div>
                      {region !== 'All' && (
                        <div className={`text-xs mt-1 ${selectedRegion === region ? 'text-gray-600' : 'text-gray-500'}`}>
                          Difficulty: {count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'}
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          <div className="space-y-4 text-gray-300 mb-8">
            <p>• <strong>Complete Africa:</strong> 50 countries across all regions</p>
            <p>• <strong>Regional Focus:</strong> Master specific geographical areas</p>
            <p>• <strong>North Africa:</strong> 6 countries (Easy)</p>
            <p>• <strong>East/Central/Southern Africa:</strong> 8-10 countries (Medium)</p>
            <p>• <strong>West Africa:</strong> 16 countries (Hard)</p>
            <p>• Drag pieces to their correct geographical positions</p>
            <p>• Learn about African geography and country locations</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Puzzle
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🧩 Jigsaw Puzzle Map</h2>
          <div className="text-yellow-400 text-xl font-bold">
            {formatTime(timeElapsed)}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{moves}</div>
            <div className="text-gray-400 text-sm">Moves</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{placedPieces.length}</div>
            <div className="text-gray-400 text-sm">Placed</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{pieces.length}</div>
            <div className="text-gray-400 text-sm">Total</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Puzzle Grid */}
        <div className="lg:col-span-2">
          <h3 className="text-xl font-semibold text-white mb-4">
            🗺️ {selectedRegion === 'All' ? 'Complete Africa Map' : `${selectedRegion} Map`}
          </h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
            <div
              className={`grid gap-2 min-h-[400px]`}
              style={{
                gridTemplateColumns: `repeat(${getGridSize().cols}, minmax(0, 1fr))`,
                gridTemplateRows: `repeat(${getGridSize().rows}, minmax(0, 1fr))`
              }}
            >
              {Array.from({ length: getGridSize().cols * getGridSize().rows }, (_, index) => {
                const x = index % getGridSize().cols;
                const y = Math.floor(index / getGridSize().cols);
                const placedPiece = getGridPosition(x, y);

                return (
                  <div
                    key={`${x}-${y}`}
                    className={`aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 ${
                      placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, x, y)}
                  >
                    {placedPiece && (
                      <div className="text-center">
                        <div className="text-2xl mb-1">{placedPiece.flag}</div>
                        <div className="text-xs text-white font-medium">
                          {placedPiece.countryName}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Region Legend */}
            <div className="mt-4 text-center">
              <div className="text-gray-400 text-sm">
                {selectedRegion === 'All'
                  ? 'Drag countries to their geographical regions'
                  : `Place all ${regionCounts[selectedRegion as keyof typeof regionCounts] || 0} countries in ${selectedRegion}`
                }
              </div>
            </div>
          </div>
        </div>

        {/* Pieces Panel */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4">🧩 Puzzle Pieces</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto">
            <div className="space-y-2">
              {pieces.filter(p => !p.placed).map((piece) => (
                <div
                  key={piece.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, piece.id)}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 ${
                    draggedPiece === piece.id ? 'opacity-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{piece.flag}</div>
                    <div>
                      <div className="text-white font-medium">{piece.countryName}</div>
                      <div className="text-gray-400 text-xs">{piece.region}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {pieces.filter(p => !p.placed).length === 0 && (
              <div className="text-center text-gray-400 py-8">
                <div className="text-4xl mb-2">🎉</div>
                <div>All pieces placed!</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">🏆</div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Puzzle Master!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Time: <span className="text-blue-400">{formatTime(timeElapsed)}</span></p>
                  <p>Moves: <span className="text-purple-400">{moves}</span></p>
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JigsawPuzzle;
