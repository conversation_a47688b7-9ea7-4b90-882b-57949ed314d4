'use client';

import React, { useState, useEffect } from 'react';
import { Country } from '@/types';
import { shuffleArray } from '@/utils';

interface JigsawPuzzleProps {
  countries: Country[];
  onComplete: (score: number) => void;
}

interface PuzzlePiece {
  id: string;
  countryId: string;
  countryName: string;
  flag: string;
  region: string;
  position: { x: number; y: number };
  placed: boolean;
  correctPosition: { x: number; y: number };
}

const JigsawPuzzle: React.FC<JigsawPuzzleProps> = ({ countries, onComplete }) => {
  const [pieces, setPieces] = useState<PuzzlePiece[]>([]);
  const [draggedPiece, setDraggedPiece] = useState<string | null>(null);
  const [placedPieces, setPlacedPieces] = useState<string[]>([]);
  const [score, setScore] = useState(0);
  const [moves, setMoves] = useState(0);
  const [gameComplete, setGameComplete] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [selectedRegion, setSelectedRegion] = useState<string>('All');

  const regions = ['All', 'North Africa', 'West Africa', 'East Africa', 'Central Africa', 'Southern Africa'];

  useEffect(() => {
    if (gameStarted && !gameComplete) {
      const timer = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [gameStarted, gameComplete]);

  const generatePuzzle = () => {
    const filteredCountries = selectedRegion === 'All' 
      ? countries.slice(0, 12) // Limit to 12 countries for manageable puzzle
      : countries.filter(c => c.region === selectedRegion).slice(0, 8);

    const regionPositions: Record<string, { x: number; y: number }[]> = {
      'North Africa': [
        { x: 1, y: 0 }, { x: 2, y: 0 }, { x: 3, y: 0 }, { x: 4, y: 0 }
      ],
      'West Africa': [
        { x: 0, y: 1 }, { x: 1, y: 1 }, { x: 0, y: 2 }, { x: 1, y: 2 }
      ],
      'East Africa': [
        { x: 3, y: 1 }, { x: 4, y: 1 }, { x: 3, y: 2 }, { x: 4, y: 2 }
      ],
      'Central Africa': [
        { x: 2, y: 1 }, { x: 2, y: 2 }
      ],
      'Southern Africa': [
        { x: 1, y: 3 }, { x: 2, y: 3 }, { x: 3, y: 3 }
      ]
    };

    const puzzlePieces: PuzzlePiece[] = [];
    
    filteredCountries.forEach((country, index) => {
      const regionPositions_array = regionPositions[country.region] || [];
      const correctPos = regionPositions_array[index % regionPositions_array.length] || { x: index % 4, y: Math.floor(index / 4) };
      
      puzzlePieces.push({
        id: `piece-${country.id}`,
        countryId: country.id,
        countryName: country.name,
        flag: country.flagUrl,
        region: country.region,
        position: { x: -1, y: -1 }, // Start unplaced
        placed: false,
        correctPosition: correctPos,
      });
    });

    return shuffleArray(puzzlePieces);
  };

  const startGame = () => {
    const newPieces = generatePuzzle();
    setPieces(newPieces);
    setPlacedPieces([]);
    setScore(0);
    setMoves(0);
    setTimeElapsed(0);
    setGameComplete(false);
    setGameStarted(true);
  };

  const handleDragStart = (e: React.DragEvent, pieceId: string) => {
    setDraggedPiece(pieceId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetX: number, targetY: number) => {
    e.preventDefault();
    
    if (!draggedPiece) return;

    const piece = pieces.find(p => p.id === draggedPiece);
    if (!piece) return;

    setMoves(moves + 1);

    // Check if position is correct
    const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;
    
    if (isCorrect) {
      // Correct placement
      setPieces(prev => prev.map(p => 
        p.id === draggedPiece 
          ? { ...p, position: { x: targetX, y: targetY }, placed: true }
          : p
      ));
      setPlacedPieces(prev => [...prev, draggedPiece]);
      setScore(score + 10);

      // Check if puzzle is complete
      if (placedPieces.length + 1 === pieces.length) {
        setGameComplete(true);
        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));
        const moveBonus = Math.max(0, 50 - moves);
        const finalScore = score + 10 + timeBonus + moveBonus;
        setTimeout(() => onComplete(finalScore), 1000);
      }
    } else {
      // Incorrect placement - piece bounces back
      setTimeout(() => {
        setPieces(prev => prev.map(p => 
          p.id === draggedPiece 
            ? { ...p, position: { x: -1, y: -1 }, placed: false }
            : p
        ));
      }, 500);
    }

    setDraggedPiece(null);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getGridPosition = (x: number, y: number) => {
    return pieces.find(p => p.position.x === x && p.position.y === y && p.placed);
  };

  if (!gameStarted) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-white mb-6">🧩 Jigsaw Puzzle Map</h2>
          <div className="text-6xl mb-6">🗺️</div>
          <p className="text-xl text-gray-300 mb-6">
            Drag and drop African countries to their correct positions on the map!
          </p>
          
          {/* Region Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Choose Region:</h3>
            <div className="flex flex-wrap justify-center gap-2">
              {regions.map((region) => (
                <button
                  key={region}
                  onClick={() => setSelectedRegion(region)}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedRegion === region
                      ? 'bg-yellow-400 text-gray-900'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  {region}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-4 text-gray-300 mb-8">
            <p>• Drag country pieces to their correct positions</p>
            <p>• Countries are grouped by African regions</p>
            <p>• Complete the map as quickly as possible</p>
            <p>• Fewer moves = higher score!</p>
          </div>
          
          <button
            onClick={startGame}
            className="bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors"
          >
            🚀 Start Puzzle
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-white">🧩 Jigsaw Puzzle Map</h2>
          <div className="text-yellow-400 text-xl font-bold">
            {formatTime(timeElapsed)}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">{score}</div>
            <div className="text-gray-400 text-sm">Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{moves}</div>
            <div className="text-gray-400 text-sm">Moves</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{placedPieces.length}</div>
            <div className="text-gray-400 text-sm">Placed</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">{pieces.length}</div>
            <div className="text-gray-400 text-sm">Total</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Puzzle Grid */}
        <div className="lg:col-span-2">
          <h3 className="text-xl font-semibold text-white mb-4">🗺️ Africa Map</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
            <div className="grid grid-cols-5 gap-2 min-h-[400px]">
              {Array.from({ length: 20 }, (_, index) => {
                const x = index % 5;
                const y = Math.floor(index / 5);
                const placedPiece = getGridPosition(x, y);
                
                return (
                  <div
                    key={`${x}-${y}`}
                    className={`aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 ${
                      placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, x, y)}
                  >
                    {placedPiece && (
                      <div className="text-center">
                        <div className="text-2xl mb-1">{placedPiece.flag}</div>
                        <div className="text-xs text-white font-medium">
                          {placedPiece.countryName}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Pieces Panel */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4">🧩 Puzzle Pieces</h3>
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto">
            <div className="space-y-2">
              {pieces.filter(p => !p.placed).map((piece) => (
                <div
                  key={piece.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, piece.id)}
                  className={`bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 ${
                    draggedPiece === piece.id ? 'opacity-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{piece.flag}</div>
                    <div>
                      <div className="text-white font-medium">{piece.countryName}</div>
                      <div className="text-gray-400 text-xs">{piece.region}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {pieces.filter(p => !p.placed).length === 0 && (
              <div className="text-center text-gray-400 py-8">
                <div className="text-4xl mb-2">🎉</div>
                <div>All pieces placed!</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Game Complete Modal */}
      {gameComplete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700">
            <div className="text-center space-y-6">
              <div className="text-6xl">🏆</div>
              
              <div>
                <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                  Puzzle Master!
                </h3>
                <div className="space-y-2 text-gray-300">
                  <p>Time: <span className="text-blue-400">{formatTime(timeElapsed)}</span></p>
                  <p>Moves: <span className="text-purple-400">{moves}</span></p>
                  <p>Final Score: <span className="text-yellow-400 font-bold">{score}</span></p>
                </div>
              </div>

              <div className="flex space-x-4 justify-center">
                <button 
                  onClick={startGame}
                  className="bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors"
                >
                  🔄 Play Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JigsawPuzzle;
