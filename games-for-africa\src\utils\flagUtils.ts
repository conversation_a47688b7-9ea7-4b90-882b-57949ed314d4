// Flag image utility functions and mappings
// Using flagcdn.com for consistent, high-quality flag images

export interface FlagImageData {
  svg: string;
  png: string;
  emoji: string;
  alt: string;
}

// Country code to flag mapping for African countries
export const AFRICAN_COUNTRY_FLAGS: Record<string, FlagImageData> = {
  // North Africa
  'egypt': {
    svg: 'https://flagcdn.com/eg.svg',
    png: 'https://flagcdn.com/w320/eg.png',
    emoji: '🇪🇬',
    alt: 'Flag of Egypt'
  },
  'libya': {
    svg: 'https://flagcdn.com/ly.svg',
    png: 'https://flagcdn.com/w320/ly.png',
    emoji: '🇱🇾',
    alt: 'Flag of Libya'
  },
  'tunisia': {
    svg: 'https://flagcdn.com/tn.svg',
    png: 'https://flagcdn.com/w320/tn.png',
    emoji: '🇹🇳',
    alt: 'Flag of Tunisia'
  },
  'algeria': {
    svg: 'https://flagcdn.com/dz.svg',
    png: 'https://flagcdn.com/w320/dz.png',
    emoji: '🇩🇿',
    alt: 'Flag of Algeria'
  },
  'morocco': {
    svg: 'https://flagcdn.com/ma.svg',
    png: 'https://flagcdn.com/w320/ma.png',
    emoji: '🇲🇦',
    alt: 'Flag of Morocco'
  },
  'sudan': {
    svg: 'https://flagcdn.com/sd.svg',
    png: 'https://flagcdn.com/w320/sd.png',
    emoji: '🇸🇩',
    alt: 'Flag of Sudan'
  },

  // West Africa
  'nigeria': {
    svg: 'https://flagcdn.com/ng.svg',
    png: 'https://flagcdn.com/w320/ng.png',
    emoji: '🇳🇬',
    alt: 'Flag of Nigeria'
  },
  'ghana': {
    svg: 'https://flagcdn.com/gh.svg',
    png: 'https://flagcdn.com/w320/gh.png',
    emoji: '🇬🇭',
    alt: 'Flag of Ghana'
  },
  'senegal': {
    svg: 'https://flagcdn.com/sn.svg',
    png: 'https://flagcdn.com/w320/sn.png',
    emoji: '🇸🇳',
    alt: 'Flag of Senegal'
  },
  'mali': {
    svg: 'https://flagcdn.com/ml.svg',
    png: 'https://flagcdn.com/w320/ml.png',
    emoji: '🇲🇱',
    alt: 'Flag of Mali'
  },
  'burkina-faso': {
    svg: 'https://flagcdn.com/bf.svg',
    png: 'https://flagcdn.com/w320/bf.png',
    emoji: '🇧🇫',
    alt: 'Flag of Burkina Faso'
  },
  'niger': {
    svg: 'https://flagcdn.com/ne.svg',
    png: 'https://flagcdn.com/w320/ne.png',
    emoji: '🇳🇪',
    alt: 'Flag of Niger'
  },
  'guinea': {
    svg: 'https://flagcdn.com/gn.svg',
    png: 'https://flagcdn.com/w320/gn.png',
    emoji: '🇬🇳',
    alt: 'Flag of Guinea'
  },
  'sierra-leone': {
    svg: 'https://flagcdn.com/sl.svg',
    png: 'https://flagcdn.com/w320/sl.png',
    emoji: '🇸🇱',
    alt: 'Flag of Sierra Leone'
  },
  'liberia': {
    svg: 'https://flagcdn.com/lr.svg',
    png: 'https://flagcdn.com/w320/lr.png',
    emoji: '🇱🇷',
    alt: 'Flag of Liberia'
  },
  'ivory-coast': {
    svg: 'https://flagcdn.com/ci.svg',
    png: 'https://flagcdn.com/w320/ci.png',
    emoji: '🇨🇮',
    alt: 'Flag of Ivory Coast'
  },
  'gambia': {
    svg: 'https://flagcdn.com/gm.svg',
    png: 'https://flagcdn.com/w320/gm.png',
    emoji: '🇬🇲',
    alt: 'Flag of Gambia'
  },
  'guinea-bissau': {
    svg: 'https://flagcdn.com/gw.svg',
    png: 'https://flagcdn.com/w320/gw.png',
    emoji: '🇬🇼',
    alt: 'Flag of Guinea-Bissau'
  },
  'cape-verde': {
    svg: 'https://flagcdn.com/cv.svg',
    png: 'https://flagcdn.com/w320/cv.png',
    emoji: '🇨🇻',
    alt: 'Flag of Cape Verde'
  },
  'mauritania': {
    svg: 'https://flagcdn.com/mr.svg',
    png: 'https://flagcdn.com/w320/mr.png',
    emoji: '🇲🇷',
    alt: 'Flag of Mauritania'
  },
  'benin': {
    svg: 'https://flagcdn.com/bj.svg',
    png: 'https://flagcdn.com/w320/bj.png',
    emoji: '🇧🇯',
    alt: 'Flag of Benin'
  },
  'togo': {
    svg: 'https://flagcdn.com/tg.svg',
    png: 'https://flagcdn.com/w320/tg.png',
    emoji: '🇹🇬',
    alt: 'Flag of Togo'
  },

  // East Africa
  'kenya': {
    svg: 'https://flagcdn.com/ke.svg',
    png: 'https://flagcdn.com/w320/ke.png',
    emoji: '🇰🇪',
    alt: 'Flag of Kenya'
  },
  'ethiopia': {
    svg: 'https://flagcdn.com/et.svg',
    png: 'https://flagcdn.com/w320/et.png',
    emoji: '🇪🇹',
    alt: 'Flag of Ethiopia'
  },
  'tanzania': {
    svg: 'https://flagcdn.com/tz.svg',
    png: 'https://flagcdn.com/w320/tz.png',
    emoji: '🇹🇿',
    alt: 'Flag of Tanzania'
  },
  'uganda': {
    svg: 'https://flagcdn.com/ug.svg',
    png: 'https://flagcdn.com/w320/ug.png',
    emoji: '🇺🇬',
    alt: 'Flag of Uganda'
  },
  'rwanda': {
    svg: 'https://flagcdn.com/rw.svg',
    png: 'https://flagcdn.com/w320/rw.png',
    emoji: '🇷🇼',
    alt: 'Flag of Rwanda'
  },
  'burundi': {
    svg: 'https://flagcdn.com/bi.svg',
    png: 'https://flagcdn.com/w320/bi.png',
    emoji: '🇧🇮',
    alt: 'Flag of Burundi'
  },
  'somalia': {
    svg: 'https://flagcdn.com/so.svg',
    png: 'https://flagcdn.com/w320/so.png',
    emoji: '🇸🇴',
    alt: 'Flag of Somalia'
  },
  'eritrea': {
    svg: 'https://flagcdn.com/er.svg',
    png: 'https://flagcdn.com/w320/er.png',
    emoji: '🇪🇷',
    alt: 'Flag of Eritrea'
  },
  'djibouti': {
    svg: 'https://flagcdn.com/dj.svg',
    png: 'https://flagcdn.com/w320/dj.png',
    emoji: '🇩🇯',
    alt: 'Flag of Djibouti'
  },
  'south-sudan': {
    svg: 'https://flagcdn.com/ss.svg',
    png: 'https://flagcdn.com/w320/ss.png',
    emoji: '🇸🇸',
    alt: 'Flag of South Sudan'
  },

  // Central Africa
  'democratic-republic-congo': {
    svg: 'https://flagcdn.com/cd.svg',
    png: 'https://flagcdn.com/w320/cd.png',
    emoji: '🇨🇩',
    alt: 'Flag of Democratic Republic of Congo'
  },
  'central-african-republic': {
    svg: 'https://flagcdn.com/cf.svg',
    png: 'https://flagcdn.com/w320/cf.png',
    emoji: '🇨🇫',
    alt: 'Flag of Central African Republic'
  },
  'chad': {
    svg: 'https://flagcdn.com/td.svg',
    png: 'https://flagcdn.com/w320/td.png',
    emoji: '🇹🇩',
    alt: 'Flag of Chad'
  },
  'cameroon': {
    svg: 'https://flagcdn.com/cm.svg',
    png: 'https://flagcdn.com/w320/cm.png',
    emoji: '🇨🇲',
    alt: 'Flag of Cameroon'
  },
  'republic-congo': {
    svg: 'https://flagcdn.com/cg.svg',
    png: 'https://flagcdn.com/w320/cg.png',
    emoji: '🇨🇬',
    alt: 'Flag of Republic of Congo'
  },
  'equatorial-guinea': {
    svg: 'https://flagcdn.com/gq.svg',
    png: 'https://flagcdn.com/w320/gq.png',
    emoji: '🇬🇶',
    alt: 'Flag of Equatorial Guinea'
  },
  'gabon': {
    svg: 'https://flagcdn.com/ga.svg',
    png: 'https://flagcdn.com/w320/ga.png',
    emoji: '🇬🇦',
    alt: 'Flag of Gabon'
  },
  'sao-tome-principe': {
    svg: 'https://flagcdn.com/st.svg',
    png: 'https://flagcdn.com/w320/st.png',
    emoji: '🇸🇹',
    alt: 'Flag of São Tomé and Príncipe'
  },

  // Southern Africa
  'south-africa': {
    svg: 'https://flagcdn.com/za.svg',
    png: 'https://flagcdn.com/w320/za.png',
    emoji: '🇿🇦',
    alt: 'Flag of South Africa'
  },
  'zimbabwe': {
    svg: 'https://flagcdn.com/zw.svg',
    png: 'https://flagcdn.com/w320/zw.png',
    emoji: '🇿🇼',
    alt: 'Flag of Zimbabwe'
  },
  'botswana': {
    svg: 'https://flagcdn.com/bw.svg',
    png: 'https://flagcdn.com/w320/bw.png',
    emoji: '🇧🇼',
    alt: 'Flag of Botswana'
  },
  'namibia': {
    svg: 'https://flagcdn.com/na.svg',
    png: 'https://flagcdn.com/w320/na.png',
    emoji: '🇳🇦',
    alt: 'Flag of Namibia'
  },
  'zambia': {
    svg: 'https://flagcdn.com/zm.svg',
    png: 'https://flagcdn.com/w320/zm.png',
    emoji: '🇿🇲',
    alt: 'Flag of Zambia'
  },
  'malawi': {
    svg: 'https://flagcdn.com/mw.svg',
    png: 'https://flagcdn.com/w320/mw.png',
    emoji: '🇲🇼',
    alt: 'Flag of Malawi'
  },
  'mozambique': {
    svg: 'https://flagcdn.com/mz.svg',
    png: 'https://flagcdn.com/w320/mz.png',
    emoji: '🇲🇿',
    alt: 'Flag of Mozambique'
  },
  'angola': {
    svg: 'https://flagcdn.com/ao.svg',
    png: 'https://flagcdn.com/w320/ao.png',
    emoji: '🇦🇴',
    alt: 'Flag of Angola'
  },
  'lesotho': {
    svg: 'https://flagcdn.com/ls.svg',
    png: 'https://flagcdn.com/w320/ls.png',
    emoji: '🇱🇸',
    alt: 'Flag of Lesotho'
  },
  'eswatini': {
    svg: 'https://flagcdn.com/sz.svg',
    png: 'https://flagcdn.com/w320/sz.png',
    emoji: '🇸🇿',
    alt: 'Flag of Eswatini'
  },
};

// Utility functions
export const getFlagImage = (countryId: string, format: 'svg' | 'png' = 'svg'): string => {
  const flagData = AFRICAN_COUNTRY_FLAGS[countryId];
  return flagData ? flagData[format] : '';
};

export const getFlagEmoji = (countryId: string): string => {
  const flagData = AFRICAN_COUNTRY_FLAGS[countryId];
  return flagData ? flagData.emoji : '🏳️';
};

export const getFlagAlt = (countryId: string): string => {
  const flagData = AFRICAN_COUNTRY_FLAGS[countryId];
  return flagData ? flagData.alt : `Flag of ${countryId}`;
};

export const getAllFlagData = (countryId: string): FlagImageData | null => {
  return AFRICAN_COUNTRY_FLAGS[countryId] || null;
};
