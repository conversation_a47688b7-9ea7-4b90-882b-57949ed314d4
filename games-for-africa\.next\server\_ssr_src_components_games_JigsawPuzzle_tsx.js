"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_games_JigsawPuzzle_tsx";
exports.ids = ["_ssr_src_components_games_JigsawPuzzle_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/games/JigsawPuzzle.tsx":
/*!***********************************************!*\
  !*** ./src/components/games/JigsawPuzzle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=shuffleArray!=!@/utils */ \"(ssr)/__barrel_optimize__?names=shuffleArray!=!./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(ssr)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/VictoryAnimation */ \"(ssr)/./src/components/ui/VictoryAnimation.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(ssr)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst JigsawPuzzle = ({ countries, onComplete })=>{\n    const [pieces, setPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPiece, setDraggedPiece] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placedPieces, setPlacedPieces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRegion, setSelectedRegion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const regions = [\n        'All',\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    // Define the exact country counts per region\n    const regionCounts = {\n        'North Africa': 6,\n        'West Africa': 16,\n        'East Africa': 10,\n        'Central Africa': 8,\n        'Southern Africa': 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory) {\n                const timer = setInterval({\n                    \"JigsawPuzzle.useEffect.timer\": ()=>{\n                        setTimeElapsed({\n                            \"JigsawPuzzle.useEffect.timer\": (prev)=>prev + 1\n                        }[\"JigsawPuzzle.useEffect.timer\"]);\n                    }\n                }[\"JigsawPuzzle.useEffect.timer\"], 1000);\n                return ({\n                    \"JigsawPuzzle.useEffect\": ()=>clearInterval(timer)\n                })[\"JigsawPuzzle.useEffect\"];\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    // Check for immediate completion when pieces are placed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JigsawPuzzle.useEffect\": ()=>{\n            if (gameStarted && !gameComplete && !showVictory && pieces.length > 0) {\n                if (_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.checkGameCompletion['jigsaw-puzzle'](placedPieces.length, pieces.length)) {\n                    handleImmediateCompletion();\n                }\n            }\n        }\n    }[\"JigsawPuzzle.useEffect\"], [\n        placedPieces.length,\n        pieces.length,\n        gameStarted,\n        gameComplete,\n        showVictory\n    ]);\n    const generatePuzzle = ()=>{\n        let filteredCountries = [];\n        let gridSize = {\n            cols: 6,\n            rows: 5\n        }; // Default grid size\n        if (selectedRegion === 'All') {\n            // Complete Africa mode - select representative countries from each region\n            const northAfrica = countries.filter((c)=>c.region === 'North Africa').slice(0, 3);\n            const westAfrica = countries.filter((c)=>c.region === 'West Africa').slice(0, 6);\n            const eastAfrica = countries.filter((c)=>c.region === 'East Africa').slice(0, 4);\n            const centralAfrica = countries.filter((c)=>c.region === 'Central Africa').slice(0, 3);\n            const southernAfrica = countries.filter((c)=>c.region === 'Southern Africa').slice(0, 4);\n            filteredCountries = [\n                ...northAfrica,\n                ...westAfrica,\n                ...eastAfrica,\n                ...centralAfrica,\n                ...southernAfrica\n            ];\n            gridSize = {\n                cols: 8,\n                rows: 4\n            }; // Larger grid for complete Africa\n        } else {\n            // Region-specific mode - include all countries from the selected region\n            filteredCountries = countries.filter((c)=>c.region === selectedRegion);\n            // Adjust grid size based on region\n            switch(selectedRegion){\n                case 'North Africa':\n                    gridSize = {\n                        cols: 3,\n                        rows: 2\n                    }; // 6 countries\n                    break;\n                case 'West Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 4\n                    }; // 16 countries\n                    break;\n                case 'East Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n                case 'Central Africa':\n                    gridSize = {\n                        cols: 4,\n                        rows: 2\n                    }; // 8 countries\n                    break;\n                case 'Southern Africa':\n                    gridSize = {\n                        cols: 5,\n                        rows: 2\n                    }; // 10 countries\n                    break;\n            }\n        }\n        // Generate geographical positions based on actual African geography\n        const regionPositions = {\n            'North Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 1\n                }\n            ],\n            'West Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 0,\n                    y: 2\n                },\n                {\n                    x: 1,\n                    y: 2\n                },\n                {\n                    x: 2,\n                    y: 2\n                },\n                {\n                    x: 3,\n                    y: 2\n                },\n                {\n                    x: 0,\n                    y: 3\n                },\n                {\n                    x: 1,\n                    y: 3\n                },\n                {\n                    x: 2,\n                    y: 3\n                },\n                {\n                    x: 3,\n                    y: 3\n                },\n                {\n                    x: 4,\n                    y: 0\n                },\n                {\n                    x: 4,\n                    y: 1\n                }\n            ],\n            'East Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ],\n            'Central Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 0\n                }\n            ],\n            'Southern Africa': [\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: 1,\n                    y: 0\n                },\n                {\n                    x: 2,\n                    y: 0\n                },\n                {\n                    x: 3,\n                    y: 0\n                },\n                {\n                    x: 0,\n                    y: 1\n                },\n                {\n                    x: 1,\n                    y: 1\n                },\n                {\n                    x: 2,\n                    y: 1\n                },\n                {\n                    x: 3,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 1\n                },\n                {\n                    x: 4,\n                    y: 0\n                }\n            ]\n        };\n        const puzzlePieces = [];\n        filteredCountries.forEach((country, index)=>{\n            let correctPos;\n            if (selectedRegion === 'All') {\n                // For complete Africa mode, arrange by regions\n                if (country.region === 'North Africa') {\n                    correctPos = {\n                        x: index % 8,\n                        y: 0\n                    };\n                } else if (country.region === 'West Africa') {\n                    const westIndex = index - 3; // Offset by North Africa countries\n                    correctPos = {\n                        x: westIndex % 8,\n                        y: 1\n                    };\n                } else if (country.region === 'East Africa') {\n                    const eastIndex = index - 9; // Offset by North + West Africa countries\n                    correctPos = {\n                        x: eastIndex % 8,\n                        y: 2\n                    };\n                } else if (country.region === 'Central Africa') {\n                    const centralIndex = index - 13; // Offset by previous regions\n                    correctPos = {\n                        x: centralIndex % 8,\n                        y: 3\n                    };\n                } else {\n                    const southIndex = index - 16; // Offset by previous regions\n                    correctPos = {\n                        x: southIndex % 8,\n                        y: 3\n                    };\n                }\n            } else {\n                // For region-specific mode, use geographical positions\n                const regionPositions_array = regionPositions[country.region] || [];\n                correctPos = regionPositions_array[index] || {\n                    x: index % gridSize.cols,\n                    y: Math.floor(index / gridSize.cols)\n                };\n            }\n            puzzlePieces.push({\n                id: `piece-${country.id}`,\n                countryId: country.id,\n                countryName: country.name,\n                flag: country.flagUrl,\n                region: country.region,\n                position: {\n                    x: -1,\n                    y: -1\n                },\n                placed: false,\n                correctPosition: correctPos\n            });\n        });\n        return (0,_barrel_optimize_names_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(puzzlePieces);\n    };\n    const startGame = ()=>{\n        const newPieces = generatePuzzle();\n        setPieces(newPieces);\n        setPlacedPieces([]);\n        setScore(0);\n        setMoves(0);\n        setTimeElapsed(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowVictory(false);\n        setCompletionData(null);\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const completionTime = timeElapsed;\n        const timeBonus = Math.max(0, 100 - Math.floor(timeElapsed / 10));\n        const moveBonus = Math.max(0, 50 - moves);\n        const finalScore = score + timeBonus + moveBonus;\n        // Determine difficulty based on region\n        let difficulty = 'medium';\n        if (selectedRegion === 'North Africa') difficulty = 'easy';\n        else if (selectedRegion === 'West Africa' || selectedRegion === 'All') difficulty = 'hard';\n        const gameData = {\n            gameType: 'jigsaw-puzzle',\n            score: finalScore,\n            timeRemaining: 0,\n            totalTime: timeElapsed,\n            perfectScore: moves <= pieces.length,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_5__.processGameCompletionWithProgression)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleDragStart = (e, pieceId)=>{\n        setDraggedPiece(pieceId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, targetX, targetY)=>{\n        e.preventDefault();\n        if (!draggedPiece) return;\n        const piece = pieces.find((p)=>p.id === draggedPiece);\n        if (!piece) return;\n        setMoves(moves + 1);\n        // Check if position is correct\n        const isCorrect = piece.correctPosition.x === targetX && piece.correctPosition.y === targetY;\n        if (isCorrect) {\n            // Correct placement\n            setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                        ...p,\n                        position: {\n                            x: targetX,\n                            y: targetY\n                        },\n                        placed: true\n                    } : p));\n            setPlacedPieces((prev)=>[\n                    ...prev,\n                    draggedPiece\n                ]);\n            setScore(score + 10);\n        // Completion will be handled by useEffect watching placedPieces.length\n        } else {\n            // Incorrect placement - piece bounces back\n            setTimeout(()=>{\n                setPieces((prev)=>prev.map((p)=>p.id === draggedPiece ? {\n                            ...p,\n                            position: {\n                                x: -1,\n                                y: -1\n                            },\n                            placed: false\n                        } : p));\n            }, 500);\n        }\n        setDraggedPiece(null);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    const getGridPosition = (x, y)=>{\n        return pieces.find((p)=>p.position.x === x && p.position.y === y && p.placed);\n    };\n    const getGridSize = ()=>{\n        if (selectedRegion === 'All') {\n            return {\n                cols: 8,\n                rows: 4\n            };\n        }\n        switch(selectedRegion){\n            case 'North Africa':\n                return {\n                    cols: 3,\n                    rows: 2\n                };\n            case 'West Africa':\n                return {\n                    cols: 4,\n                    rows: 4\n                };\n            case 'East Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            case 'Central Africa':\n                return {\n                    cols: 4,\n                    rows: 2\n                };\n            case 'Southern Africa':\n                return {\n                    cols: 5,\n                    rows: 2\n                };\n            default:\n                return {\n                    cols: 6,\n                    rows: 5\n                };\n        }\n    };\n    const handleVictoryComplete = ()=>{\n        if (completionData) {\n            onComplete(completionData.finalScore);\n        }\n    };\n    const handleAutoProgress = (action, nextDifficulty)=>{\n        if (action === 'next-difficulty' && nextDifficulty) {\n            // For jigsaw puzzle, we can progress through regions as difficulty levels\n            const regionProgression = {\n                'easy': 'North Africa',\n                'medium': 'East Africa',\n                'hard': 'West Africa'\n            };\n            const nextRegion = regionProgression[nextDifficulty];\n            if (nextRegion) {\n                setSelectedRegion(nextRegion);\n                startGame();\n            }\n        } else {\n            // Return to menu\n            if (completionData) {\n                onComplete(completionData.finalScore);\n            }\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83D\\uDDFA️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Explore all 50 African countries! Choose a specific region or challenge yourself with the complete Africa map.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Region:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto\",\n                                children: regions.map((region)=>{\n                                    const getRegionInfo = (region)=>{\n                                        if (region === 'All') {\n                                            return {\n                                                count: 50,\n                                                description: 'Complete Africa'\n                                            };\n                                        }\n                                        const count = regionCounts[region] || 0;\n                                        return {\n                                            count,\n                                            description: `${count} countries`\n                                        };\n                                    };\n                                    const { count, description } = getRegionInfo(region);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedRegion(region),\n                                        className: `p-4 rounded-lg transition-all duration-200 border-2 ${selectedRegion === region ? 'bg-yellow-400 text-gray-900 border-yellow-400 transform scale-105' : 'bg-gray-700 text-white border-gray-600 hover:bg-gray-600 hover:border-gray-500'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: region\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `text-sm ${selectedRegion === region ? 'text-gray-700' : 'text-gray-400'}`,\n                                                    children: description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                region !== 'All' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `text-xs mt-1 ${selectedRegion === region ? 'text-gray-600' : 'text-gray-500'}`,\n                                                    children: [\n                                                        \"Difficulty: \",\n                                                        count <= 6 ? 'Easy' : count <= 10 ? 'Medium' : 'Hard'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, region, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Complete Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 50 countries across all regions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Regional Focus:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Master specific geographical areas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"North Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 6 countries (Easy)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"East/Central/Southern Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 8-10 countries (Medium)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"West Africa:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" 16 countries (Hard)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag pieces to their correct geographical positions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Learn about African geography and country locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Puzzle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83E\\uDDE9 Jigsaw Puzzle Map\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: formatTime(timeElapsed)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: placedPieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: pieces.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDDFA️ \",\n                                    selectedRegion === 'All' ? 'Complete Africa Map' : `${selectedRegion} Map`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `grid gap-2 min-h-[400px]`,\n                                        style: {\n                                            gridTemplateColumns: `repeat(${getGridSize().cols}, minmax(0, 1fr))`,\n                                            gridTemplateRows: `repeat(${getGridSize().rows}, minmax(0, 1fr))`\n                                        },\n                                        children: Array.from({\n                                            length: getGridSize().cols * getGridSize().rows\n                                        }, (_, index)=>{\n                                            const x = index % getGridSize().cols;\n                                            const y = Math.floor(index / getGridSize().cols);\n                                            const placedPiece = getGridPosition(x, y);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200 ${placedPiece ? 'bg-green-500 bg-opacity-20 border-green-400' : 'hover:border-yellow-400'}`,\n                                                onDragOver: handleDragOver,\n                                                onDrop: (e)=>handleDrop(e, x, y),\n                                                children: placedPiece && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-1 flex justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                countryId: placedPiece.countryId,\n                                                                size: \"medium\",\n                                                                className: \"mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white font-medium\",\n                                                            children: placedPiece.countryName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, `${x}-${y}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedRegion === 'All' ? 'Drag countries to their geographical regions' : `Place all ${regionCounts[selectedRegion] || 0} countries in ${selectedRegion}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83E\\uDDE9 Puzzle Pieces\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[500px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: pieces.filter((p)=>!p.placed).map((piece)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, piece.id),\n                                                className: `bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 ${draggedPiece === piece.id ? 'opacity-50' : ''}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            countryId: piece.countryId,\n                                                            size: \"medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: piece.countryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: piece.region\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, piece.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    pieces.filter((p)=>!p.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All pieces placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, undefined),\n            showVictory && completionData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_VictoryAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showVictory,\n                completionData: completionData,\n                onComplete: handleVictoryComplete,\n                onAutoProgress: handleAutoProgress\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n                lineNumber: 565,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\JigsawPuzzle.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JigsawPuzzle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/games/JigsawPuzzle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(ssr)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FlagImage = ({ countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick })=>{\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = `${flagImageUrl}?cache=1`;\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = `\n    ${sizeClasses[size]} \n    object-cover \n    rounded-sm \n    border \n    border-gray-300 \n    shadow-sm\n    ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}\n    ${className}\n  `;\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${baseClasses} bg-gray-200 animate-pulse flex items-center justify-center`,\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n          ${sizeClasses[size]} \n          flex \n          items-center \n          justify-center \n          bg-gray-100 \n          rounded-sm \n          border \n          border-gray-300\n          ${onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : ''}\n          ${className}\n        `,\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${baseClasses} bg-gray-100 flex items-center justify-center`,\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FlagImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/VictoryAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/VictoryAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(ssr)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst VictoryAnimation = ({ isVisible, completionData, onComplete, onAutoProgress })=>{\n    const [showConfetti, setShowConfetti] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationPhase, setAnimationPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('enter');\n    const [progressCountdown, setProgressCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProgressing, setIsProgressing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_2__.celebrationConfigs[completionData.celebrationType];\n    const progressDelay = completionData.autoProgression.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VictoryAnimation.useEffect\": ()=>{\n            if (!isVisible) return;\n            setShowConfetti(true);\n            setAnimationPhase('enter');\n            setIsProgressing(false);\n            // Start countdown for auto-progression\n            const countdownStart = Math.ceil(progressDelay / 1000);\n            setProgressCountdown(countdownStart);\n            const timeline = [\n                // Enter phase\n                {\n                    delay: 0,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('enter')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Display phase\n                {\n                    delay: 300,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('display')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Start countdown\n                {\n                    delay: 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            const countdownInterval = setInterval({\n                                \"VictoryAnimation.useEffect.countdownInterval\": ()=>{\n                                    setProgressCountdown({\n                                        \"VictoryAnimation.useEffect.countdownInterval\": (prev)=>{\n                                            if (prev <= 1) {\n                                                clearInterval(countdownInterval);\n                                                return 0;\n                                            }\n                                            return prev - 1;\n                                        }\n                                    }[\"VictoryAnimation.useEffect.countdownInterval\"]);\n                                }\n                            }[\"VictoryAnimation.useEffect.countdownInterval\"], 1000);\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Exit phase\n                {\n                    delay: progressDelay - 500,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>setAnimationPhase('exit')\n                    }[\"VictoryAnimation.useEffect\"]\n                },\n                // Auto-progress\n                {\n                    delay: progressDelay,\n                    action: {\n                        \"VictoryAnimation.useEffect\": ()=>{\n                            setShowConfetti(false);\n                            setIsProgressing(true);\n                            if (onAutoProgress) {\n                                onAutoProgress(completionData.autoProgression.action, completionData.autoProgression.nextDifficulty);\n                            } else {\n                                onComplete();\n                            }\n                        }\n                    }[\"VictoryAnimation.useEffect\"]\n                }\n            ];\n            const timeouts = timeline.map({\n                \"VictoryAnimation.useEffect.timeouts\": ({ delay, action })=>setTimeout(action, delay)\n            }[\"VictoryAnimation.useEffect.timeouts\"]);\n            return ({\n                \"VictoryAnimation.useEffect\": ()=>{\n                    timeouts.forEach(clearTimeout);\n                }\n            })[\"VictoryAnimation.useEffect\"];\n        }\n    }[\"VictoryAnimation.useEffect\"], [\n        isVisible,\n        progressDelay,\n        onComplete,\n        onAutoProgress,\n        completionData.autoProgression\n    ]);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\",\n        children: [\n            showConfetti && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: config.particles\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute animate-bounce\",\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`,\n                            animationDelay: `${Math.random() * 2}s`,\n                            animationDuration: `${1 + Math.random() * 2}s`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full\",\n                            style: {\n                                backgroundColor: config.colors[Math.floor(Math.random() * config.colors.length)],\n                                transform: `rotate(${Math.random() * 360}deg)`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          bg-gradient-to-br from-yellow-400 to-orange-500 \n          rounded-2xl p-8 text-center max-w-md mx-4 \n          transform transition-all duration-500 ease-out\n          ${animationPhase === 'enter' ? 'scale-0 rotate-180 opacity-0' : ''}\n          ${animationPhase === 'display' ? 'scale-100 rotate-0 opacity-100' : ''}\n          ${animationPhase === 'exit' ? 'scale-110 opacity-90' : ''}\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-8xl mb-4 animate-pulse\",\n                        children: config.emoji\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: completionData.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white bg-opacity-20 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: completionData.finalScore\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Final Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                \"+\",\n                                                completionData.timeBonus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Time Bonus\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-500 bg-opacity-20 rounded-lg p-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-blue-900 font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: [\n                                        \"+\",\n                                        completionData.xpGained\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    children: \"XP Gained!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    completionData.achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-900 font-semibold text-sm\",\n                                children: \"Achievements Unlocked:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            completionData.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-500 bg-opacity-20 rounded-lg p-2 text-purple-900 font-medium text-sm\",\n                                    style: {\n                                        animationDelay: `${index * 200}ms`\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 \",\n                                        achievement\n                                    ]\n                                }, achievement, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-300 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out\",\n                                    style: {\n                                        width: animationPhase === 'display' ? `${100 - progressCountdown / Math.ceil(progressDelay / 1000) * 100}%` : '0%'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-700 text-xs mt-2\",\n                                children: isProgressing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 font-semibold\",\n                                    children: \"Progressing...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined) : progressCountdown > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: completionData.autoProgression.action === 'next-difficulty' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Starting \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold capitalize\",\n                                                children: completionData.autoProgression.nextDifficulty\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 30\n                                            }, undefined),\n                                            \" difficulty in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \"Returning to menu in \",\n                                            progressCountdown,\n                                            \"s...\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-semibold\",\n                                    children: \"Ready!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-yellow-300 text-2xl animate-ping\",\n                            style: {\n                                left: `${20 + Math.random() * 60}%`,\n                                top: `${20 + Math.random() * 60}%`,\n                                animationDelay: `${Math.random() * 2}s`,\n                                animationDuration: `${2 + Math.random() * 2}s`\n                            },\n                            children: \"⭐\"\n                        }, `star-${i}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from({\n                        length: 12\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute text-white text-lg animate-bounce\",\n                            style: {\n                                left: `${10 + Math.random() * 80}%`,\n                                top: `${10 + Math.random() * 80}%`,\n                                animationDelay: `${Math.random() * 3}s`,\n                                animationDuration: `${1 + Math.random() * 2}s`\n                            },\n                            children: \"✨\"\n                        }, `sparkle-${i}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            animationPhase === 'display' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl font-bold text-white animate-bounce\",\n                    children: [\n                        completionData.celebrationType === 'perfect' && '🎊 PERFECT! 🎊',\n                        completionData.celebrationType === 'excellent' && '🌟 EXCELLENT! 🌟',\n                        completionData.celebrationType === 'good' && '👏 GREAT JOB! 👏',\n                        completionData.celebrationType === 'complete' && '✅ COMPLETE! ✅'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\VictoryAnimation.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VictoryAnimation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/VictoryAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = (countryId, format = 'svg')=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : `Flag of ${countryId}`;\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/flagUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/gameCompletion.ts":
/*!*************************************!*\
  !*** ./src/utils/gameCompletion.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   calculateXP: () => (/* binding */ calculateXP),\n/* harmony export */   celebrationConfigs: () => (/* binding */ celebrationConfigs),\n/* harmony export */   checkGameCompletion: () => (/* binding */ checkGameCompletion),\n/* harmony export */   getCompletionMessage: () => (/* binding */ getCompletionMessage),\n/* harmony export */   getNextAction: () => (/* binding */ getNextAction),\n/* harmony export */   processGameCompletion: () => (/* binding */ processGameCompletion),\n/* harmony export */   processGameCompletionWithProgression: () => (/* binding */ processGameCompletionWithProgression)\n/* harmony export */ });\n// Game completion utilities and animations\n// Calculate time bonus based on remaining time\nconst calculateTimeBonus = (timeRemaining, totalTime, difficulty)=>{\n    if (timeRemaining <= 0) return 0;\n    const timePercentage = timeRemaining / totalTime;\n    const difficultyMultiplier = {\n        easy: 1,\n        medium: 1.5,\n        hard: 2\n    }[difficulty];\n    // Base time bonus: up to 50 points for completing with full time remaining\n    const baseBonus = Math.floor(timePercentage * 50);\n    return Math.floor(baseBonus * difficultyMultiplier);\n};\n// Calculate XP based on performance\nconst calculateXP = (data)=>{\n    const baseXP = 20; // Base XP for completion\n    const scoreMultiplier = Math.floor(data.score / 10); // 1 XP per 10 points\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const difficultyBonus = {\n        easy: 0,\n        medium: 10,\n        hard: 20\n    }[data.difficulty];\n    const perfectBonus = data.perfectScore ? 30 : 0;\n    return baseXP + scoreMultiplier + Math.floor(timeBonus / 2) + difficultyBonus + perfectBonus;\n};\n// Determine completion message and type\nconst getCompletionMessage = (data)=>{\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore && timePercentage > 0.7) {\n        return {\n            message: 'Perfect! Outstanding performance!',\n            type: 'perfect'\n        };\n    } else if (data.perfectScore && timePercentage > 0.4) {\n        return {\n            message: 'Excellent! Great job!',\n            type: 'excellent'\n        };\n    } else if (data.perfectScore) {\n        return {\n            message: 'Perfect Score! Well done!',\n            type: 'excellent'\n        };\n    } else if (timePercentage > 0.5) {\n        return {\n            message: 'Great work! Fast completion!',\n            type: 'good'\n        };\n    } else {\n        return {\n            message: 'Game Complete! Nice job!',\n            type: 'complete'\n        };\n    }\n};\n// Process game completion\nconst processGameCompletion = (data)=>{\n    const timeBonus = calculateTimeBonus(data.timeRemaining, data.totalTime, data.difficulty);\n    const finalScore = data.score + timeBonus;\n    const xpGained = calculateXP(data);\n    const { message, type } = getCompletionMessage(data);\n    // Determine achievements based on performance\n    const achievements = [];\n    const timePercentage = data.timeRemaining / data.totalTime;\n    if (data.perfectScore) {\n        achievements.push('Perfect Score');\n    }\n    if (timePercentage > 0.8) {\n        achievements.push('Speed Demon');\n    }\n    if (data.difficulty === 'hard' && data.perfectScore) {\n        achievements.push('Master Player');\n    }\n    if (timeBonus > 30) {\n        achievements.push('Time Master');\n    }\n    return {\n        finalScore,\n        timeBonus,\n        xpGained,\n        achievements,\n        message,\n        celebrationType: type\n    };\n};\n// Game-specific completion checkers\nconst checkGameCompletion = {\n    'flag-matching': (matches, totalPairs)=>{\n        return matches >= totalPairs;\n    },\n    'country-name-scramble': (currentRound, totalRounds, roundComplete)=>{\n        return currentRound >= totalRounds - 1 && roundComplete;\n    },\n    'jigsaw-puzzle': (placedPieces, totalPieces)=>{\n        return placedPieces >= totalPieces;\n    },\n    'quiz': (answeredQuestions, totalQuestions)=>{\n        return answeredQuestions >= totalQuestions;\n    },\n    'memory-grid': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'matching': (matchedPairs, totalPairs)=>{\n        return matchedPairs >= totalPairs;\n    },\n    'country-explorer': (visitedCountries, targetCountries)=>{\n        return visitedCountries >= targetCountries;\n    },\n    'speed-challenge': (answeredQuestions, targetQuestions)=>{\n        return answeredQuestions >= targetQuestions;\n    },\n    'mystery-land': (correctGuesses, totalRounds)=>{\n        return correctGuesses >= totalRounds;\n    },\n    'timeline-builder': (placedEvents, totalEvents)=>{\n        return placedEvents >= totalEvents;\n    },\n    'where-in-africa': (completedRounds, totalRounds)=>{\n        return completedRounds >= totalRounds;\n    },\n    'dress-character': (completedOutfits, targetOutfits)=>{\n        return completedOutfits >= targetOutfits;\n    }\n};\n// Celebration animation configurations\nconst celebrationConfigs = {\n    perfect: {\n        duration: 3000,\n        emoji: '🏆',\n        colors: [\n            '#FFD700',\n            '#FFA500',\n            '#FF6B6B'\n        ],\n        particles: 50\n    },\n    excellent: {\n        duration: 2500,\n        emoji: '🎉',\n        colors: [\n            '#4ECDC4',\n            '#45B7D1',\n            '#96CEB4'\n        ],\n        particles: 40\n    },\n    good: {\n        duration: 2000,\n        emoji: '👏',\n        colors: [\n            '#FECA57',\n            '#48CAE4',\n            '#A8E6CF'\n        ],\n        particles: 30\n    },\n    complete: {\n        duration: 1500,\n        emoji: '✅',\n        colors: [\n            '#6C5CE7',\n            '#A29BFE',\n            '#74B9FF'\n        ],\n        particles: 20\n    }\n};\n// Auto-progression logic\nconst getNextAction = (gameType, difficulty)=>{\n    // If completed on easy, progress to medium\n    if (difficulty === 'easy') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'medium'\n        };\n    }\n    // If completed on medium, progress to hard\n    if (difficulty === 'medium') {\n        return {\n            action: 'next-difficulty',\n            nextDifficulty: 'hard'\n        };\n    }\n    // If completed on hard, return to menu\n    return {\n        action: 'menu'\n    };\n};\n// Process game completion with auto-progression\nconst processGameCompletionWithProgression = (data)=>{\n    const baseResult = processGameCompletion(data);\n    const progression = getNextAction(data.gameType, data.difficulty);\n    // Determine delay based on celebration type\n    const delay = {\n        perfect: 3000,\n        excellent: 2500,\n        good: 2000,\n        complete: 1500\n    }[baseResult.celebrationType];\n    return {\n        ...baseResult,\n        autoProgression: {\n            ...progression,\n            delay\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvZ2FtZUNvbXBsZXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSwyQ0FBMkM7QUFzQjNDLCtDQUErQztBQUN4QyxNQUFNQSxxQkFBcUIsQ0FBQ0MsZUFBdUJDLFdBQW1CQztJQUMzRSxJQUFJRixpQkFBaUIsR0FBRyxPQUFPO0lBRS9CLE1BQU1HLGlCQUFpQkgsZ0JBQWdCQztJQUN2QyxNQUFNRyx1QkFBdUI7UUFDM0JDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxNQUFNO0lBQ1IsQ0FBQyxDQUFDTCxXQUFXO0lBRWIsMkVBQTJFO0lBQzNFLE1BQU1NLFlBQVlDLEtBQUtDLEtBQUssQ0FBQ1AsaUJBQWlCO0lBQzlDLE9BQU9NLEtBQUtDLEtBQUssQ0FBQ0YsWUFBWUo7QUFDaEMsRUFBRTtBQUVGLG9DQUFvQztBQUM3QixNQUFNTyxjQUFjLENBQUNDO0lBQzFCLE1BQU1DLFNBQVMsSUFBSSx5QkFBeUI7SUFDNUMsTUFBTUMsa0JBQWtCTCxLQUFLQyxLQUFLLENBQUNFLEtBQUtHLEtBQUssR0FBRyxLQUFLLHFCQUFxQjtJQUMxRSxNQUFNQyxZQUFZakIsbUJBQW1CYSxLQUFLWixhQUFhLEVBQUVZLEtBQUtYLFNBQVMsRUFBRVcsS0FBS1YsVUFBVTtJQUN4RixNQUFNZSxrQkFBa0I7UUFDdEJaLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxNQUFNO0lBQ1IsQ0FBQyxDQUFDSyxLQUFLVixVQUFVLENBQUM7SUFFbEIsTUFBTWdCLGVBQWVOLEtBQUtPLFlBQVksR0FBRyxLQUFLO0lBRTlDLE9BQU9OLFNBQVNDLGtCQUFrQkwsS0FBS0MsS0FBSyxDQUFDTSxZQUFZLEtBQUtDLGtCQUFrQkM7QUFDbEYsRUFBRTtBQUVGLHdDQUF3QztBQUNqQyxNQUFNRSx1QkFBdUIsQ0FBQ1I7SUFDbkMsTUFBTVQsaUJBQWlCUyxLQUFLWixhQUFhLEdBQUdZLEtBQUtYLFNBQVM7SUFFMUQsSUFBSVcsS0FBS08sWUFBWSxJQUFJaEIsaUJBQWlCLEtBQUs7UUFDN0MsT0FBTztZQUFFa0IsU0FBUztZQUFxQ0MsTUFBTTtRQUFVO0lBQ3pFLE9BQU8sSUFBSVYsS0FBS08sWUFBWSxJQUFJaEIsaUJBQWlCLEtBQUs7UUFDcEQsT0FBTztZQUFFa0IsU0FBUztZQUF5QkMsTUFBTTtRQUFZO0lBQy9ELE9BQU8sSUFBSVYsS0FBS08sWUFBWSxFQUFFO1FBQzVCLE9BQU87WUFBRUUsU0FBUztZQUE2QkMsTUFBTTtRQUFZO0lBQ25FLE9BQU8sSUFBSW5CLGlCQUFpQixLQUFLO1FBQy9CLE9BQU87WUFBRWtCLFNBQVM7WUFBZ0NDLE1BQU07UUFBTztJQUNqRSxPQUFPO1FBQ0wsT0FBTztZQUFFRCxTQUFTO1lBQTRCQyxNQUFNO1FBQVc7SUFDakU7QUFDRixFQUFFO0FBRUYsMEJBQTBCO0FBQ25CLE1BQU1DLHdCQUF3QixDQUFDWDtJQUNwQyxNQUFNSSxZQUFZakIsbUJBQW1CYSxLQUFLWixhQUFhLEVBQUVZLEtBQUtYLFNBQVMsRUFBRVcsS0FBS1YsVUFBVTtJQUN4RixNQUFNc0IsYUFBYVosS0FBS0csS0FBSyxHQUFHQztJQUNoQyxNQUFNUyxXQUFXZCxZQUFZQztJQUM3QixNQUFNLEVBQUVTLE9BQU8sRUFBRUMsSUFBSSxFQUFFLEdBQUdGLHFCQUFxQlI7SUFFL0MsOENBQThDO0lBQzlDLE1BQU1jLGVBQXlCLEVBQUU7SUFDakMsTUFBTXZCLGlCQUFpQlMsS0FBS1osYUFBYSxHQUFHWSxLQUFLWCxTQUFTO0lBRTFELElBQUlXLEtBQUtPLFlBQVksRUFBRTtRQUNyQk8sYUFBYUMsSUFBSSxDQUFDO0lBQ3BCO0lBRUEsSUFBSXhCLGlCQUFpQixLQUFLO1FBQ3hCdUIsYUFBYUMsSUFBSSxDQUFDO0lBQ3BCO0lBRUEsSUFBSWYsS0FBS1YsVUFBVSxLQUFLLFVBQVVVLEtBQUtPLFlBQVksRUFBRTtRQUNuRE8sYUFBYUMsSUFBSSxDQUFDO0lBQ3BCO0lBRUEsSUFBSVgsWUFBWSxJQUFJO1FBQ2xCVSxhQUFhQyxJQUFJLENBQUM7SUFDcEI7SUFFQSxPQUFPO1FBQ0xIO1FBQ0FSO1FBQ0FTO1FBQ0FDO1FBQ0FMO1FBQ0FPLGlCQUFpQk47SUFDbkI7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQzdCLE1BQU1PLHNCQUFzQjtJQUNqQyxpQkFBaUIsQ0FBQ0MsU0FBaUJDO1FBQ2pDLE9BQU9ELFdBQVdDO0lBQ3BCO0lBRUEseUJBQXlCLENBQUNDLGNBQXNCQyxhQUFxQkM7UUFDbkUsT0FBT0YsZ0JBQWdCQyxjQUFjLEtBQUtDO0lBQzVDO0lBRUEsaUJBQWlCLENBQUNDLGNBQXNCQztRQUN0QyxPQUFPRCxnQkFBZ0JDO0lBQ3pCO0lBRUEsUUFBUSxDQUFDQyxtQkFBMkJDO1FBQ2xDLE9BQU9ELHFCQUFxQkM7SUFDOUI7SUFFQSxlQUFlLENBQUNDLGNBQXNCUjtRQUNwQyxPQUFPUSxnQkFBZ0JSO0lBQ3pCO0lBRUEsWUFBWSxDQUFDUSxjQUFzQlI7UUFDakMsT0FBT1EsZ0JBQWdCUjtJQUN6QjtJQUVBLG9CQUFvQixDQUFDUyxrQkFBMEJDO1FBQzdDLE9BQU9ELG9CQUFvQkM7SUFDN0I7SUFFQSxtQkFBbUIsQ0FBQ0osbUJBQTJCSztRQUM3QyxPQUFPTCxxQkFBcUJLO0lBQzlCO0lBRUEsZ0JBQWdCLENBQUNDLGdCQUF3QlY7UUFDdkMsT0FBT1Usa0JBQWtCVjtJQUMzQjtJQUVBLG9CQUFvQixDQUFDVyxjQUFzQkM7UUFDekMsT0FBT0QsZ0JBQWdCQztJQUN6QjtJQUVBLG1CQUFtQixDQUFDQyxpQkFBeUJiO1FBQzNDLE9BQU9hLG1CQUFtQmI7SUFDNUI7SUFFQSxtQkFBbUIsQ0FBQ2Msa0JBQTBCQztRQUM1QyxPQUFPRCxvQkFBb0JDO0lBQzdCO0FBQ0YsRUFBRTtBQUVGLHVDQUF1QztBQUNoQyxNQUFNQyxxQkFBcUI7SUFDaENDLFNBQVM7UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFBQztZQUFXO1lBQVc7U0FBVTtRQUN6Q0MsV0FBVztJQUNiO0lBQ0FDLFdBQVc7UUFDVEosVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFBQztZQUFXO1lBQVc7U0FBVTtRQUN6Q0MsV0FBVztJQUNiO0lBQ0FFLE1BQU07UUFDSkwsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFBQztZQUFXO1lBQVc7U0FBVTtRQUN6Q0MsV0FBVztJQUNiO0lBQ0FHLFVBQVU7UUFDUk4sVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFBQztZQUFXO1lBQVc7U0FBVTtRQUN6Q0MsV0FBVztJQUNiO0FBQ0YsRUFBRTtBQUVGLHlCQUF5QjtBQUNsQixNQUFNSSxnQkFBZ0IsQ0FBQ0MsVUFBb0J6RDtJQUNoRCwyQ0FBMkM7SUFDM0MsSUFBSUEsZUFBZSxRQUFRO1FBQ3pCLE9BQU87WUFBRTBELFFBQVE7WUFBbUJDLGdCQUFnQjtRQUFTO0lBQy9EO0lBRUEsMkNBQTJDO0lBQzNDLElBQUkzRCxlQUFlLFVBQVU7UUFDM0IsT0FBTztZQUFFMEQsUUFBUTtZQUFtQkMsZ0JBQWdCO1FBQU87SUFDN0Q7SUFFQSx1Q0FBdUM7SUFDdkMsT0FBTztRQUFFRCxRQUFRO0lBQU87QUFDMUIsRUFBRTtBQVdGLGdEQUFnRDtBQUN6QyxNQUFNRSx1Q0FBdUMsQ0FBQ2xEO0lBQ25ELE1BQU1tRCxhQUFheEMsc0JBQXNCWDtJQUN6QyxNQUFNb0QsY0FBY04sY0FBYzlDLEtBQUsrQyxRQUFRLEVBQUUvQyxLQUFLVixVQUFVO0lBRWhFLDRDQUE0QztJQUM1QyxNQUFNK0QsUUFBUTtRQUNaZixTQUFTO1FBQ1RLLFdBQVc7UUFDWEMsTUFBTTtRQUNOQyxVQUFVO0lBQ1osQ0FBQyxDQUFDTSxXQUFXbkMsZUFBZSxDQUFDO0lBRTdCLE9BQU87UUFDTCxHQUFHbUMsVUFBVTtRQUNiRyxpQkFBaUI7WUFDZixHQUFHRixXQUFXO1lBQ2RDO1FBQ0Y7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFx1dGlsc1xcZ2FtZUNvbXBsZXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2FtZSBjb21wbGV0aW9uIHV0aWxpdGllcyBhbmQgYW5pbWF0aW9uc1xuaW1wb3J0IHsgR2FtZVR5cGUgfSBmcm9tICdAL3R5cGVzJztcblxuZXhwb3J0IGludGVyZmFjZSBHYW1lQ29tcGxldGlvbkRhdGEge1xuICBnYW1lVHlwZTogR2FtZVR5cGU7XG4gIHNjb3JlOiBudW1iZXI7XG4gIHRpbWVSZW1haW5pbmc6IG51bWJlcjtcbiAgdG90YWxUaW1lOiBudW1iZXI7XG4gIHBlcmZlY3RTY29yZTogYm9vbGVhbjtcbiAgZGlmZmljdWx0eTogJ2Vhc3knIHwgJ21lZGl1bScgfCAnaGFyZCc7XG4gIGNvbXBsZXRpb25UaW1lOiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ29tcGxldGlvblJlc3VsdCB7XG4gIGZpbmFsU2NvcmU6IG51bWJlcjtcbiAgdGltZUJvbnVzOiBudW1iZXI7XG4gIHhwR2FpbmVkOiBudW1iZXI7XG4gIGFjaGlldmVtZW50czogc3RyaW5nW107XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgY2VsZWJyYXRpb25UeXBlOiAncGVyZmVjdCcgfCAnZXhjZWxsZW50JyB8ICdnb29kJyB8ICdjb21wbGV0ZSc7XG59XG5cbi8vIENhbGN1bGF0ZSB0aW1lIGJvbnVzIGJhc2VkIG9uIHJlbWFpbmluZyB0aW1lXG5leHBvcnQgY29uc3QgY2FsY3VsYXRlVGltZUJvbnVzID0gKHRpbWVSZW1haW5pbmc6IG51bWJlciwgdG90YWxUaW1lOiBudW1iZXIsIGRpZmZpY3VsdHk6ICdlYXN5JyB8ICdtZWRpdW0nIHwgJ2hhcmQnKTogbnVtYmVyID0+IHtcbiAgaWYgKHRpbWVSZW1haW5pbmcgPD0gMCkgcmV0dXJuIDA7XG4gIFxuICBjb25zdCB0aW1lUGVyY2VudGFnZSA9IHRpbWVSZW1haW5pbmcgLyB0b3RhbFRpbWU7XG4gIGNvbnN0IGRpZmZpY3VsdHlNdWx0aXBsaWVyID0ge1xuICAgIGVhc3k6IDEsXG4gICAgbWVkaXVtOiAxLjUsXG4gICAgaGFyZDogMlxuICB9W2RpZmZpY3VsdHldO1xuICBcbiAgLy8gQmFzZSB0aW1lIGJvbnVzOiB1cCB0byA1MCBwb2ludHMgZm9yIGNvbXBsZXRpbmcgd2l0aCBmdWxsIHRpbWUgcmVtYWluaW5nXG4gIGNvbnN0IGJhc2VCb251cyA9IE1hdGguZmxvb3IodGltZVBlcmNlbnRhZ2UgKiA1MCk7XG4gIHJldHVybiBNYXRoLmZsb29yKGJhc2VCb251cyAqIGRpZmZpY3VsdHlNdWx0aXBsaWVyKTtcbn07XG5cbi8vIENhbGN1bGF0ZSBYUCBiYXNlZCBvbiBwZXJmb3JtYW5jZVxuZXhwb3J0IGNvbnN0IGNhbGN1bGF0ZVhQID0gKGRhdGE6IEdhbWVDb21wbGV0aW9uRGF0YSk6IG51bWJlciA9PiB7XG4gIGNvbnN0IGJhc2VYUCA9IDIwOyAvLyBCYXNlIFhQIGZvciBjb21wbGV0aW9uXG4gIGNvbnN0IHNjb3JlTXVsdGlwbGllciA9IE1hdGguZmxvb3IoZGF0YS5zY29yZSAvIDEwKTsgLy8gMSBYUCBwZXIgMTAgcG9pbnRzXG4gIGNvbnN0IHRpbWVCb251cyA9IGNhbGN1bGF0ZVRpbWVCb251cyhkYXRhLnRpbWVSZW1haW5pbmcsIGRhdGEudG90YWxUaW1lLCBkYXRhLmRpZmZpY3VsdHkpO1xuICBjb25zdCBkaWZmaWN1bHR5Qm9udXMgPSB7XG4gICAgZWFzeTogMCxcbiAgICBtZWRpdW06IDEwLFxuICAgIGhhcmQ6IDIwXG4gIH1bZGF0YS5kaWZmaWN1bHR5XTtcbiAgXG4gIGNvbnN0IHBlcmZlY3RCb251cyA9IGRhdGEucGVyZmVjdFNjb3JlID8gMzAgOiAwO1xuICBcbiAgcmV0dXJuIGJhc2VYUCArIHNjb3JlTXVsdGlwbGllciArIE1hdGguZmxvb3IodGltZUJvbnVzIC8gMikgKyBkaWZmaWN1bHR5Qm9udXMgKyBwZXJmZWN0Qm9udXM7XG59O1xuXG4vLyBEZXRlcm1pbmUgY29tcGxldGlvbiBtZXNzYWdlIGFuZCB0eXBlXG5leHBvcnQgY29uc3QgZ2V0Q29tcGxldGlvbk1lc3NhZ2UgPSAoZGF0YTogR2FtZUNvbXBsZXRpb25EYXRhKTogeyBtZXNzYWdlOiBzdHJpbmc7IHR5cGU6ICdwZXJmZWN0JyB8ICdleGNlbGxlbnQnIHwgJ2dvb2QnIHwgJ2NvbXBsZXRlJyB9ID0+IHtcbiAgY29uc3QgdGltZVBlcmNlbnRhZ2UgPSBkYXRhLnRpbWVSZW1haW5pbmcgLyBkYXRhLnRvdGFsVGltZTtcbiAgXG4gIGlmIChkYXRhLnBlcmZlY3RTY29yZSAmJiB0aW1lUGVyY2VudGFnZSA+IDAuNykge1xuICAgIHJldHVybiB7IG1lc3NhZ2U6ICdQZXJmZWN0ISBPdXRzdGFuZGluZyBwZXJmb3JtYW5jZSEnLCB0eXBlOiAncGVyZmVjdCcgfTtcbiAgfSBlbHNlIGlmIChkYXRhLnBlcmZlY3RTY29yZSAmJiB0aW1lUGVyY2VudGFnZSA+IDAuNCkge1xuICAgIHJldHVybiB7IG1lc3NhZ2U6ICdFeGNlbGxlbnQhIEdyZWF0IGpvYiEnLCB0eXBlOiAnZXhjZWxsZW50JyB9O1xuICB9IGVsc2UgaWYgKGRhdGEucGVyZmVjdFNjb3JlKSB7XG4gICAgcmV0dXJuIHsgbWVzc2FnZTogJ1BlcmZlY3QgU2NvcmUhIFdlbGwgZG9uZSEnLCB0eXBlOiAnZXhjZWxsZW50JyB9O1xuICB9IGVsc2UgaWYgKHRpbWVQZXJjZW50YWdlID4gMC41KSB7XG4gICAgcmV0dXJuIHsgbWVzc2FnZTogJ0dyZWF0IHdvcmshIEZhc3QgY29tcGxldGlvbiEnLCB0eXBlOiAnZ29vZCcgfTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4geyBtZXNzYWdlOiAnR2FtZSBDb21wbGV0ZSEgTmljZSBqb2IhJywgdHlwZTogJ2NvbXBsZXRlJyB9O1xuICB9XG59O1xuXG4vLyBQcm9jZXNzIGdhbWUgY29tcGxldGlvblxuZXhwb3J0IGNvbnN0IHByb2Nlc3NHYW1lQ29tcGxldGlvbiA9IChkYXRhOiBHYW1lQ29tcGxldGlvbkRhdGEpOiBDb21wbGV0aW9uUmVzdWx0ID0+IHtcbiAgY29uc3QgdGltZUJvbnVzID0gY2FsY3VsYXRlVGltZUJvbnVzKGRhdGEudGltZVJlbWFpbmluZywgZGF0YS50b3RhbFRpbWUsIGRhdGEuZGlmZmljdWx0eSk7XG4gIGNvbnN0IGZpbmFsU2NvcmUgPSBkYXRhLnNjb3JlICsgdGltZUJvbnVzO1xuICBjb25zdCB4cEdhaW5lZCA9IGNhbGN1bGF0ZVhQKGRhdGEpO1xuICBjb25zdCB7IG1lc3NhZ2UsIHR5cGUgfSA9IGdldENvbXBsZXRpb25NZXNzYWdlKGRhdGEpO1xuICBcbiAgLy8gRGV0ZXJtaW5lIGFjaGlldmVtZW50cyBiYXNlZCBvbiBwZXJmb3JtYW5jZVxuICBjb25zdCBhY2hpZXZlbWVudHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IHRpbWVQZXJjZW50YWdlID0gZGF0YS50aW1lUmVtYWluaW5nIC8gZGF0YS50b3RhbFRpbWU7XG4gIFxuICBpZiAoZGF0YS5wZXJmZWN0U2NvcmUpIHtcbiAgICBhY2hpZXZlbWVudHMucHVzaCgnUGVyZmVjdCBTY29yZScpO1xuICB9XG4gIFxuICBpZiAodGltZVBlcmNlbnRhZ2UgPiAwLjgpIHtcbiAgICBhY2hpZXZlbWVudHMucHVzaCgnU3BlZWQgRGVtb24nKTtcbiAgfVxuICBcbiAgaWYgKGRhdGEuZGlmZmljdWx0eSA9PT0gJ2hhcmQnICYmIGRhdGEucGVyZmVjdFNjb3JlKSB7XG4gICAgYWNoaWV2ZW1lbnRzLnB1c2goJ01hc3RlciBQbGF5ZXInKTtcbiAgfVxuICBcbiAgaWYgKHRpbWVCb251cyA+IDMwKSB7XG4gICAgYWNoaWV2ZW1lbnRzLnB1c2goJ1RpbWUgTWFzdGVyJyk7XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgZmluYWxTY29yZSxcbiAgICB0aW1lQm9udXMsXG4gICAgeHBHYWluZWQsXG4gICAgYWNoaWV2ZW1lbnRzLFxuICAgIG1lc3NhZ2UsXG4gICAgY2VsZWJyYXRpb25UeXBlOiB0eXBlXG4gIH07XG59O1xuXG4vLyBHYW1lLXNwZWNpZmljIGNvbXBsZXRpb24gY2hlY2tlcnNcbmV4cG9ydCBjb25zdCBjaGVja0dhbWVDb21wbGV0aW9uID0ge1xuICAnZmxhZy1tYXRjaGluZyc6IChtYXRjaGVzOiBudW1iZXIsIHRvdGFsUGFpcnM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBtYXRjaGVzID49IHRvdGFsUGFpcnM7XG4gIH0sXG4gIFxuICAnY291bnRyeS1uYW1lLXNjcmFtYmxlJzogKGN1cnJlbnRSb3VuZDogbnVtYmVyLCB0b3RhbFJvdW5kczogbnVtYmVyLCByb3VuZENvbXBsZXRlOiBib29sZWFuKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGN1cnJlbnRSb3VuZCA+PSB0b3RhbFJvdW5kcyAtIDEgJiYgcm91bmRDb21wbGV0ZTtcbiAgfSxcbiAgXG4gICdqaWdzYXctcHV6emxlJzogKHBsYWNlZFBpZWNlczogbnVtYmVyLCB0b3RhbFBpZWNlczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIHBsYWNlZFBpZWNlcyA+PSB0b3RhbFBpZWNlcztcbiAgfSxcbiAgXG4gICdxdWl6JzogKGFuc3dlcmVkUXVlc3Rpb25zOiBudW1iZXIsIHRvdGFsUXVlc3Rpb25zOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gYW5zd2VyZWRRdWVzdGlvbnMgPj0gdG90YWxRdWVzdGlvbnM7XG4gIH0sXG4gIFxuICAnbWVtb3J5LWdyaWQnOiAobWF0Y2hlZFBhaXJzOiBudW1iZXIsIHRvdGFsUGFpcnM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBtYXRjaGVkUGFpcnMgPj0gdG90YWxQYWlycztcbiAgfSxcbiAgXG4gICdtYXRjaGluZyc6IChtYXRjaGVkUGFpcnM6IG51bWJlciwgdG90YWxQYWlyczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIG1hdGNoZWRQYWlycyA+PSB0b3RhbFBhaXJzO1xuICB9LFxuICBcbiAgJ2NvdW50cnktZXhwbG9yZXInOiAodmlzaXRlZENvdW50cmllczogbnVtYmVyLCB0YXJnZXRDb3VudHJpZXM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiB2aXNpdGVkQ291bnRyaWVzID49IHRhcmdldENvdW50cmllcztcbiAgfSxcbiAgXG4gICdzcGVlZC1jaGFsbGVuZ2UnOiAoYW5zd2VyZWRRdWVzdGlvbnM6IG51bWJlciwgdGFyZ2V0UXVlc3Rpb25zOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gYW5zd2VyZWRRdWVzdGlvbnMgPj0gdGFyZ2V0UXVlc3Rpb25zO1xuICB9LFxuICBcbiAgJ215c3RlcnktbGFuZCc6IChjb3JyZWN0R3Vlc3NlczogbnVtYmVyLCB0b3RhbFJvdW5kczogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGNvcnJlY3RHdWVzc2VzID49IHRvdGFsUm91bmRzO1xuICB9LFxuICBcbiAgJ3RpbWVsaW5lLWJ1aWxkZXInOiAocGxhY2VkRXZlbnRzOiBudW1iZXIsIHRvdGFsRXZlbnRzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gcGxhY2VkRXZlbnRzID49IHRvdGFsRXZlbnRzO1xuICB9LFxuICBcbiAgJ3doZXJlLWluLWFmcmljYSc6IChjb21wbGV0ZWRSb3VuZHM6IG51bWJlciwgdG90YWxSb3VuZHM6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBjb21wbGV0ZWRSb3VuZHMgPj0gdG90YWxSb3VuZHM7XG4gIH0sXG4gIFxuICAnZHJlc3MtY2hhcmFjdGVyJzogKGNvbXBsZXRlZE91dGZpdHM6IG51bWJlciwgdGFyZ2V0T3V0Zml0czogbnVtYmVyKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGNvbXBsZXRlZE91dGZpdHMgPj0gdGFyZ2V0T3V0Zml0cztcbiAgfVxufTtcblxuLy8gQ2VsZWJyYXRpb24gYW5pbWF0aW9uIGNvbmZpZ3VyYXRpb25zXG5leHBvcnQgY29uc3QgY2VsZWJyYXRpb25Db25maWdzID0ge1xuICBwZXJmZWN0OiB7XG4gICAgZHVyYXRpb246IDMwMDAsXG4gICAgZW1vamk6ICfwn4+GJyxcbiAgICBjb2xvcnM6IFsnI0ZGRDcwMCcsICcjRkZBNTAwJywgJyNGRjZCNkInXSxcbiAgICBwYXJ0aWNsZXM6IDUwXG4gIH0sXG4gIGV4Y2VsbGVudDoge1xuICAgIGR1cmF0aW9uOiAyNTAwLFxuICAgIGVtb2ppOiAn8J+OiScsXG4gICAgY29sb3JzOiBbJyM0RUNEQzQnLCAnIzQ1QjdEMScsICcjOTZDRUI0J10sXG4gICAgcGFydGljbGVzOiA0MFxuICB9LFxuICBnb29kOiB7XG4gICAgZHVyYXRpb246IDIwMDAsXG4gICAgZW1vamk6ICfwn5GPJyxcbiAgICBjb2xvcnM6IFsnI0ZFQ0E1NycsICcjNDhDQUU0JywgJyNBOEU2Q0YnXSxcbiAgICBwYXJ0aWNsZXM6IDMwXG4gIH0sXG4gIGNvbXBsZXRlOiB7XG4gICAgZHVyYXRpb246IDE1MDAsXG4gICAgZW1vamk6ICfinIUnLFxuICAgIGNvbG9yczogWycjNkM1Q0U3JywgJyNBMjlCRkUnLCAnIzc0QjlGRiddLFxuICAgIHBhcnRpY2xlczogMjBcbiAgfVxufTtcblxuLy8gQXV0by1wcm9ncmVzc2lvbiBsb2dpY1xuZXhwb3J0IGNvbnN0IGdldE5leHRBY3Rpb24gPSAoZ2FtZVR5cGU6IEdhbWVUeXBlLCBkaWZmaWN1bHR5OiAnZWFzeScgfCAnbWVkaXVtJyB8ICdoYXJkJyk6IHsgYWN0aW9uOiAnbmV4dC1kaWZmaWN1bHR5JyB8ICdtZW51JyB8ICdyZXBsYXknOyBuZXh0RGlmZmljdWx0eT86ICdlYXN5JyB8ICdtZWRpdW0nIHwgJ2hhcmQnIH0gPT4ge1xuICAvLyBJZiBjb21wbGV0ZWQgb24gZWFzeSwgcHJvZ3Jlc3MgdG8gbWVkaXVtXG4gIGlmIChkaWZmaWN1bHR5ID09PSAnZWFzeScpIHtcbiAgICByZXR1cm4geyBhY3Rpb246ICduZXh0LWRpZmZpY3VsdHknLCBuZXh0RGlmZmljdWx0eTogJ21lZGl1bScgfTtcbiAgfVxuXG4gIC8vIElmIGNvbXBsZXRlZCBvbiBtZWRpdW0sIHByb2dyZXNzIHRvIGhhcmRcbiAgaWYgKGRpZmZpY3VsdHkgPT09ICdtZWRpdW0nKSB7XG4gICAgcmV0dXJuIHsgYWN0aW9uOiAnbmV4dC1kaWZmaWN1bHR5JywgbmV4dERpZmZpY3VsdHk6ICdoYXJkJyB9O1xuICB9XG5cbiAgLy8gSWYgY29tcGxldGVkIG9uIGhhcmQsIHJldHVybiB0byBtZW51XG4gIHJldHVybiB7IGFjdGlvbjogJ21lbnUnIH07XG59O1xuXG4vLyBFbmhhbmNlZCBjb21wbGV0aW9uIHJlc3VsdCB3aXRoIGF1dG8tcHJvZ3Jlc3Npb24gZGF0YVxuZXhwb3J0IGludGVyZmFjZSBFbmhhbmNlZENvbXBsZXRpb25SZXN1bHQgZXh0ZW5kcyBDb21wbGV0aW9uUmVzdWx0IHtcbiAgYXV0b1Byb2dyZXNzaW9uOiB7XG4gICAgYWN0aW9uOiAnbmV4dC1kaWZmaWN1bHR5JyB8ICdtZW51JyB8ICdyZXBsYXknO1xuICAgIG5leHREaWZmaWN1bHR5PzogJ2Vhc3knIHwgJ21lZGl1bScgfCAnaGFyZCc7XG4gICAgZGVsYXk6IG51bWJlcjsgLy8gbWlsbGlzZWNvbmRzIGJlZm9yZSBhdXRvLXByb2dyZXNzaW9uXG4gIH07XG59XG5cbi8vIFByb2Nlc3MgZ2FtZSBjb21wbGV0aW9uIHdpdGggYXV0by1wcm9ncmVzc2lvblxuZXhwb3J0IGNvbnN0IHByb2Nlc3NHYW1lQ29tcGxldGlvbldpdGhQcm9ncmVzc2lvbiA9IChkYXRhOiBHYW1lQ29tcGxldGlvbkRhdGEpOiBFbmhhbmNlZENvbXBsZXRpb25SZXN1bHQgPT4ge1xuICBjb25zdCBiYXNlUmVzdWx0ID0gcHJvY2Vzc0dhbWVDb21wbGV0aW9uKGRhdGEpO1xuICBjb25zdCBwcm9ncmVzc2lvbiA9IGdldE5leHRBY3Rpb24oZGF0YS5nYW1lVHlwZSwgZGF0YS5kaWZmaWN1bHR5KTtcblxuICAvLyBEZXRlcm1pbmUgZGVsYXkgYmFzZWQgb24gY2VsZWJyYXRpb24gdHlwZVxuICBjb25zdCBkZWxheSA9IHtcbiAgICBwZXJmZWN0OiAzMDAwLFxuICAgIGV4Y2VsbGVudDogMjUwMCxcbiAgICBnb29kOiAyMDAwLFxuICAgIGNvbXBsZXRlOiAxNTAwXG4gIH1bYmFzZVJlc3VsdC5jZWxlYnJhdGlvblR5cGVdO1xuXG4gIHJldHVybiB7XG4gICAgLi4uYmFzZVJlc3VsdCxcbiAgICBhdXRvUHJvZ3Jlc3Npb246IHtcbiAgICAgIC4uLnByb2dyZXNzaW9uLFxuICAgICAgZGVsYXlcbiAgICB9XG4gIH07XG59O1xuIl0sIm5hbWVzIjpbImNhbGN1bGF0ZVRpbWVCb251cyIsInRpbWVSZW1haW5pbmciLCJ0b3RhbFRpbWUiLCJkaWZmaWN1bHR5IiwidGltZVBlcmNlbnRhZ2UiLCJkaWZmaWN1bHR5TXVsdGlwbGllciIsImVhc3kiLCJtZWRpdW0iLCJoYXJkIiwiYmFzZUJvbnVzIiwiTWF0aCIsImZsb29yIiwiY2FsY3VsYXRlWFAiLCJkYXRhIiwiYmFzZVhQIiwic2NvcmVNdWx0aXBsaWVyIiwic2NvcmUiLCJ0aW1lQm9udXMiLCJkaWZmaWN1bHR5Qm9udXMiLCJwZXJmZWN0Qm9udXMiLCJwZXJmZWN0U2NvcmUiLCJnZXRDb21wbGV0aW9uTWVzc2FnZSIsIm1lc3NhZ2UiLCJ0eXBlIiwicHJvY2Vzc0dhbWVDb21wbGV0aW9uIiwiZmluYWxTY29yZSIsInhwR2FpbmVkIiwiYWNoaWV2ZW1lbnRzIiwicHVzaCIsImNlbGVicmF0aW9uVHlwZSIsImNoZWNrR2FtZUNvbXBsZXRpb24iLCJtYXRjaGVzIiwidG90YWxQYWlycyIsImN1cnJlbnRSb3VuZCIsInRvdGFsUm91bmRzIiwicm91bmRDb21wbGV0ZSIsInBsYWNlZFBpZWNlcyIsInRvdGFsUGllY2VzIiwiYW5zd2VyZWRRdWVzdGlvbnMiLCJ0b3RhbFF1ZXN0aW9ucyIsIm1hdGNoZWRQYWlycyIsInZpc2l0ZWRDb3VudHJpZXMiLCJ0YXJnZXRDb3VudHJpZXMiLCJ0YXJnZXRRdWVzdGlvbnMiLCJjb3JyZWN0R3Vlc3NlcyIsInBsYWNlZEV2ZW50cyIsInRvdGFsRXZlbnRzIiwiY29tcGxldGVkUm91bmRzIiwiY29tcGxldGVkT3V0Zml0cyIsInRhcmdldE91dGZpdHMiLCJjZWxlYnJhdGlvbkNvbmZpZ3MiLCJwZXJmZWN0IiwiZHVyYXRpb24iLCJlbW9qaSIsImNvbG9ycyIsInBhcnRpY2xlcyIsImV4Y2VsbGVudCIsImdvb2QiLCJjb21wbGV0ZSIsImdldE5leHRBY3Rpb24iLCJnYW1lVHlwZSIsImFjdGlvbiIsIm5leHREaWZmaWN1bHR5IiwicHJvY2Vzc0dhbWVDb21wbGV0aW9uV2l0aFByb2dyZXNzaW9uIiwiYmFzZVJlc3VsdCIsInByb2dyZXNzaW9uIiwiZGVsYXkiLCJhdXRvUHJvZ3Jlc3Npb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/gameCompletion.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = (newSeed = 1)=>{\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = (correct, total, timeBonus = 0, difficultyMultiplier = 1, streakBonus = 0)=>{\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = (countries, category, difficulty, count = 10)=>{\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the capital of ${country.name}?`,\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: `${country.capital} is the capital city of ${country.name}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the currency of ${country.name}?`,\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: `The currency of ${country.name} is ${country.currency}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `Which region is ${country.name} located in?`,\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: `${country.name} is located in ${country.region}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: `When did ${country.name} gain independence?`,\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: `${country.name} gained independence in ${new Date(country.independence).getFullYear()}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: `Which country is known for ${item}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${item} is a traditional ${aspect.slice(0, -1)} from ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: `Which country is home to ${animal}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${animal} can be found in ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: `${figure.name} is from which country?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${figure.name} is from ${country.name}. ${figure.achievement}`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n};\nconst formatScore = (score)=>{\n    return `${score}%`;\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/index.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=shuffleArray!=!./src/utils/index.ts":
/*!*********************************************************************!*\
  !*** __barrel_optimize__?names=shuffleArray!=!./src/utils/index.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   formatScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatScore),\n/* harmony export */   formatTime: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.shuffleArray),\n/* harmony export */   validateAnswer: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.validateAnswer)\n/* harmony export */ });\n/* harmony import */ var C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/index.ts */ \"(ssr)/./src/utils/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1zaHVmZmxlQXJyYXkhPSEuL3NyYy91dGlscy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUEwSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNRUVLIEVERU5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcR0FNRVMgRk9SIEFSSUNBXFxnYW1lcy1mb3ItYWZyaWNhXFxzcmNcXHV0aWxzXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXHNyY1xcXFx1dGlsc1xcXFxpbmRleC50c1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=shuffleArray!=!./src/utils/index.ts\n");

/***/ })

};
;