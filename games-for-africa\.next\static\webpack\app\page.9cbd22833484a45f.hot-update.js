"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/games/MemoryGrid */ \"(app-pages-browser)/./src/components/games/MemoryGrid.tsx\");\n/* harmony import */ var _components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/games/JigsawPuzzle */ \"(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\");\n/* harmony import */ var _components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/games/TimelineBuilder */ \"(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_10__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = (score)=>{\n        console.log('Game completed with score:', score);\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, this);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, this);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-accent-gold font-medium\",\n                                children: \"Score: 0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border border-gray-700 rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer\",\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: game.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⏱️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    game.estimatedTime,\n                                                                    \"min\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                children: game.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-4 flex-grow\",\n                                                children: game.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                        children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            game.maxScore,\n                                                            \" pts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                children: \"▶️ Play Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, game.type, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"g4JRP421rujMtt/9zwXe7LlkLmM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/games/TimelineBuilder.tsx":
/*!**************************************************!*\
  !*** ./src/components/games/TimelineBuilder.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst TimelineBuilder = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedEvent, setDraggedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const generateEvents = ()=>{\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, 6);\n        const timelineEvents = [];\n        selectedCountries.forEach((country, index)=>{\n            const independenceYear = new Date(country.independence).getFullYear();\n            // Skip countries with invalid dates\n            if (isNaN(independenceYear) || independenceYear < 1800) return;\n            timelineEvents.push({\n                id: \"event-\".concat(country.id),\n                country: country.name,\n                event: 'Independence',\n                year: independenceYear,\n                description: \"\".concat(country.name, \" gained independence\"),\n                placed: false,\n                position: -1\n            });\n        });\n        // Add some additional historical events\n        const additionalEvents = [\n            {\n                id: 'suez-canal',\n                country: 'Egypt',\n                event: 'Suez Canal Opens',\n                year: 1869,\n                description: 'The Suez Canal opened, connecting the Mediterranean and Red Seas',\n                placed: false,\n                position: -1\n            },\n            {\n                id: 'apartheid-end',\n                country: 'South Africa',\n                event: 'End of Apartheid',\n                year: 1994,\n                description: 'Nelson Mandela became the first Black president of South Africa',\n                placed: false,\n                position: -1\n            },\n            {\n                id: 'oau-formation',\n                country: 'Ethiopia',\n                event: 'OAU Formation',\n                year: 1963,\n                description: 'Organization of African Unity was formed in Addis Ababa',\n                placed: false,\n                position: -1\n            }\n        ];\n        const allEvents = [\n            ...timelineEvents,\n            ...additionalEvents\n        ].slice(0, 8);\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(allEvents);\n    };\n    const startGame = ()=>{\n        const newEvents = generateEvents();\n        setEvents(newEvents);\n        setTimeline(new Array(newEvents.length).fill(null));\n        setScore(0);\n        setMoves(0);\n        setGameComplete(false);\n        setGameStarted(true);\n        setShowHints(false);\n    };\n    const handleDragStart = (e, eventId)=>{\n        setDraggedEvent(eventId);\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, position)=>{\n        e.preventDefault();\n        if (!draggedEvent) return;\n        const event = events.find((e)=>e.id === draggedEvent);\n        if (!event || event.placed) return;\n        setMoves(moves + 1);\n        // Place the event in the timeline\n        const newTimeline = [\n            ...timeline\n        ];\n        // Remove event from its current position if it was already placed\n        const currentPos = newTimeline.findIndex((e)=>(e === null || e === void 0 ? void 0 : e.id) === draggedEvent);\n        if (currentPos !== -1) {\n            newTimeline[currentPos] = null;\n        }\n        // Place event in new position\n        newTimeline[position] = event;\n        setTimeline(newTimeline);\n        // Update event as placed\n        setEvents((prev)=>prev.map((e)=>e.id === draggedEvent ? {\n                    ...e,\n                    placed: true,\n                    position\n                } : e));\n        setDraggedEvent(null);\n        // Check if timeline is complete and correct\n        if (newTimeline.every((e)=>e !== null)) {\n            checkTimelineCorrectness(newTimeline);\n        }\n    };\n    const checkTimelineCorrectness = (completedTimeline)=>{\n        const sortedEvents = [\n            ...completedTimeline\n        ].sort((a, b)=>a.year - b.year);\n        let correctPlacements = 0;\n        completedTimeline.forEach((event, index)=>{\n            if (event.id === sortedEvents[index].id) {\n                correctPlacements++;\n            }\n        });\n        const accuracy = correctPlacements / completedTimeline.length * 100;\n        const finalScore = Math.round(accuracy + Math.max(0, 50 - moves));\n        setScore(finalScore);\n        setGameComplete(true);\n        setTimeout(()=>onComplete(finalScore), 1500);\n    };\n    const removeFromTimeline = (position)=>{\n        const event = timeline[position];\n        if (!event) return;\n        const newTimeline = [\n            ...timeline\n        ];\n        newTimeline[position] = null;\n        setTimeline(newTimeline);\n        setEvents((prev)=>prev.map((e)=>e.id === event.id ? {\n                    ...e,\n                    placed: false,\n                    position: -1\n                } : e));\n    };\n    const getSortedEvents = ()=>{\n        return [\n            ...events\n        ].sort((a, b)=>a.year - b.year);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDCDA Timeline Builder\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"⏰\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Arrange historical events from African countries in chronological order!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Drag events to arrange them chronologically\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Events include independence dates and major milestones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Get the correct order for maximum points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints if you need help with dates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Timeline\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDCDA Timeline Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowHints(!showHints),\n                                    className: \"px-4 py-2 rounded-lg transition-colors \".concat(showHints ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                    children: [\n                                        \"\\uD83D\\uDCA1 \",\n                                        showHints ? 'Hide' : 'Show',\n                                        \" Hints\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: moves\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Moves\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: [\n                                            timeline.filter((e)=>e !== null).length,\n                                            \"/\",\n                                            events.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Placed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"⏰ Timeline (Earliest → Latest)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: timeline.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-h-[80px] border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-200 \".concat(event ? 'bg-blue-500 bg-opacity-20 border-blue-400' : 'border-gray-600 hover:border-yellow-400'),\n                                            onDragOver: handleDragOver,\n                                            onDrop: (e)=>handleDrop(e, index),\n                                            children: event ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between w-full p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-semibold\",\n                                                                children: event.event\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: event.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-yellow-400 text-sm mt-1\",\n                                                                children: [\n                                                                    \"Year: \",\n                                                                    event.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeFromTimeline(index),\n                                                        className: \"text-red-400 hover:text-red-300 ml-4\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-500 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: \"Drop event here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Position \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"\\uD83D\\uDCCB Historical Events\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4 max-h-[600px] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: events.filter((e)=>!e.placed).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                draggable: true,\n                                                onDragStart: (e)=>handleDragStart(e, event.id),\n                                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-3 cursor-move hover:border-yellow-400 transition-all duration-200 \".concat(draggedEvent === event.id ? 'opacity-50' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: event.event\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300 text-sm\",\n                                                        children: event.country\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs mt-1\",\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-yellow-400 text-sm mt-2\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCC5 \",\n                                                            event.year\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    events.filter((e)=>!e.placed).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-400 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"All events placed!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-yellow-400 font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCA1 Correct Order:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm\",\n                                        children: getSortedEvents().map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    index + 1,\n                                                    \". \",\n                                                    event.event,\n                                                    \" (\",\n                                                    event.year,\n                                                    \")\"\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 80 ? '🏆' : score >= 60 ? '🎉' : '📚'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Timeline Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Moves: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: moves\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: [\n                                                            score,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: score >= 80 ? 'Perfect historian!' : score >= 60 ? 'Great job!' : 'Keep studying history!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\TimelineBuilder.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimelineBuilder, \"1qbbfbVkHKbrJfJScGRkEVWdU4o=\");\n_c = TimelineBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimelineBuilder);\nvar _c;\n$RefreshReg$(_c, \"TimelineBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\n"));

/***/ })

});