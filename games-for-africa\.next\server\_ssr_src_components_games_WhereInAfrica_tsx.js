"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_games_WhereInAfrica_tsx";
exports.ids = ["_ssr_src_components_games_WhereInAfrica_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/games/WhereInAfrica.tsx":
/*!************************************************!*\
  !*** ./src/components/games/WhereInAfrica.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=getRandomItems,shuffleArray!=!@/utils */ \"(ssr)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(ssr)/./src/components/ui/FlagImage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst WhereInAfrica = ({ countries, onComplete })=>{\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAnswer, setSelectedAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [correctAnswers, setCorrectAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalRounds = 10;\n    const generateClue = (country)=>{\n        const clueTypes = [\n            {\n                type: 'landmark',\n                items: country.landmarks\n            },\n            {\n                type: 'food',\n                items: country.culturalElements.cuisine\n            },\n            {\n                type: 'culture',\n                items: country.culturalElements.music\n            },\n            {\n                type: 'wildlife',\n                items: country.wildlife\n            },\n            {\n                type: 'export',\n                items: country.exports\n            }\n        ];\n        const availableClues = clueTypes.filter((ct)=>ct.items.length > 0);\n        const selectedClueType = availableClues[Math.floor(Math.random() * availableClues.length)];\n        const selectedItem = selectedClueType.items[Math.floor(Math.random() * selectedClueType.items.length)];\n        return {\n            type: selectedClueType.type,\n            clue: selectedItem\n        };\n    };\n    const generateRounds = ()=>{\n        const selectedCountries = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries, totalRounds);\n        const gameRounds = [];\n        selectedCountries.forEach((country)=>{\n            const { type, clue } = generateClue(country);\n            // Generate wrong options\n            const wrongCountries = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.getRandomItems)(countries.filter((c)=>c.id !== country.id), 3);\n            const options = (0,_barrel_optimize_names_getRandomItems_shuffleArray_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)([\n                country.name,\n                ...wrongCountries.map((c)=>c.name)\n            ]);\n            gameRounds.push({\n                country,\n                clueType: type,\n                clue,\n                options,\n                correctAnswer: country.name\n            });\n        });\n        return gameRounds;\n    };\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setCorrectAnswers(0);\n        setSelectedAnswer(null);\n        setShowResult(false);\n        setGameComplete(false);\n        setGameStarted(true);\n    };\n    const handleAnswerSelect = (answer)=>{\n        if (selectedAnswer) return;\n        setSelectedAnswer(answer);\n        setShowResult(true);\n        const isCorrect = answer === rounds[currentRound].correctAnswer;\n        if (isCorrect) {\n            setCorrectAnswers(correctAnswers + 1);\n            setScore(score + 10);\n        }\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setSelectedAnswer(null);\n                setShowResult(false);\n            } else {\n                setGameComplete(true);\n                const finalScore = Math.round((correctAnswers + (isCorrect ? 1 : 0)) / totalRounds * 100);\n                setTimeout(()=>onComplete(finalScore), 1000);\n            }\n        }, 2000);\n    };\n    const getClueIcon = (type)=>{\n        switch(type){\n            case 'landmark':\n                return '🏛️';\n            case 'food':\n                return '🍽️';\n            case 'culture':\n                return '🎵';\n            case 'wildlife':\n                return '🦁';\n            case 'export':\n                return '📦';\n            default:\n                return '❓';\n        }\n    };\n    const getClueDescription = (type)=>{\n        switch(type){\n            case 'landmark':\n                return 'Famous landmark';\n            case 'food':\n                return 'Traditional dish';\n            case 'culture':\n                return 'Musical style';\n            case 'wildlife':\n                return 'Native animal';\n            case 'export':\n                return 'Major export';\n            default:\n                return 'Clue';\n        }\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDDFA️ Where in Africa?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Guess the African country from clues about its landmarks, food, culture, and more!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• \",\n                                    totalRounds,\n                                    \" rounds of cultural clues\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Clues include landmarks, food, music, wildlife, and exports\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Multiple choice answers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Score points for correct guesses\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Adventure\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined);\n    }\n    const round = rounds[currentRound];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDDFA️ Where in Africa?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    \"Round \",\n                                    currentRound + 1,\n                                    \"/\",\n                                    totalRounds\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: correctAnswers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Correct\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Current\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: [\n                                            Math.round(correctAnswers / Math.max(currentRound, 1) * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Accuracy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${(currentRound + 1) / totalRounds * 100}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: getClueIcon(round.clueType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Which African country is known for this?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 bg-opacity-10 border border-yellow-400 rounded-lg p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-yellow-400 text-sm font-medium mb-1\",\n                                        children: getClueDescription(round.clueType)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white text-xl font-bold\",\n                                        children: [\n                                            '\"',\n                                            round.clue,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: round.options.map((option, index)=>{\n                            const isSelected = selectedAnswer === option;\n                            const isCorrect = option === round.correctAnswer;\n                            const isIncorrect = isSelected && !isCorrect && showResult;\n                            let optionClass = 'bg-gray-700 border border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-yellow-400';\n                            if (showResult) {\n                                if (isCorrect) optionClass = 'bg-green-500 bg-opacity-20 border-green-400 rounded-lg p-4';\n                                else if (isIncorrect) optionClass = 'bg-red-500 bg-opacity-20 border-red-400 rounded-lg p-4';\n                            } else if (isSelected) {\n                                optionClass = 'bg-yellow-400 bg-opacity-10 border-yellow-400 rounded-lg p-4';\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: optionClass,\n                                onClick: ()=>handleAnswerSelect(option),\n                                disabled: showResult,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-left text-white font-medium\",\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        showResult && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 text-xl\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        showResult && isIncorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400 text-xl\",\n                                            children: \"✗\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-gray-700 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    countryId: round.country.id,\n                                    size: \"large\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: round.country.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-2\",\n                                            children: [\n                                                \"Capital: \",\n                                                round.country.capital,\n                                                \" | Region: \",\n                                                round.country.region\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                round.clueType === 'landmark' && `${round.clue} is one of the famous landmarks in ${round.country.name}.`,\n                                                round.clueType === 'food' && `${round.clue} is a traditional dish from ${round.country.name}.`,\n                                                round.clueType === 'culture' && `${round.clue} is a popular music style in ${round.country.name}.`,\n                                                round.clueType === 'wildlife' && `${round.clue} can be found in the wild areas of ${round.country.name}.`,\n                                                round.clueType === 'export' && `${round.clue} is one of the major exports of ${round.country.name}.`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: correctAnswers >= 8 ? '🏆' : correctAnswers >= 6 ? '🎉' : '🗺️'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Adventure Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Correct Answers: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: correctAnswers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 39\n                                                    }, undefined),\n                                                    \"/\",\n                                                    totalRounds\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: [\n                                                            Math.round(correctAnswers / totalRounds * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: correctAnswers >= 8 ? 'Africa Expert!' : correctAnswers >= 6 ? 'Great Explorer!' : 'Keep Exploring!'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Explore Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\WhereInAfrica.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhereInAfrica);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9nYW1lcy9XaGVyZUluQWZyaWNhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVtRDtBQUVJO0FBQ0w7QUFlbEQsTUFBTUssZ0JBQThDLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUU7SUFDNUUsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1IsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDUyxRQUFRQyxVQUFVLEdBQUdWLCtDQUFRQSxDQUFjLEVBQUU7SUFDcEQsTUFBTSxDQUFDVyxnQkFBZ0JDLGtCQUFrQixHQUFHWiwrQ0FBUUEsQ0FBZ0I7SUFDcEUsTUFBTSxDQUFDYSxZQUFZQyxjQUFjLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2UsT0FBT0MsU0FBUyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDaUIsZ0JBQWdCQyxrQkFBa0IsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ21CLGNBQWNDLGdCQUFnQixHQUFHcEIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDcUIsYUFBYUMsZUFBZSxHQUFHdEIsK0NBQVFBLENBQUM7SUFFL0MsTUFBTXVCLGNBQWM7SUFFcEIsTUFBTUMsZUFBZSxDQUFDQztRQUNwQixNQUFNQyxZQUFxRTtZQUN6RTtnQkFBRUMsTUFBTTtnQkFBWUMsT0FBT0gsUUFBUUksU0FBUztZQUFDO1lBQzdDO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSCxRQUFRSyxnQkFBZ0IsQ0FBQ0MsT0FBTztZQUFDO1lBQ3hEO2dCQUFFSixNQUFNO2dCQUFXQyxPQUFPSCxRQUFRSyxnQkFBZ0IsQ0FBQ0UsS0FBSztZQUFDO1lBQ3pEO2dCQUFFTCxNQUFNO2dCQUFZQyxPQUFPSCxRQUFRUSxRQUFRO1lBQUM7WUFDNUM7Z0JBQUVOLE1BQU07Z0JBQVVDLE9BQU9ILFFBQVFTLE9BQU87WUFBQztTQUMxQztRQUVELE1BQU1DLGlCQUFpQlQsVUFBVVUsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHVCxLQUFLLENBQUNVLE1BQU0sR0FBRztRQUNoRSxNQUFNQyxtQkFBbUJKLGNBQWMsQ0FBQ0ssS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtQLGVBQWVHLE1BQU0sRUFBRTtRQUMxRixNQUFNSyxlQUFlSixpQkFBaUJYLEtBQUssQ0FBQ1ksS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtILGlCQUFpQlgsS0FBSyxDQUFDVSxNQUFNLEVBQUU7UUFFdEcsT0FBTztZQUNMWCxNQUFNWSxpQkFBaUJaLElBQUk7WUFDM0JpQixNQUFNRDtRQUNSO0lBQ0Y7SUFFQSxNQUFNRSxpQkFBaUI7UUFDckIsTUFBTUMsb0JBQW9CN0Msd0dBQWNBLENBQUNJLFdBQVdrQjtRQUNwRCxNQUFNd0IsYUFBMEIsRUFBRTtRQUVsQ0Qsa0JBQWtCRSxPQUFPLENBQUMsQ0FBQ3ZCO1lBQ3pCLE1BQU0sRUFBRUUsSUFBSSxFQUFFaUIsSUFBSSxFQUFFLEdBQUdwQixhQUFhQztZQUVwQyx5QkFBeUI7WUFDekIsTUFBTXdCLGlCQUFpQmhELHdHQUFjQSxDQUNuQ0ksVUFBVStCLE1BQU0sQ0FBQ2MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLMUIsUUFBUTBCLEVBQUUsR0FDekM7WUFHRixNQUFNQyxVQUFVbEQsc0dBQVlBLENBQUM7Z0JBQzNCdUIsUUFBUTRCLElBQUk7bUJBQ1RKLGVBQWVLLEdBQUcsQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRUcsSUFBSTthQUNsQztZQUVETixXQUFXUSxJQUFJLENBQUM7Z0JBQ2Q5QjtnQkFDQStCLFVBQVU3QjtnQkFDVmlCO2dCQUNBUTtnQkFDQUssZUFBZWhDLFFBQVE0QixJQUFJO1lBQzdCO1FBQ0Y7UUFFQSxPQUFPTjtJQUNUO0lBRUEsTUFBTVcsWUFBWTtRQUNoQixNQUFNQyxZQUFZZDtRQUNsQm5DLFVBQVVpRDtRQUNWbkQsZ0JBQWdCO1FBQ2hCUSxTQUFTO1FBQ1RFLGtCQUFrQjtRQUNsQk4sa0JBQWtCO1FBQ2xCRSxjQUFjO1FBQ2RNLGdCQUFnQjtRQUNoQkUsZUFBZTtJQUNqQjtJQUVBLE1BQU1zQyxxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSWxELGdCQUFnQjtRQUVwQkMsa0JBQWtCaUQ7UUFDbEIvQyxjQUFjO1FBRWQsTUFBTWdELFlBQVlELFdBQVdwRCxNQUFNLENBQUNGLGFBQWEsQ0FBQ2tELGFBQWE7UUFFL0QsSUFBSUssV0FBVztZQUNiNUMsa0JBQWtCRCxpQkFBaUI7WUFDbkNELFNBQVNELFFBQVE7UUFDbkI7UUFFQWdELFdBQVc7WUFDVCxJQUFJeEQsZUFBZWdCLGNBQWMsR0FBRztnQkFDbENmLGdCQUFnQkQsZUFBZTtnQkFDL0JLLGtCQUFrQjtnQkFDbEJFLGNBQWM7WUFDaEIsT0FBTztnQkFDTE0sZ0JBQWdCO2dCQUNoQixNQUFNNEMsYUFBYXhCLEtBQUt5QixLQUFLLENBQUMsQ0FBQ2hELGlCQUFrQjZDLENBQUFBLFlBQVksSUFBSSxFQUFDLElBQUt2QyxjQUFjO2dCQUNyRndDLFdBQVcsSUFBTXpELFdBQVcwRCxhQUFhO1lBQzNDO1FBQ0YsR0FBRztJQUNMO0lBRUEsTUFBTUUsY0FBYyxDQUFDdkM7UUFDbkIsT0FBUUE7WUFDTixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU13QyxxQkFBcUIsQ0FBQ3hDO1FBQzFCLE9BQVFBO1lBQ04sS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJLENBQUNOLGFBQWE7UUFDaEIscUJBQ0UsOERBQUMrQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFxQzs7Ozs7O2tDQUNuRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQWdCOzs7Ozs7a0NBQy9CLDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBNkI7Ozs7OztrQ0FJMUMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7O29DQUFFO29DQUFHaEQ7b0NBQVk7Ozs7Ozs7MENBQ2xCLDhEQUFDZ0Q7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7Ozs7Ozs7OztrQ0FHTCw4REFBQ0M7d0JBQ0NDLFNBQVNmO3dCQUNUVyxXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztJQU1UO0lBRUEsSUFBSSxDQUFDNUQsTUFBTSxDQUFDRixhQUFhLEVBQUU7UUFDekIscUJBQ0UsOERBQUM2RDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxNQUFNSixRQUFReEQsTUFBTSxDQUFDRixhQUFhO0lBRWxDLHFCQUNFLDhEQUFDNkQ7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBR0QsV0FBVTswQ0FBZ0M7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOztvQ0FBb0M7b0NBQzFDOUQsZUFBZTtvQ0FBRTtvQ0FBRWdCOzs7Ozs7Ozs7Ozs7O2tDQUk5Qiw4REFBQzZDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBc0N0RDs7Ozs7O2tEQUNyRCw4REFBQ3FEO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBcUNwRDs7Ozs7O2tEQUNwRCw4REFBQ21EO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBb0M5RCxlQUFlOzs7Ozs7a0RBQ2xFLDhEQUFDNkQ7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBRXpDLDhEQUFDRDs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVOzs0Q0FDWjdCLEtBQUt5QixLQUFLLENBQUMsaUJBQWtCekIsS0FBS2tDLEdBQUcsQ0FBQ25FLGNBQWMsS0FBTTs0Q0FBSzs7Ozs7OztrREFFbEUsOERBQUM2RDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLM0MsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQ0NDLFdBQVU7Z0NBQ1ZNLE9BQU87b0NBQUVDLE9BQU8sR0FBRyxDQUFFckUsZUFBZSxLQUFLZ0IsY0FBZSxJQUFJLENBQUMsQ0FBQztnQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkUsOERBQUM2QztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQWlCSCxZQUFZRCxNQUFNVCxRQUFROzs7Ozs7MENBQzFELDhEQUFDcUI7Z0NBQUdSLFdBQVU7MENBQXFDOzs7Ozs7MENBR25ELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaRixtQkFBbUJGLE1BQU1ULFFBQVE7Ozs7OztrREFFcEMsOERBQUNZO3dDQUFJQyxXQUFVOzs0Q0FBK0I7NENBQzFDSixNQUFNckIsSUFBSTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbkIsOERBQUN3Qjt3QkFBSUMsV0FBVTtrQ0FDWkosTUFBTWIsT0FBTyxDQUFDRSxHQUFHLENBQUMsQ0FBQ3dCLFFBQVFDOzRCQUMxQixNQUFNQyxhQUFhckUsbUJBQW1CbUU7NEJBQ3RDLE1BQU1oQixZQUFZZ0IsV0FBV2IsTUFBTVIsYUFBYTs0QkFDaEQsTUFBTXdCLGNBQWNELGNBQWMsQ0FBQ2xCLGFBQWFqRDs0QkFFaEQsSUFBSXFFLGNBQWM7NEJBQ2xCLElBQUlyRSxZQUFZO2dDQUNkLElBQUlpRCxXQUFXb0IsY0FBYztxQ0FDeEIsSUFBSUQsYUFBYUMsY0FBYzs0QkFDdEMsT0FBTyxJQUFJRixZQUFZO2dDQUNyQkUsY0FBYzs0QkFDaEI7NEJBRUEscUJBQ0UsOERBQUNWO2dDQUVDSCxXQUFXYTtnQ0FDWFQsU0FBUyxJQUFNYixtQkFBbUJrQjtnQ0FDbENLLFVBQVV0RTswQ0FFViw0RUFBQ3VEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2U7NENBQUtmLFdBQVU7c0RBQW9DUzs7Ozs7O3dDQUNuRGpFLGNBQWNpRCwyQkFDYiw4REFBQ3NCOzRDQUFLZixXQUFVO3NEQUF5Qjs7Ozs7O3dDQUUxQ3hELGNBQWNvRSw2QkFDYiw4REFBQ0c7NENBQUtmLFdBQVU7c0RBQXVCOzs7Ozs7Ozs7Ozs7K0JBWHRDVTs7Ozs7d0JBZ0JYOzs7Ozs7b0JBSURsRSw0QkFDQyw4REFBQ3VEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNsRSxnRUFBU0E7b0NBQ1JrRixXQUFXcEIsTUFBTXhDLE9BQU8sQ0FBQzBCLEVBQUU7b0NBQzNCbUMsTUFBSzs7Ozs7OzhDQUVQLDhEQUFDbEI7O3NEQUNDLDhEQUFDbUI7NENBQUdsQixXQUFVO3NEQUFnQ0osTUFBTXhDLE9BQU8sQ0FBQzRCLElBQUk7Ozs7OztzREFDaEUsOERBQUNrQjs0Q0FBRUYsV0FBVTs7Z0RBQTZCO2dEQUM5QkosTUFBTXhDLE9BQU8sQ0FBQytELE9BQU87Z0RBQUM7Z0RBQVl2QixNQUFNeEMsT0FBTyxDQUFDZ0UsTUFBTTs7Ozs7OztzREFFbEUsOERBQUNsQjs0Q0FBRUYsV0FBVTs7Z0RBQ1ZKLE1BQU1ULFFBQVEsS0FBSyxjQUFjLEdBQUdTLE1BQU1yQixJQUFJLENBQUMsbUNBQW1DLEVBQUVxQixNQUFNeEMsT0FBTyxDQUFDNEIsSUFBSSxDQUFDLENBQUMsQ0FBQztnREFDekdZLE1BQU1ULFFBQVEsS0FBSyxVQUFVLEdBQUdTLE1BQU1yQixJQUFJLENBQUMsNEJBQTRCLEVBQUVxQixNQUFNeEMsT0FBTyxDQUFDNEIsSUFBSSxDQUFDLENBQUMsQ0FBQztnREFDOUZZLE1BQU1ULFFBQVEsS0FBSyxhQUFhLEdBQUdTLE1BQU1yQixJQUFJLENBQUMsNkJBQTZCLEVBQUVxQixNQUFNeEMsT0FBTyxDQUFDNEIsSUFBSSxDQUFDLENBQUMsQ0FBQztnREFDbEdZLE1BQU1ULFFBQVEsS0FBSyxjQUFjLEdBQUdTLE1BQU1yQixJQUFJLENBQUMsbUNBQW1DLEVBQUVxQixNQUFNeEMsT0FBTyxDQUFDNEIsSUFBSSxDQUFDLENBQUMsQ0FBQztnREFDekdZLE1BQU1ULFFBQVEsS0FBSyxZQUFZLEdBQUdTLE1BQU1yQixJQUFJLENBQUMsZ0NBQWdDLEVBQUVxQixNQUFNeEMsT0FBTyxDQUFDNEIsSUFBSSxDQUFDLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU2hIbEMsOEJBQ0MsOERBQUNpRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pwRCxrQkFBa0IsSUFBSSxPQUFPQSxrQkFBa0IsSUFBSSxPQUFPOzs7Ozs7MENBRzdELDhEQUFDbUQ7O2tEQUNDLDhEQUFDUzt3Q0FBR1IsV0FBVTtrREFBMEM7Ozs7OztrREFHeEQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7O29EQUFFO2tFQUFpQiw4REFBQ2E7d0RBQUtmLFdBQVU7a0VBQWtCcEQ7Ozs7OztvREFBc0I7b0RBQUVNOzs7Ozs7OzBEQUM5RSw4REFBQ2dEOztvREFBRTtrRUFBYSw4REFBQ2E7d0RBQUtmLFdBQVU7OzREQUE2QjdCLEtBQUt5QixLQUFLLENBQUMsaUJBQWtCMUMsY0FBZTs0REFBSzs7Ozs7Ozs7Ozs7OzswREFDOUcsOERBQUNnRDtnREFBRUYsV0FBVTswREFDVnBELGtCQUFrQixJQUFJLG1CQUN0QkEsa0JBQWtCLElBQUksb0JBQ3RCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS1AsOERBQUNtRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0c7b0NBQ0NDLFNBQVNmO29DQUNUVyxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVakI7QUFFQSxpRUFBZWpFLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTUVFSyBFREVOXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXEdBTUVTIEZPUiBBUklDQVxcZ2FtZXMtZm9yLWFmcmljYVxcc3JjXFxjb21wb25lbnRzXFxnYW1lc1xcV2hlcmVJbkFmcmljYS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENvdW50cnkgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IGdldFJhbmRvbUl0ZW1zLCBzaHVmZmxlQXJyYXkgfSBmcm9tICdAL3V0aWxzJztcbmltcG9ydCBGbGFnSW1hZ2UgZnJvbSAnQC9jb21wb25lbnRzL3VpL0ZsYWdJbWFnZSc7XG5cbmludGVyZmFjZSBXaGVyZUluQWZyaWNhUHJvcHMge1xuICBjb3VudHJpZXM6IENvdW50cnlbXTtcbiAgb25Db21wbGV0ZTogKHNjb3JlOiBudW1iZXIpID0+IHZvaWQ7XG59XG5cbmludGVyZmFjZSBHYW1lUm91bmQge1xuICBjb3VudHJ5OiBDb3VudHJ5O1xuICBjbHVlVHlwZTogJ2xhbmRtYXJrJyB8ICdmb29kJyB8ICdjdWx0dXJlJyB8ICd3aWxkbGlmZScgfCAnZXhwb3J0JztcbiAgY2x1ZTogc3RyaW5nO1xuICBvcHRpb25zOiBzdHJpbmdbXTtcbiAgY29ycmVjdEFuc3dlcjogc3RyaW5nO1xufVxuXG5jb25zdCBXaGVyZUluQWZyaWNhOiBSZWFjdC5GQzxXaGVyZUluQWZyaWNhUHJvcHM+ID0gKHsgY291bnRyaWVzLCBvbkNvbXBsZXRlIH0pID0+IHtcbiAgY29uc3QgW2N1cnJlbnRSb3VuZCwgc2V0Q3VycmVudFJvdW5kXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbcm91bmRzLCBzZXRSb3VuZHNdID0gdXNlU3RhdGU8R2FtZVJvdW5kW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkQW5zd2VyLCBzZXRTZWxlY3RlZEFuc3dlcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Nob3dSZXN1bHQsIHNldFNob3dSZXN1bHRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2NvcmUsIHNldFNjb3JlXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbY29ycmVjdEFuc3dlcnMsIHNldENvcnJlY3RBbnN3ZXJzXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbZ2FtZUNvbXBsZXRlLCBzZXRHYW1lQ29tcGxldGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZ2FtZVN0YXJ0ZWQsIHNldEdhbWVTdGFydGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCB0b3RhbFJvdW5kcyA9IDEwO1xuXG4gIGNvbnN0IGdlbmVyYXRlQ2x1ZSA9IChjb3VudHJ5OiBDb3VudHJ5KTogeyB0eXBlOiBHYW1lUm91bmRbJ2NsdWVUeXBlJ107IGNsdWU6IHN0cmluZyB9ID0+IHtcbiAgICBjb25zdCBjbHVlVHlwZXM6IEFycmF5PHsgdHlwZTogR2FtZVJvdW5kWydjbHVlVHlwZSddOyBpdGVtczogc3RyaW5nW10gfT4gPSBbXG4gICAgICB7IHR5cGU6ICdsYW5kbWFyaycsIGl0ZW1zOiBjb3VudHJ5LmxhbmRtYXJrcyB9LFxuICAgICAgeyB0eXBlOiAnZm9vZCcsIGl0ZW1zOiBjb3VudHJ5LmN1bHR1cmFsRWxlbWVudHMuY3Vpc2luZSB9LFxuICAgICAgeyB0eXBlOiAnY3VsdHVyZScsIGl0ZW1zOiBjb3VudHJ5LmN1bHR1cmFsRWxlbWVudHMubXVzaWMgfSxcbiAgICAgIHsgdHlwZTogJ3dpbGRsaWZlJywgaXRlbXM6IGNvdW50cnkud2lsZGxpZmUgfSxcbiAgICAgIHsgdHlwZTogJ2V4cG9ydCcsIGl0ZW1zOiBjb3VudHJ5LmV4cG9ydHMgfSxcbiAgICBdO1xuXG4gICAgY29uc3QgYXZhaWxhYmxlQ2x1ZXMgPSBjbHVlVHlwZXMuZmlsdGVyKGN0ID0+IGN0Lml0ZW1zLmxlbmd0aCA+IDApO1xuICAgIGNvbnN0IHNlbGVjdGVkQ2x1ZVR5cGUgPSBhdmFpbGFibGVDbHVlc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBhdmFpbGFibGVDbHVlcy5sZW5ndGgpXTtcbiAgICBjb25zdCBzZWxlY3RlZEl0ZW0gPSBzZWxlY3RlZENsdWVUeXBlLml0ZW1zW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHNlbGVjdGVkQ2x1ZVR5cGUuaXRlbXMubGVuZ3RoKV07XG5cbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogc2VsZWN0ZWRDbHVlVHlwZS50eXBlLFxuICAgICAgY2x1ZTogc2VsZWN0ZWRJdGVtLFxuICAgIH07XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVSb3VuZHMgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2VsZWN0ZWRDb3VudHJpZXMgPSBnZXRSYW5kb21JdGVtcyhjb3VudHJpZXMsIHRvdGFsUm91bmRzKTtcbiAgICBjb25zdCBnYW1lUm91bmRzOiBHYW1lUm91bmRbXSA9IFtdO1xuXG4gICAgc2VsZWN0ZWRDb3VudHJpZXMuZm9yRWFjaCgoY291bnRyeSkgPT4ge1xuICAgICAgY29uc3QgeyB0eXBlLCBjbHVlIH0gPSBnZW5lcmF0ZUNsdWUoY291bnRyeSk7XG4gICAgICBcbiAgICAgIC8vIEdlbmVyYXRlIHdyb25nIG9wdGlvbnNcbiAgICAgIGNvbnN0IHdyb25nQ291bnRyaWVzID0gZ2V0UmFuZG9tSXRlbXMoXG4gICAgICAgIGNvdW50cmllcy5maWx0ZXIoYyA9PiBjLmlkICE9PSBjb3VudHJ5LmlkKSxcbiAgICAgICAgM1xuICAgICAgKTtcbiAgICAgIFxuICAgICAgY29uc3Qgb3B0aW9ucyA9IHNodWZmbGVBcnJheShbXG4gICAgICAgIGNvdW50cnkubmFtZSxcbiAgICAgICAgLi4ud3JvbmdDb3VudHJpZXMubWFwKGMgPT4gYy5uYW1lKVxuICAgICAgXSk7XG5cbiAgICAgIGdhbWVSb3VuZHMucHVzaCh7XG4gICAgICAgIGNvdW50cnksXG4gICAgICAgIGNsdWVUeXBlOiB0eXBlLFxuICAgICAgICBjbHVlLFxuICAgICAgICBvcHRpb25zLFxuICAgICAgICBjb3JyZWN0QW5zd2VyOiBjb3VudHJ5Lm5hbWUsXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIHJldHVybiBnYW1lUm91bmRzO1xuICB9O1xuXG4gIGNvbnN0IHN0YXJ0R2FtZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdSb3VuZHMgPSBnZW5lcmF0ZVJvdW5kcygpO1xuICAgIHNldFJvdW5kcyhuZXdSb3VuZHMpO1xuICAgIHNldEN1cnJlbnRSb3VuZCgwKTtcbiAgICBzZXRTY29yZSgwKTtcbiAgICBzZXRDb3JyZWN0QW5zd2VycygwKTtcbiAgICBzZXRTZWxlY3RlZEFuc3dlcihudWxsKTtcbiAgICBzZXRTaG93UmVzdWx0KGZhbHNlKTtcbiAgICBzZXRHYW1lQ29tcGxldGUoZmFsc2UpO1xuICAgIHNldEdhbWVTdGFydGVkKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFuc3dlclNlbGVjdCA9IChhbnN3ZXI6IHN0cmluZykgPT4ge1xuICAgIGlmIChzZWxlY3RlZEFuc3dlcikgcmV0dXJuO1xuXG4gICAgc2V0U2VsZWN0ZWRBbnN3ZXIoYW5zd2VyKTtcbiAgICBzZXRTaG93UmVzdWx0KHRydWUpO1xuXG4gICAgY29uc3QgaXNDb3JyZWN0ID0gYW5zd2VyID09PSByb3VuZHNbY3VycmVudFJvdW5kXS5jb3JyZWN0QW5zd2VyO1xuICAgIFxuICAgIGlmIChpc0NvcnJlY3QpIHtcbiAgICAgIHNldENvcnJlY3RBbnN3ZXJzKGNvcnJlY3RBbnN3ZXJzICsgMSk7XG4gICAgICBzZXRTY29yZShzY29yZSArIDEwKTtcbiAgICB9XG5cbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGlmIChjdXJyZW50Um91bmQgPCB0b3RhbFJvdW5kcyAtIDEpIHtcbiAgICAgICAgc2V0Q3VycmVudFJvdW5kKGN1cnJlbnRSb3VuZCArIDEpO1xuICAgICAgICBzZXRTZWxlY3RlZEFuc3dlcihudWxsKTtcbiAgICAgICAgc2V0U2hvd1Jlc3VsdChmYWxzZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRHYW1lQ29tcGxldGUodHJ1ZSk7XG4gICAgICAgIGNvbnN0IGZpbmFsU2NvcmUgPSBNYXRoLnJvdW5kKChjb3JyZWN0QW5zd2VycyArIChpc0NvcnJlY3QgPyAxIDogMCkpIC8gdG90YWxSb3VuZHMgKiAxMDApO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IG9uQ29tcGxldGUoZmluYWxTY29yZSksIDEwMDApO1xuICAgICAgfVxuICAgIH0sIDIwMDApO1xuICB9O1xuXG4gIGNvbnN0IGdldENsdWVJY29uID0gKHR5cGU6IEdhbWVSb3VuZFsnY2x1ZVR5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnbGFuZG1hcmsnOiByZXR1cm4gJ/Cfj5vvuI8nO1xuICAgICAgY2FzZSAnZm9vZCc6IHJldHVybiAn8J+Nve+4jyc7XG4gICAgICBjYXNlICdjdWx0dXJlJzogcmV0dXJuICfwn461JztcbiAgICAgIGNhc2UgJ3dpbGRsaWZlJzogcmV0dXJuICfwn6aBJztcbiAgICAgIGNhc2UgJ2V4cG9ydCc6IHJldHVybiAn8J+Tpic7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ+Kdkyc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldENsdWVEZXNjcmlwdGlvbiA9ICh0eXBlOiBHYW1lUm91bmRbJ2NsdWVUeXBlJ10pID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ2xhbmRtYXJrJzogcmV0dXJuICdGYW1vdXMgbGFuZG1hcmsnO1xuICAgICAgY2FzZSAnZm9vZCc6IHJldHVybiAnVHJhZGl0aW9uYWwgZGlzaCc7XG4gICAgICBjYXNlICdjdWx0dXJlJzogcmV0dXJuICdNdXNpY2FsIHN0eWxlJztcbiAgICAgIGNhc2UgJ3dpbGRsaWZlJzogcmV0dXJuICdOYXRpdmUgYW5pbWFsJztcbiAgICAgIGNhc2UgJ2V4cG9ydCc6IHJldHVybiAnTWFqb3IgZXhwb3J0JztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnQ2x1ZSc7XG4gICAgfVxuICB9O1xuXG4gIGlmICghZ2FtZVN0YXJ0ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC04XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj7wn5e677iPIFdoZXJlIGluIEFmcmljYT88L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNlwiPvCfjI08L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0zMDAgbWItNlwiPlxuICAgICAgICAgICAgR3Vlc3MgdGhlIEFmcmljYW4gY291bnRyeSBmcm9tIGNsdWVzIGFib3V0IGl0cyBsYW5kbWFya3MsIGZvb2QsIGN1bHR1cmUsIGFuZCBtb3JlIVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCB0ZXh0LWdyYXktMzAwIG1iLThcIj5cbiAgICAgICAgICAgIDxwPuKAoiB7dG90YWxSb3VuZHN9IHJvdW5kcyBvZiBjdWx0dXJhbCBjbHVlczwvcD5cbiAgICAgICAgICAgIDxwPuKAoiBDbHVlcyBpbmNsdWRlIGxhbmRtYXJrcywgZm9vZCwgbXVzaWMsIHdpbGRsaWZlLCBhbmQgZXhwb3J0czwvcD5cbiAgICAgICAgICAgIDxwPuKAoiBNdWx0aXBsZSBjaG9pY2UgYW5zd2VyczwvcD5cbiAgICAgICAgICAgIDxwPuKAoiBTY29yZSBwb2ludHMgZm9yIGNvcnJlY3QgZ3Vlc3NlczwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtzdGFydEdhbWV9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNDAwIHRleHQtZ3JheS05MDAgcHktMyBweC04IHJvdW5kZWQtbGcgdGV4dC14bCBmb250LWJvbGQgaG92ZXI6YmcteWVsbG93LTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAg8J+agCBTdGFydCBBZHZlbnR1cmVcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFyb3VuZHNbY3VycmVudFJvdW5kXSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci15ZWxsb3ctNDAwXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgY29uc3Qgcm91bmQgPSByb3VuZHNbY3VycmVudFJvdW5kXTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcC02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02IG1iLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPvCfl7rvuI8gV2hlcmUgaW4gQWZyaWNhPzwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC14bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgIFJvdW5kIHtjdXJyZW50Um91bmQgKyAxfS97dG90YWxSb3VuZHN9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQteWVsbG93LTQwMFwiPntzY29yZX08L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+U2NvcmU8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi00MDBcIj57Y29ycmVjdEFuc3dlcnN9PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkNvcnJlY3Q8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPntjdXJyZW50Um91bmQgKyAxfTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5DdXJyZW50PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTQwMFwiPlxuICAgICAgICAgICAgICB7TWF0aC5yb3VuZCgoY29ycmVjdEFuc3dlcnMgLyBNYXRoLm1heChjdXJyZW50Um91bmQsIDEpKSAqIDEwMCl9JVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkFjY3VyYWN5PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmcteWVsbG93LTQwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHsoKGN1cnJlbnRSb3VuZCArIDEpIC8gdG90YWxSb3VuZHMpICogMTAwfSVgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUXVlc3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTYgbWItNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTRcIj57Z2V0Q2x1ZUljb24ocm91bmQuY2x1ZVR5cGUpfTwvZGl2PlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICBXaGljaCBBZnJpY2FuIGNvdW50cnkgaXMga25vd24gZm9yIHRoaXM/XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy00MDAgYmctb3BhY2l0eS0xMCBib3JkZXIgYm9yZGVyLXllbGxvdy00MDAgcm91bmRlZC1sZyBwLTQgbWItNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0xXCI+XG4gICAgICAgICAgICAgIHtnZXRDbHVlRGVzY3JpcHRpb24ocm91bmQuY2x1ZVR5cGUpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgXCJ7cm91bmQuY2x1ZX1cIlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBPcHRpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICB7cm91bmQub3B0aW9ucy5tYXAoKG9wdGlvbiwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZEFuc3dlciA9PT0gb3B0aW9uO1xuICAgICAgICAgICAgY29uc3QgaXNDb3JyZWN0ID0gb3B0aW9uID09PSByb3VuZC5jb3JyZWN0QW5zd2VyO1xuICAgICAgICAgICAgY29uc3QgaXNJbmNvcnJlY3QgPSBpc1NlbGVjdGVkICYmICFpc0NvcnJlY3QgJiYgc2hvd1Jlc3VsdDtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgbGV0IG9wdGlvbkNsYXNzID0gJ2JnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBwLTQgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOmJvcmRlci15ZWxsb3ctNDAwJztcbiAgICAgICAgICAgIGlmIChzaG93UmVzdWx0KSB7XG4gICAgICAgICAgICAgIGlmIChpc0NvcnJlY3QpIG9wdGlvbkNsYXNzID0gJ2JnLWdyZWVuLTUwMCBiZy1vcGFjaXR5LTIwIGJvcmRlci1ncmVlbi00MDAgcm91bmRlZC1sZyBwLTQnO1xuICAgICAgICAgICAgICBlbHNlIGlmIChpc0luY29ycmVjdCkgb3B0aW9uQ2xhc3MgPSAnYmctcmVkLTUwMCBiZy1vcGFjaXR5LTIwIGJvcmRlci1yZWQtNDAwIHJvdW5kZWQtbGcgcC00JztcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNTZWxlY3RlZCkge1xuICAgICAgICAgICAgICBvcHRpb25DbGFzcyA9ICdiZy15ZWxsb3ctNDAwIGJnLW9wYWNpdHktMTAgYm9yZGVyLXllbGxvdy00MDAgcm91bmRlZC1sZyBwLTQnO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e29wdGlvbkNsYXNzfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFuc3dlclNlbGVjdChvcHRpb24pfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzaG93UmVzdWx0fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGVmdCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e29wdGlvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICB7c2hvd1Jlc3VsdCAmJiBpc0NvcnJlY3QgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXhsXCI+4pyTPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIHtzaG93UmVzdWx0ICYmIGlzSW5jb3JyZWN0ICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIHRleHQteGxcIj7inJc8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZXN1bHQgRXhwbGFuYXRpb24gKi99XG4gICAgICAgIHtzaG93UmVzdWx0ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLWdyYXktNzAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPEZsYWdJbWFnZVxuICAgICAgICAgICAgICAgIGNvdW50cnlJZD17cm91bmQuY291bnRyeS5pZH1cbiAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+e3JvdW5kLmNvdW50cnkubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBDYXBpdGFsOiB7cm91bmQuY291bnRyeS5jYXBpdGFsfSB8IFJlZ2lvbjoge3JvdW5kLmNvdW50cnkucmVnaW9ufVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIHtyb3VuZC5jbHVlVHlwZSA9PT0gJ2xhbmRtYXJrJyAmJiBgJHtyb3VuZC5jbHVlfSBpcyBvbmUgb2YgdGhlIGZhbW91cyBsYW5kbWFya3MgaW4gJHtyb3VuZC5jb3VudHJ5Lm5hbWV9LmB9XG4gICAgICAgICAgICAgICAgICB7cm91bmQuY2x1ZVR5cGUgPT09ICdmb29kJyAmJiBgJHtyb3VuZC5jbHVlfSBpcyBhIHRyYWRpdGlvbmFsIGRpc2ggZnJvbSAke3JvdW5kLmNvdW50cnkubmFtZX0uYH1cbiAgICAgICAgICAgICAgICAgIHtyb3VuZC5jbHVlVHlwZSA9PT0gJ2N1bHR1cmUnICYmIGAke3JvdW5kLmNsdWV9IGlzIGEgcG9wdWxhciBtdXNpYyBzdHlsZSBpbiAke3JvdW5kLmNvdW50cnkubmFtZX0uYH1cbiAgICAgICAgICAgICAgICAgIHtyb3VuZC5jbHVlVHlwZSA9PT0gJ3dpbGRsaWZlJyAmJiBgJHtyb3VuZC5jbHVlfSBjYW4gYmUgZm91bmQgaW4gdGhlIHdpbGQgYXJlYXMgb2YgJHtyb3VuZC5jb3VudHJ5Lm5hbWV9LmB9XG4gICAgICAgICAgICAgICAgICB7cm91bmQuY2x1ZVR5cGUgPT09ICdleHBvcnQnICYmIGAke3JvdW5kLmNsdWV9IGlzIG9uZSBvZiB0aGUgbWFqb3IgZXhwb3J0cyBvZiAke3JvdW5kLmNvdW50cnkubmFtZX0uYH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEdhbWUgQ29tcGxldGUgTW9kYWwgKi99XG4gICAgICB7Z2FtZUNvbXBsZXRlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00IGJnLWJsYWNrIGJnLW9wYWNpdHktNzVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIHJvdW5kZWQtbGcgcC02IG1heC13LW1kIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsXCI+XG4gICAgICAgICAgICAgICAge2NvcnJlY3RBbnN3ZXJzID49IDggPyAn8J+PhicgOiBjb3JyZWN0QW5zd2VycyA+PSA2ID8gJ/CfjoknIDogJ/Cfl7rvuI8nfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy00MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgQWR2ZW50dXJlIENvbXBsZXRlIVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgPHA+Q29ycmVjdCBBbnN3ZXJzOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMFwiPntjb3JyZWN0QW5zd2Vyc308L3NwYW4+L3t0b3RhbFJvdW5kc308L3A+XG4gICAgICAgICAgICAgICAgICA8cD5GaW5hbCBTY29yZTogPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIGZvbnQtYm9sZFwiPntNYXRoLnJvdW5kKChjb3JyZWN0QW5zd2VycyAvIHRvdGFsUm91bmRzKSAqIDEwMCl9JTwvc3Bhbj48L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtjb3JyZWN0QW5zd2VycyA+PSA4ID8gJ0FmcmljYSBFeHBlcnQhJyA6IFxuICAgICAgICAgICAgICAgICAgICAgY29ycmVjdEFuc3dlcnMgPj0gNiA/ICdHcmVhdCBFeHBsb3JlciEnIDogXG4gICAgICAgICAgICAgICAgICAgICAnS2VlcCBFeHBsb3JpbmchJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtzdGFydEdhbWV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNDAwIHRleHQtZ3JheS05MDAgcHktMiBweC00IHJvdW5kZWQtbGcgaG92ZXI6YmcteWVsbG93LTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg8J+UhCBFeHBsb3JlIEFnYWluXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFdoZXJlSW5BZnJpY2E7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsImdldFJhbmRvbUl0ZW1zIiwic2h1ZmZsZUFycmF5IiwiRmxhZ0ltYWdlIiwiV2hlcmVJbkFmcmljYSIsImNvdW50cmllcyIsIm9uQ29tcGxldGUiLCJjdXJyZW50Um91bmQiLCJzZXRDdXJyZW50Um91bmQiLCJyb3VuZHMiLCJzZXRSb3VuZHMiLCJzZWxlY3RlZEFuc3dlciIsInNldFNlbGVjdGVkQW5zd2VyIiwic2hvd1Jlc3VsdCIsInNldFNob3dSZXN1bHQiLCJzY29yZSIsInNldFNjb3JlIiwiY29ycmVjdEFuc3dlcnMiLCJzZXRDb3JyZWN0QW5zd2VycyIsImdhbWVDb21wbGV0ZSIsInNldEdhbWVDb21wbGV0ZSIsImdhbWVTdGFydGVkIiwic2V0R2FtZVN0YXJ0ZWQiLCJ0b3RhbFJvdW5kcyIsImdlbmVyYXRlQ2x1ZSIsImNvdW50cnkiLCJjbHVlVHlwZXMiLCJ0eXBlIiwiaXRlbXMiLCJsYW5kbWFya3MiLCJjdWx0dXJhbEVsZW1lbnRzIiwiY3Vpc2luZSIsIm11c2ljIiwid2lsZGxpZmUiLCJleHBvcnRzIiwiYXZhaWxhYmxlQ2x1ZXMiLCJmaWx0ZXIiLCJjdCIsImxlbmd0aCIsInNlbGVjdGVkQ2x1ZVR5cGUiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJzZWxlY3RlZEl0ZW0iLCJjbHVlIiwiZ2VuZXJhdGVSb3VuZHMiLCJzZWxlY3RlZENvdW50cmllcyIsImdhbWVSb3VuZHMiLCJmb3JFYWNoIiwid3JvbmdDb3VudHJpZXMiLCJjIiwiaWQiLCJvcHRpb25zIiwibmFtZSIsIm1hcCIsInB1c2giLCJjbHVlVHlwZSIsImNvcnJlY3RBbnN3ZXIiLCJzdGFydEdhbWUiLCJuZXdSb3VuZHMiLCJoYW5kbGVBbnN3ZXJTZWxlY3QiLCJhbnN3ZXIiLCJpc0NvcnJlY3QiLCJzZXRUaW1lb3V0IiwiZmluYWxTY29yZSIsInJvdW5kIiwiZ2V0Q2x1ZUljb24iLCJnZXRDbHVlRGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwibWF4Iiwic3R5bGUiLCJ3aWR0aCIsImgzIiwib3B0aW9uIiwiaW5kZXgiLCJpc1NlbGVjdGVkIiwiaXNJbmNvcnJlY3QiLCJvcHRpb25DbGFzcyIsImRpc2FibGVkIiwic3BhbiIsImNvdW50cnlJZCIsInNpemUiLCJoNCIsImNhcGl0YWwiLCJyZWdpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/games/WhereInAfrica.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FlagImage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FlagImage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/flagUtils */ \"(ssr)/./src/utils/flagUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FlagImage = ({ countryId, size = 'medium', format = 'svg', className = '', showFallback = true, onClick })=>{\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Try to get flag image URL, fallback to emoji if not available\n    const flagImageUrl = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagImage)(countryId, format);\n    const flagEmoji = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagEmoji)(countryId);\n    const flagAlt = (0,_utils_flagUtils__WEBPACK_IMPORTED_MODULE_2__.getFlagAlt)(countryId);\n    // Size configurations\n    const sizeClasses = {\n        small: 'w-8 h-6',\n        medium: 'w-12 h-9',\n        large: 'w-16 h-12',\n        xl: 'w-24 h-18'\n    };\n    const emojiSizes = {\n        small: 'text-lg',\n        medium: 'text-2xl',\n        large: 'text-3xl',\n        xl: 'text-5xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagImage.useEffect\": ()=>{\n            if (!flagImageUrl) {\n                setImageError(true);\n                setIsLoading(false);\n                return;\n            }\n            setImageLoaded(false);\n            setImageError(false);\n            setIsLoading(true);\n            // Preload the image with caching optimization\n            const img = new Image();\n            img.crossOrigin = 'anonymous'; // Enable CORS for better caching\n            img.loading = 'eager'; // Prioritize loading for visible flags\n            img.onload = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(true);\n                    setImageError(false);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            img.onerror = ({\n                \"FlagImage.useEffect\": ()=>{\n                    setImageLoaded(false);\n                    setImageError(true);\n                    setIsLoading(false);\n                }\n            })[\"FlagImage.useEffect\"];\n            // Add cache-busting prevention and optimization\n            const cachedUrl = `${flagImageUrl}?cache=1`;\n            img.src = cachedUrl;\n            return ({\n                \"FlagImage.useEffect\": ()=>{\n                    img.onload = null;\n                    img.onerror = null;\n                }\n            })[\"FlagImage.useEffect\"];\n        }\n    }[\"FlagImage.useEffect\"], [\n        flagImageUrl\n    ]);\n    const baseClasses = `\n    ${sizeClasses[size]} \n    object-cover \n    rounded-sm \n    border \n    border-gray-300 \n    shadow-sm\n    ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}\n    ${className}\n  `;\n    // Show loading state\n    if (isLoading && flagImageUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${baseClasses} bg-gray-200 animate-pulse flex items-center justify-center`,\n            onClick: onClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show flag image if loaded successfully\n    if (imageLoaded && flagImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: flagImageUrl,\n            alt: flagAlt,\n            className: baseClasses,\n            onClick: onClick,\n            onError: ()=>setImageError(true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show emoji fallback if image failed to load or showFallback is true\n    if (showFallback || imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n          ${sizeClasses[size]} \n          flex \n          items-center \n          justify-center \n          bg-gray-100 \n          rounded-sm \n          border \n          border-gray-300\n          ${onClick ? 'cursor-pointer hover:bg-gray-200 transition-colors' : ''}\n          ${className}\n        `,\n            onClick: onClick,\n            title: flagAlt,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: emojiSizes[size],\n                children: flagEmoji\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback to empty state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${baseClasses} bg-gray-100 flex items-center justify-center`,\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-400 text-xs\",\n            children: \"\\uD83C\\uDFF3️\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\ui\\\\FlagImage.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagImage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9GbGFnSW1hZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFbUQ7QUFDd0I7QUFXM0UsTUFBTU0sWUFBc0MsQ0FBQyxFQUMzQ0MsU0FBUyxFQUNUQyxPQUFPLFFBQVEsRUFDZkMsU0FBUyxLQUFLLEVBQ2RDLFlBQVksRUFBRSxFQUNkQyxlQUFlLElBQUksRUFDbkJDLE9BQU8sRUFDUjtJQUNDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHYiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNjLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFFM0MsZ0VBQWdFO0lBQ2hFLE1BQU1rQixlQUFlaEIsOERBQVlBLENBQUNJLFdBQVdFO0lBQzdDLE1BQU1XLFlBQVloQiw4REFBWUEsQ0FBQ0c7SUFDL0IsTUFBTWMsVUFBVWhCLDREQUFVQSxDQUFDRTtJQUUzQixzQkFBc0I7SUFDdEIsTUFBTWUsY0FBYztRQUNsQkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsYUFBYTtRQUNqQkosT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsSUFBSTtJQUNOO0lBRUF4QixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNpQixjQUFjO2dCQUNqQkgsY0FBYztnQkFDZEUsYUFBYTtnQkFDYjtZQUNGO1lBRUFKLGVBQWU7WUFDZkUsY0FBYztZQUNkRSxhQUFhO1lBRWIsOENBQThDO1lBQzlDLE1BQU1VLE1BQU0sSUFBSUM7WUFDaEJELElBQUlFLFdBQVcsR0FBRyxhQUFhLGlDQUFpQztZQUNoRUYsSUFBSUcsT0FBTyxHQUFHLFNBQVMsdUNBQXVDO1lBRTlESCxJQUFJSSxNQUFNO3VDQUFHO29CQUNYbEIsZUFBZTtvQkFDZkUsY0FBYztvQkFDZEUsYUFBYTtnQkFDZjs7WUFFQVUsSUFBSUssT0FBTzt1Q0FBRztvQkFDWm5CLGVBQWU7b0JBQ2ZFLGNBQWM7b0JBQ2RFLGFBQWE7Z0JBQ2Y7O1lBRUEsZ0RBQWdEO1lBQ2hELE1BQU1nQixZQUFZLEdBQUdmLGFBQWEsUUFBUSxDQUFDO1lBQzNDUyxJQUFJTyxHQUFHLEdBQUdEO1lBRVY7dUNBQU87b0JBQ0xOLElBQUlJLE1BQU0sR0FBRztvQkFDYkosSUFBSUssT0FBTyxHQUFHO2dCQUNoQjs7UUFDRjs4QkFBRztRQUFDZDtLQUFhO0lBRWpCLE1BQU1pQixjQUFjLENBQUM7SUFDbkIsRUFBRWQsV0FBVyxDQUFDZCxLQUFLLENBQUM7Ozs7OztJQU1wQixFQUFFSSxVQUFVLHFEQUFxRCxHQUFHO0lBQ3BFLEVBQUVGLFVBQVU7RUFDZCxDQUFDO0lBRUQscUJBQXFCO0lBQ3JCLElBQUlPLGFBQWFFLGNBQWM7UUFDN0IscUJBQ0UsOERBQUNrQjtZQUNDM0IsV0FBVyxHQUFHMEIsWUFBWSwyREFBMkQsQ0FBQztZQUN0RnhCLFNBQVNBO3NCQUVULDRFQUFDeUI7Z0JBQUkzQixXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLHlDQUF5QztJQUN6QyxJQUFJRyxlQUFlTSxnQkFBZ0IsQ0FBQ0osWUFBWTtRQUM5QyxxQkFDRSw4REFBQ2E7WUFDQ08sS0FBS2hCO1lBQ0xtQixLQUFLakI7WUFDTFgsV0FBVzBCO1lBQ1h4QixTQUFTQTtZQUNUMkIsU0FBUyxJQUFNdkIsY0FBYzs7Ozs7O0lBR25DO0lBRUEsc0VBQXNFO0lBQ3RFLElBQUlMLGdCQUFnQkksWUFBWTtRQUM5QixxQkFDRSw4REFBQ3NCO1lBQ0MzQixXQUFXLENBQUM7VUFDVixFQUFFWSxXQUFXLENBQUNkLEtBQUssQ0FBQzs7Ozs7Ozs7VUFRcEIsRUFBRUksVUFBVSx1REFBdUQsR0FBRztVQUN0RSxFQUFFRixVQUFVO1FBQ2QsQ0FBQztZQUNERSxTQUFTQTtZQUNUNEIsT0FBT25CO3NCQUVQLDRFQUFDb0I7Z0JBQUsvQixXQUFXaUIsVUFBVSxDQUFDbkIsS0FBSzswQkFBR1k7Ozs7Ozs7Ozs7O0lBRzFDO0lBRUEsMEJBQTBCO0lBQzFCLHFCQUNFLDhEQUFDaUI7UUFDQzNCLFdBQVcsR0FBRzBCLFlBQVksNkNBQTZDLENBQUM7UUFDeEV4QixTQUFTQTtrQkFFVCw0RUFBQzZCO1lBQUsvQixXQUFVO3NCQUF3Qjs7Ozs7Ozs7Ozs7QUFHOUM7QUFFQSxpRUFBZUosU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNRUVLIEVERU5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcR0FNRVMgRk9SIEFSSUNBXFxnYW1lcy1mb3ItYWZyaWNhXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxGbGFnSW1hZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRGbGFnSW1hZ2UsIGdldEZsYWdFbW9qaSwgZ2V0RmxhZ0FsdCB9IGZyb20gJ0AvdXRpbHMvZmxhZ1V0aWxzJztcblxuaW50ZXJmYWNlIEZsYWdJbWFnZVByb3BzIHtcbiAgY291bnRyeUlkOiBzdHJpbmc7XG4gIHNpemU/OiAnc21hbGwnIHwgJ21lZGl1bScgfCAnbGFyZ2UnIHwgJ3hsJztcbiAgZm9ybWF0PzogJ3N2ZycgfCAncG5nJztcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBzaG93RmFsbGJhY2s/OiBib29sZWFuO1xuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgRmxhZ0ltYWdlOiBSZWFjdC5GQzxGbGFnSW1hZ2VQcm9wcz4gPSAoe1xuICBjb3VudHJ5SWQsXG4gIHNpemUgPSAnbWVkaXVtJyxcbiAgZm9ybWF0ID0gJ3N2ZycsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBzaG93RmFsbGJhY2sgPSB0cnVlLFxuICBvbkNsaWNrLFxufSkgPT4ge1xuICBjb25zdCBbaW1hZ2VMb2FkZWQsIHNldEltYWdlTG9hZGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2ltYWdlRXJyb3IsIHNldEltYWdlRXJyb3JdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgLy8gVHJ5IHRvIGdldCBmbGFnIGltYWdlIFVSTCwgZmFsbGJhY2sgdG8gZW1vamkgaWYgbm90IGF2YWlsYWJsZVxuICBjb25zdCBmbGFnSW1hZ2VVcmwgPSBnZXRGbGFnSW1hZ2UoY291bnRyeUlkLCBmb3JtYXQpO1xuICBjb25zdCBmbGFnRW1vamkgPSBnZXRGbGFnRW1vamkoY291bnRyeUlkKTtcbiAgY29uc3QgZmxhZ0FsdCA9IGdldEZsYWdBbHQoY291bnRyeUlkKTtcblxuICAvLyBTaXplIGNvbmZpZ3VyYXRpb25zXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtYWxsOiAndy04IGgtNicsXG4gICAgbWVkaXVtOiAndy0xMiBoLTknLFxuICAgIGxhcmdlOiAndy0xNiBoLTEyJyxcbiAgICB4bDogJ3ctMjQgaC0xOCcsXG4gIH07XG5cbiAgY29uc3QgZW1vamlTaXplcyA9IHtcbiAgICBzbWFsbDogJ3RleHQtbGcnLFxuICAgIG1lZGl1bTogJ3RleHQtMnhsJyxcbiAgICBsYXJnZTogJ3RleHQtM3hsJyxcbiAgICB4bDogJ3RleHQtNXhsJyxcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZmxhZ0ltYWdlVXJsKSB7XG4gICAgICBzZXRJbWFnZUVycm9yKHRydWUpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJbWFnZUxvYWRlZChmYWxzZSk7XG4gICAgc2V0SW1hZ2VFcnJvcihmYWxzZSk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgLy8gUHJlbG9hZCB0aGUgaW1hZ2Ugd2l0aCBjYWNoaW5nIG9wdGltaXphdGlvblxuICAgIGNvbnN0IGltZyA9IG5ldyBJbWFnZSgpO1xuICAgIGltZy5jcm9zc09yaWdpbiA9ICdhbm9ueW1vdXMnOyAvLyBFbmFibGUgQ09SUyBmb3IgYmV0dGVyIGNhY2hpbmdcbiAgICBpbWcubG9hZGluZyA9ICdlYWdlcic7IC8vIFByaW9yaXRpemUgbG9hZGluZyBmb3IgdmlzaWJsZSBmbGFnc1xuXG4gICAgaW1nLm9ubG9hZCA9ICgpID0+IHtcbiAgICAgIHNldEltYWdlTG9hZGVkKHRydWUpO1xuICAgICAgc2V0SW1hZ2VFcnJvcihmYWxzZSk7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH07XG5cbiAgICBpbWcub25lcnJvciA9ICgpID0+IHtcbiAgICAgIHNldEltYWdlTG9hZGVkKGZhbHNlKTtcbiAgICAgIHNldEltYWdlRXJyb3IodHJ1ZSk7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH07XG5cbiAgICAvLyBBZGQgY2FjaGUtYnVzdGluZyBwcmV2ZW50aW9uIGFuZCBvcHRpbWl6YXRpb25cbiAgICBjb25zdCBjYWNoZWRVcmwgPSBgJHtmbGFnSW1hZ2VVcmx9P2NhY2hlPTFgO1xuICAgIGltZy5zcmMgPSBjYWNoZWRVcmw7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaW1nLm9ubG9hZCA9IG51bGw7XG4gICAgICBpbWcub25lcnJvciA9IG51bGw7XG4gICAgfTtcbiAgfSwgW2ZsYWdJbWFnZVVybF0pO1xuXG4gIGNvbnN0IGJhc2VDbGFzc2VzID0gYFxuICAgICR7c2l6ZUNsYXNzZXNbc2l6ZV19IFxuICAgIG9iamVjdC1jb3ZlciBcbiAgICByb3VuZGVkLXNtIFxuICAgIGJvcmRlciBcbiAgICBib3JkZXItZ3JheS0zMDAgXG4gICAgc2hhZG93LXNtXG4gICAgJHtvbkNsaWNrID8gJ2N1cnNvci1wb2ludGVyIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdycgOiAnJ31cbiAgICAke2NsYXNzTmFtZX1cbiAgYDtcblxuICAvLyBTaG93IGxvYWRpbmcgc3RhdGVcbiAgaWYgKGlzTG9hZGluZyAmJiBmbGFnSW1hZ2VVcmwpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtiYXNlQ2xhc3Nlc30gYmctZ3JheS0yMDAgYW5pbWF0ZS1wdWxzZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcmB9XG4gICAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItZ3JheS00MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIFNob3cgZmxhZyBpbWFnZSBpZiBsb2FkZWQgc3VjY2Vzc2Z1bGx5XG4gIGlmIChpbWFnZUxvYWRlZCAmJiBmbGFnSW1hZ2VVcmwgJiYgIWltYWdlRXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGltZ1xuICAgICAgICBzcmM9e2ZsYWdJbWFnZVVybH1cbiAgICAgICAgYWx0PXtmbGFnQWx0fVxuICAgICAgICBjbGFzc05hbWU9e2Jhc2VDbGFzc2VzfVxuICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgICBvbkVycm9yPXsoKSA9PiBzZXRJbWFnZUVycm9yKHRydWUpfVxuICAgICAgLz5cbiAgICApO1xuICB9XG5cbiAgLy8gU2hvdyBlbW9qaSBmYWxsYmFjayBpZiBpbWFnZSBmYWlsZWQgdG8gbG9hZCBvciBzaG93RmFsbGJhY2sgaXMgdHJ1ZVxuICBpZiAoc2hvd0ZhbGxiYWNrIHx8IGltYWdlRXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBcbiAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgJHtzaXplQ2xhc3Nlc1tzaXplXX0gXG4gICAgICAgICAgZmxleCBcbiAgICAgICAgICBpdGVtcy1jZW50ZXIgXG4gICAgICAgICAganVzdGlmeS1jZW50ZXIgXG4gICAgICAgICAgYmctZ3JheS0xMDAgXG4gICAgICAgICAgcm91bmRlZC1zbSBcbiAgICAgICAgICBib3JkZXIgXG4gICAgICAgICAgYm9yZGVyLWdyYXktMzAwXG4gICAgICAgICAgJHtvbkNsaWNrID8gJ2N1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzJyA6ICcnfVxuICAgICAgICAgICR7Y2xhc3NOYW1lfVxuICAgICAgICBgfVxuICAgICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgICAgICB0aXRsZT17ZmxhZ0FsdH1cbiAgICAgID5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtlbW9qaVNpemVzW3NpemVdfT57ZmxhZ0Vtb2ppfTwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBGYWxsYmFjayB0byBlbXB0eSBzdGF0ZVxuICByZXR1cm4gKFxuICAgIDxkaXYgXG4gICAgICBjbGFzc05hbWU9e2Ake2Jhc2VDbGFzc2VzfSBiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcmB9XG4gICAgICBvbkNsaWNrPXtvbkNsaWNrfVxuICAgID5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPvCfj7PvuI88L3NwYW4+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGbGFnSW1hZ2U7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImdldEZsYWdJbWFnZSIsImdldEZsYWdFbW9qaSIsImdldEZsYWdBbHQiLCJGbGFnSW1hZ2UiLCJjb3VudHJ5SWQiLCJzaXplIiwiZm9ybWF0IiwiY2xhc3NOYW1lIiwic2hvd0ZhbGxiYWNrIiwib25DbGljayIsImltYWdlTG9hZGVkIiwic2V0SW1hZ2VMb2FkZWQiLCJpbWFnZUVycm9yIiwic2V0SW1hZ2VFcnJvciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImZsYWdJbWFnZVVybCIsImZsYWdFbW9qaSIsImZsYWdBbHQiLCJzaXplQ2xhc3NlcyIsInNtYWxsIiwibWVkaXVtIiwibGFyZ2UiLCJ4bCIsImVtb2ppU2l6ZXMiLCJpbWciLCJJbWFnZSIsImNyb3NzT3JpZ2luIiwibG9hZGluZyIsIm9ubG9hZCIsIm9uZXJyb3IiLCJjYWNoZWRVcmwiLCJzcmMiLCJiYXNlQ2xhc3NlcyIsImRpdiIsImFsdCIsIm9uRXJyb3IiLCJ0aXRsZSIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FlagImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/flagUtils.ts":
/*!********************************!*\
  !*** ./src/utils/flagUtils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AFRICAN_COUNTRY_FLAGS: () => (/* binding */ AFRICAN_COUNTRY_FLAGS),\n/* harmony export */   getAllFlagData: () => (/* binding */ getAllFlagData),\n/* harmony export */   getFlagAlt: () => (/* binding */ getFlagAlt),\n/* harmony export */   getFlagEmoji: () => (/* binding */ getFlagEmoji),\n/* harmony export */   getFlagImage: () => (/* binding */ getFlagImage)\n/* harmony export */ });\n// Flag image utility functions and mappings\n// Using flagcdn.com for consistent, high-quality flag images\n// Country code to flag mapping for African countries\nconst AFRICAN_COUNTRY_FLAGS = {\n    // North Africa\n    'egypt': {\n        svg: 'https://flagcdn.com/eg.svg',\n        png: 'https://flagcdn.com/w320/eg.png',\n        emoji: '🇪🇬',\n        alt: 'Flag of Egypt'\n    },\n    'libya': {\n        svg: 'https://flagcdn.com/ly.svg',\n        png: 'https://flagcdn.com/w320/ly.png',\n        emoji: '🇱🇾',\n        alt: 'Flag of Libya'\n    },\n    'tunisia': {\n        svg: 'https://flagcdn.com/tn.svg',\n        png: 'https://flagcdn.com/w320/tn.png',\n        emoji: '🇹🇳',\n        alt: 'Flag of Tunisia'\n    },\n    'algeria': {\n        svg: 'https://flagcdn.com/dz.svg',\n        png: 'https://flagcdn.com/w320/dz.png',\n        emoji: '🇩🇿',\n        alt: 'Flag of Algeria'\n    },\n    'morocco': {\n        svg: 'https://flagcdn.com/ma.svg',\n        png: 'https://flagcdn.com/w320/ma.png',\n        emoji: '🇲🇦',\n        alt: 'Flag of Morocco'\n    },\n    'sudan': {\n        svg: 'https://flagcdn.com/sd.svg',\n        png: 'https://flagcdn.com/w320/sd.png',\n        emoji: '🇸🇩',\n        alt: 'Flag of Sudan'\n    },\n    // West Africa\n    'nigeria': {\n        svg: 'https://flagcdn.com/ng.svg',\n        png: 'https://flagcdn.com/w320/ng.png',\n        emoji: '🇳🇬',\n        alt: 'Flag of Nigeria'\n    },\n    'ghana': {\n        svg: 'https://flagcdn.com/gh.svg',\n        png: 'https://flagcdn.com/w320/gh.png',\n        emoji: '🇬🇭',\n        alt: 'Flag of Ghana'\n    },\n    'senegal': {\n        svg: 'https://flagcdn.com/sn.svg',\n        png: 'https://flagcdn.com/w320/sn.png',\n        emoji: '🇸🇳',\n        alt: 'Flag of Senegal'\n    },\n    'mali': {\n        svg: 'https://flagcdn.com/ml.svg',\n        png: 'https://flagcdn.com/w320/ml.png',\n        emoji: '🇲🇱',\n        alt: 'Flag of Mali'\n    },\n    'burkina-faso': {\n        svg: 'https://flagcdn.com/bf.svg',\n        png: 'https://flagcdn.com/w320/bf.png',\n        emoji: '🇧🇫',\n        alt: 'Flag of Burkina Faso'\n    },\n    'niger': {\n        svg: 'https://flagcdn.com/ne.svg',\n        png: 'https://flagcdn.com/w320/ne.png',\n        emoji: '🇳🇪',\n        alt: 'Flag of Niger'\n    },\n    'guinea': {\n        svg: 'https://flagcdn.com/gn.svg',\n        png: 'https://flagcdn.com/w320/gn.png',\n        emoji: '🇬🇳',\n        alt: 'Flag of Guinea'\n    },\n    'sierra-leone': {\n        svg: 'https://flagcdn.com/sl.svg',\n        png: 'https://flagcdn.com/w320/sl.png',\n        emoji: '🇸🇱',\n        alt: 'Flag of Sierra Leone'\n    },\n    'liberia': {\n        svg: 'https://flagcdn.com/lr.svg',\n        png: 'https://flagcdn.com/w320/lr.png',\n        emoji: '🇱🇷',\n        alt: 'Flag of Liberia'\n    },\n    'ivory-coast': {\n        svg: 'https://flagcdn.com/ci.svg',\n        png: 'https://flagcdn.com/w320/ci.png',\n        emoji: '🇨🇮',\n        alt: 'Flag of Ivory Coast'\n    },\n    'gambia': {\n        svg: 'https://flagcdn.com/gm.svg',\n        png: 'https://flagcdn.com/w320/gm.png',\n        emoji: '🇬🇲',\n        alt: 'Flag of Gambia'\n    },\n    'guinea-bissau': {\n        svg: 'https://flagcdn.com/gw.svg',\n        png: 'https://flagcdn.com/w320/gw.png',\n        emoji: '🇬🇼',\n        alt: 'Flag of Guinea-Bissau'\n    },\n    'cape-verde': {\n        svg: 'https://flagcdn.com/cv.svg',\n        png: 'https://flagcdn.com/w320/cv.png',\n        emoji: '🇨🇻',\n        alt: 'Flag of Cape Verde'\n    },\n    'mauritania': {\n        svg: 'https://flagcdn.com/mr.svg',\n        png: 'https://flagcdn.com/w320/mr.png',\n        emoji: '🇲🇷',\n        alt: 'Flag of Mauritania'\n    },\n    'benin': {\n        svg: 'https://flagcdn.com/bj.svg',\n        png: 'https://flagcdn.com/w320/bj.png',\n        emoji: '🇧🇯',\n        alt: 'Flag of Benin'\n    },\n    'togo': {\n        svg: 'https://flagcdn.com/tg.svg',\n        png: 'https://flagcdn.com/w320/tg.png',\n        emoji: '🇹🇬',\n        alt: 'Flag of Togo'\n    },\n    // East Africa\n    'kenya': {\n        svg: 'https://flagcdn.com/ke.svg',\n        png: 'https://flagcdn.com/w320/ke.png',\n        emoji: '🇰🇪',\n        alt: 'Flag of Kenya'\n    },\n    'ethiopia': {\n        svg: 'https://flagcdn.com/et.svg',\n        png: 'https://flagcdn.com/w320/et.png',\n        emoji: '🇪🇹',\n        alt: 'Flag of Ethiopia'\n    },\n    'tanzania': {\n        svg: 'https://flagcdn.com/tz.svg',\n        png: 'https://flagcdn.com/w320/tz.png',\n        emoji: '🇹🇿',\n        alt: 'Flag of Tanzania'\n    },\n    'uganda': {\n        svg: 'https://flagcdn.com/ug.svg',\n        png: 'https://flagcdn.com/w320/ug.png',\n        emoji: '🇺🇬',\n        alt: 'Flag of Uganda'\n    },\n    'rwanda': {\n        svg: 'https://flagcdn.com/rw.svg',\n        png: 'https://flagcdn.com/w320/rw.png',\n        emoji: '🇷🇼',\n        alt: 'Flag of Rwanda'\n    },\n    'burundi': {\n        svg: 'https://flagcdn.com/bi.svg',\n        png: 'https://flagcdn.com/w320/bi.png',\n        emoji: '🇧🇮',\n        alt: 'Flag of Burundi'\n    },\n    'somalia': {\n        svg: 'https://flagcdn.com/so.svg',\n        png: 'https://flagcdn.com/w320/so.png',\n        emoji: '🇸🇴',\n        alt: 'Flag of Somalia'\n    },\n    'eritrea': {\n        svg: 'https://flagcdn.com/er.svg',\n        png: 'https://flagcdn.com/w320/er.png',\n        emoji: '🇪🇷',\n        alt: 'Flag of Eritrea'\n    },\n    'djibouti': {\n        svg: 'https://flagcdn.com/dj.svg',\n        png: 'https://flagcdn.com/w320/dj.png',\n        emoji: '🇩🇯',\n        alt: 'Flag of Djibouti'\n    },\n    'south-sudan': {\n        svg: 'https://flagcdn.com/ss.svg',\n        png: 'https://flagcdn.com/w320/ss.png',\n        emoji: '🇸🇸',\n        alt: 'Flag of South Sudan'\n    },\n    // Central Africa\n    'democratic-republic-congo': {\n        svg: 'https://flagcdn.com/cd.svg',\n        png: 'https://flagcdn.com/w320/cd.png',\n        emoji: '🇨🇩',\n        alt: 'Flag of Democratic Republic of Congo'\n    },\n    'central-african-republic': {\n        svg: 'https://flagcdn.com/cf.svg',\n        png: 'https://flagcdn.com/w320/cf.png',\n        emoji: '🇨🇫',\n        alt: 'Flag of Central African Republic'\n    },\n    'chad': {\n        svg: 'https://flagcdn.com/td.svg',\n        png: 'https://flagcdn.com/w320/td.png',\n        emoji: '🇹🇩',\n        alt: 'Flag of Chad'\n    },\n    'cameroon': {\n        svg: 'https://flagcdn.com/cm.svg',\n        png: 'https://flagcdn.com/w320/cm.png',\n        emoji: '🇨🇲',\n        alt: 'Flag of Cameroon'\n    },\n    'republic-congo': {\n        svg: 'https://flagcdn.com/cg.svg',\n        png: 'https://flagcdn.com/w320/cg.png',\n        emoji: '🇨🇬',\n        alt: 'Flag of Republic of Congo'\n    },\n    'equatorial-guinea': {\n        svg: 'https://flagcdn.com/gq.svg',\n        png: 'https://flagcdn.com/w320/gq.png',\n        emoji: '🇬🇶',\n        alt: 'Flag of Equatorial Guinea'\n    },\n    'gabon': {\n        svg: 'https://flagcdn.com/ga.svg',\n        png: 'https://flagcdn.com/w320/ga.png',\n        emoji: '🇬🇦',\n        alt: 'Flag of Gabon'\n    },\n    'sao-tome-principe': {\n        svg: 'https://flagcdn.com/st.svg',\n        png: 'https://flagcdn.com/w320/st.png',\n        emoji: '🇸🇹',\n        alt: 'Flag of São Tomé and Príncipe'\n    },\n    // Southern Africa\n    'south-africa': {\n        svg: 'https://flagcdn.com/za.svg',\n        png: 'https://flagcdn.com/w320/za.png',\n        emoji: '🇿🇦',\n        alt: 'Flag of South Africa'\n    },\n    'zimbabwe': {\n        svg: 'https://flagcdn.com/zw.svg',\n        png: 'https://flagcdn.com/w320/zw.png',\n        emoji: '🇿🇼',\n        alt: 'Flag of Zimbabwe'\n    },\n    'botswana': {\n        svg: 'https://flagcdn.com/bw.svg',\n        png: 'https://flagcdn.com/w320/bw.png',\n        emoji: '🇧🇼',\n        alt: 'Flag of Botswana'\n    },\n    'namibia': {\n        svg: 'https://flagcdn.com/na.svg',\n        png: 'https://flagcdn.com/w320/na.png',\n        emoji: '🇳🇦',\n        alt: 'Flag of Namibia'\n    },\n    'zambia': {\n        svg: 'https://flagcdn.com/zm.svg',\n        png: 'https://flagcdn.com/w320/zm.png',\n        emoji: '🇿🇲',\n        alt: 'Flag of Zambia'\n    },\n    'malawi': {\n        svg: 'https://flagcdn.com/mw.svg',\n        png: 'https://flagcdn.com/w320/mw.png',\n        emoji: '🇲🇼',\n        alt: 'Flag of Malawi'\n    },\n    'mozambique': {\n        svg: 'https://flagcdn.com/mz.svg',\n        png: 'https://flagcdn.com/w320/mz.png',\n        emoji: '🇲🇿',\n        alt: 'Flag of Mozambique'\n    },\n    'angola': {\n        svg: 'https://flagcdn.com/ao.svg',\n        png: 'https://flagcdn.com/w320/ao.png',\n        emoji: '🇦🇴',\n        alt: 'Flag of Angola'\n    },\n    'lesotho': {\n        svg: 'https://flagcdn.com/ls.svg',\n        png: 'https://flagcdn.com/w320/ls.png',\n        emoji: '🇱🇸',\n        alt: 'Flag of Lesotho'\n    },\n    'eswatini': {\n        svg: 'https://flagcdn.com/sz.svg',\n        png: 'https://flagcdn.com/w320/sz.png',\n        emoji: '🇸🇿',\n        alt: 'Flag of Eswatini'\n    }\n};\n// Utility functions\nconst getFlagImage = (countryId, format = 'svg')=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData[format] : '';\n};\nconst getFlagEmoji = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.emoji : '🏳️';\n};\nconst getFlagAlt = (countryId)=>{\n    const flagData = AFRICAN_COUNTRY_FLAGS[countryId];\n    return flagData ? flagData.alt : `Flag of ${countryId}`;\n};\nconst getAllFlagData = (countryId)=>{\n    return AFRICAN_COUNTRY_FLAGS[countryId] || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/flagUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* binding */ calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* binding */ calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* binding */ calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* binding */ generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* binding */ getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* binding */ loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* binding */ resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* binding */ saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   validateAnswer: () => (/* binding */ validateAnswer)\n/* harmony export */ });\n// Seeded random number generator for consistent results\nlet seed = 1;\nconst seededRandom = ()=>{\n    const x = Math.sin(seed++) * 10000;\n    return x - Math.floor(x);\n};\n// Reset seed function for consistent shuffling\nconst resetSeed = (newSeed = 1)=>{\n    seed = newSeed;\n};\n// Array shuffling utility with seeded random\nconst shuffleArray = (array)=>{\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(seededRandom() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n};\n// Random selection utility\nconst getRandomItems = (array, count)=>{\n    const shuffled = shuffleArray(array);\n    return shuffled.slice(0, Math.min(count, array.length));\n};\n// Score calculation utilities\nconst calculateScore = (correct, total, timeBonus = 0, difficultyMultiplier = 1, streakBonus = 0)=>{\n    const baseScore = correct / total * 100;\n    const bonusScore = timeBonus + streakBonus;\n    return Math.round((baseScore + bonusScore) * difficultyMultiplier);\n};\nconst calculateTimeBonus = (timeSpent, maxTime)=>{\n    if (timeSpent <= maxTime * 0.5) return 10; // Answered in first half of time\n    if (timeSpent <= maxTime * 0.75) return 5; // Answered in first 3/4 of time\n    return 0;\n};\nconst calculateStreakBonus = (streak)=>{\n    return Math.min(streak * 2, 20); // Max 20 bonus points\n};\n// Quiz question generation\nconst generateQuizQuestions = (countries, category, difficulty, count = 10)=>{\n    const questions = [];\n    const selectedCountries = getRandomItems(countries, count);\n    selectedCountries.forEach((country, index)=>{\n        switch(category){\n            case 'geography':\n                questions.push(generateGeographyQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'history':\n                questions.push(generateHistoryQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'culture':\n                questions.push(generateCultureQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'wildlife':\n                questions.push(generateWildlifeQuestion(country, countries, difficulty, index.toString()));\n                break;\n            case 'notable-figures':\n                questions.push(generateNotableFiguresQuestion(country, countries, difficulty, index.toString()));\n                break;\n        }\n    });\n    return shuffleArray(questions);\n};\nconst generateGeographyQuestion = (country, allCountries, difficulty, id)=>{\n    const questionTypes = [\n        'capital',\n        'currency',\n        'region'\n    ];\n    const type = questionTypes[Math.floor(seededRandom() * questionTypes.length)];\n    switch(type){\n        case 'capital':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the capital of ${country.name}?`,\n                options: generateCapitalOptions(country, allCountries),\n                correctAnswer: country.capital,\n                explanation: `${country.capital} is the capital city of ${country.name}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        case 'currency':\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `What is the currency of ${country.name}?`,\n                options: generateCurrencyOptions(country, allCountries),\n                correctAnswer: country.currency,\n                explanation: `The currency of ${country.name} is ${country.currency}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n        default:\n            return {\n                id,\n                type: 'multiple-choice',\n                category: 'geography',\n                question: `Which region is ${country.name} located in?`,\n                options: generateRegionOptions(country, allCountries),\n                correctAnswer: country.region,\n                explanation: `${country.name} is located in ${country.region}.`,\n                difficulty: difficulty,\n                countryId: country.id\n            };\n    }\n};\nconst generateHistoryQuestion = (country, allCountries, difficulty, id)=>{\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'history',\n        question: `When did ${country.name} gain independence?`,\n        options: generateIndependenceOptions(country, allCountries),\n        correctAnswer: new Date(country.independence).getFullYear().toString(),\n        explanation: `${country.name} gained independence in ${new Date(country.independence).getFullYear()}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateCultureQuestion = (country, allCountries, difficulty, id)=>{\n    const culturalAspects = [\n        'cuisine',\n        'music',\n        'dances'\n    ];\n    const aspect = culturalAspects[Math.floor(seededRandom() * culturalAspects.length)];\n    const item = country.culturalElements[aspect][0];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'culture',\n        question: `Which country is known for ${item}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${item} is a traditional ${aspect.slice(0, -1)} from ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateWildlifeQuestion = (country, allCountries, difficulty, id)=>{\n    const animal = country.wildlife[Math.floor(seededRandom() * country.wildlife.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'wildlife',\n        question: `Which country is home to ${animal}?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${animal} can be found in ${country.name}.`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\nconst generateNotableFiguresQuestion = (country, allCountries, difficulty, id)=>{\n    const figure = country.notableFigures[Math.floor(seededRandom() * country.notableFigures.length)];\n    return {\n        id,\n        type: 'multiple-choice',\n        category: 'notable-figures',\n        question: `${figure.name} is from which country?`,\n        options: generateCountryOptions(country, allCountries),\n        correctAnswer: country.name,\n        explanation: `${figure.name} is from ${country.name}. ${figure.achievement}`,\n        difficulty: difficulty,\n        countryId: country.id\n    };\n};\n// Helper functions for generating options\nconst generateCapitalOptions = (country, allCountries)=>{\n    const options = [\n        country.capital\n    ];\n    const otherCapitals = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.capital);\n    while(options.length < 4){\n        const randomCapital = otherCapitals[Math.floor(seededRandom() * otherCapitals.length)];\n        if (!options.includes(randomCapital)) {\n            options.push(randomCapital);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateCurrencyOptions = (country, allCountries)=>{\n    const options = [\n        country.currency\n    ];\n    const otherCurrencies = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.currency);\n    while(options.length < 4){\n        const randomCurrency = otherCurrencies[Math.floor(seededRandom() * otherCurrencies.length)];\n        if (!options.includes(randomCurrency)) {\n            options.push(randomCurrency);\n        }\n    }\n    return shuffleArray(options);\n};\nconst generateRegionOptions = (country, allCountries)=>{\n    const regions = [\n        'North Africa',\n        'West Africa',\n        'East Africa',\n        'Central Africa',\n        'Southern Africa'\n    ];\n    const options = [\n        country.region\n    ];\n    regions.forEach((region)=>{\n        if (region !== country.region && options.length < 4) {\n            options.push(region);\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateIndependenceOptions = (country, allCountries)=>{\n    const year = new Date(country.independence).getFullYear();\n    const options = [\n        year.toString()\n    ];\n    // Generate nearby years\n    const nearbyYears = [\n        year - 10,\n        year + 5,\n        year - 5\n    ];\n    nearbyYears.forEach((y)=>{\n        if (options.length < 4) {\n            options.push(y.toString());\n        }\n    });\n    return shuffleArray(options);\n};\nconst generateCountryOptions = (country, allCountries)=>{\n    const options = [\n        country.name\n    ];\n    const otherCountries = allCountries.filter((c)=>c.id !== country.id).map((c)=>c.name);\n    while(options.length < 4){\n        const randomCountry = otherCountries[Math.floor(seededRandom() * otherCountries.length)];\n        if (!options.includes(randomCountry)) {\n            options.push(randomCountry);\n        }\n    }\n    return shuffleArray(options);\n};\n// Format utilities\nconst formatTime = (seconds)=>{\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n};\nconst formatScore = (score)=>{\n    return `${score}%`;\n};\nconst formatNumber = (num)=>{\n    return new Intl.NumberFormat().format(num);\n};\n// Validation utilities\nconst validateAnswer = (userAnswer, correctAnswer)=>{\n    return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n};\n// Local storage utilities\nconst saveToLocalStorage = (key, data)=>{\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error('Error saving to localStorage:', error);\n    }\n};\nconst loadFromLocalStorage = (key)=>{\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n    } catch (error) {\n        console.error('Error loading from localStorage:', error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/index.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateScore),\n/* harmony export */   calculateStreakBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateStreakBonus),\n/* harmony export */   calculateTimeBonus: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.calculateTimeBonus),\n/* harmony export */   formatNumber: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   formatScore: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatScore),\n/* harmony export */   formatTime: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.formatTime),\n/* harmony export */   generateQuizQuestions: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.generateQuizQuestions),\n/* harmony export */   getRandomItems: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.getRandomItems),\n/* harmony export */   loadFromLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.loadFromLocalStorage),\n/* harmony export */   resetSeed: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.resetSeed),\n/* harmony export */   saveToLocalStorage: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.saveToLocalStorage),\n/* harmony export */   shuffleArray: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.shuffleArray),\n/* harmony export */   validateAnswer: () => (/* reexport safe */ C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__.validateAnswer)\n/* harmony export */ });\n/* harmony import */ var C_Users_MEEK_EDEN_Documents_augment_projects_GAMES_FOR_ARICA_games_for_africa_src_utils_index_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/index.ts */ \"(ssr)/./src/utils/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1nZXRSYW5kb21JdGVtcyxzaHVmZmxlQXJyYXkhPSEuL3NyYy91dGlscy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUEwSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNRUVLIEVERU5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcR0FNRVMgRk9SIEFSSUNBXFxnYW1lcy1mb3ItYWZyaWNhXFxzcmNcXHV0aWxzXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcTUVFSyBFREVOXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXEdBTUVTIEZPUiBBUklDQVxcXFxnYW1lcy1mb3ItYWZyaWNhXFxcXHNyY1xcXFx1dGlsc1xcXFxpbmRleC50c1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=getRandomItems,shuffleArray!=!./src/utils/index.ts\n");

/***/ })

};
;