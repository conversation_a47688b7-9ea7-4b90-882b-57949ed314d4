"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useProgressStore */ \"(app-pages-browser)/./src/stores/useProgressStore.ts\");\n/* harmony import */ var _components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserDashboard */ \"(app-pages-browser)/./src/components/UserDashboard.tsx\");\n/* harmony import */ var _components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CelebrationModal */ \"(app-pages-browser)/./src/components/CelebrationModal.tsx\");\n/* harmony import */ var _components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/games/QuizGame */ \"(app-pages-browser)/./src/components/games/QuizGame.tsx\");\n/* harmony import */ var _components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/games/MatchingGame */ \"(app-pages-browser)/./src/components/games/MatchingGame.tsx\");\n/* harmony import */ var _components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/games/SpeedChallenge */ \"(app-pages-browser)/./src/components/games/SpeedChallenge.tsx\");\n/* harmony import */ var _components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/games/CountryExplorer */ \"(app-pages-browser)/./src/components/games/CountryExplorer.tsx\");\n/* harmony import */ var _components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/games/MysteryLand */ \"(app-pages-browser)/./src/components/games/MysteryLand.tsx\");\n/* harmony import */ var _components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/games/MemoryGrid */ \"(app-pages-browser)/./src/components/games/MemoryGrid.tsx\");\n/* harmony import */ var _components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/games/JigsawPuzzle */ \"(app-pages-browser)/./src/components/games/JigsawPuzzle.tsx\");\n/* harmony import */ var _components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/games/TimelineBuilder */ \"(app-pages-browser)/./src/components/games/TimelineBuilder.tsx\");\n/* harmony import */ var _components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/games/WhereInAfrica */ \"(app-pages-browser)/./src/components/games/WhereInAfrica.tsx\");\n/* harmony import */ var _components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/games/DressTheCharacter */ \"(app-pages-browser)/./src/components/games/DressTheCharacter.tsx\");\n/* harmony import */ var _data_countries_json__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/data/countries.json */ \"(app-pages-browser)/./src/data/countries.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst gameConfigs = [\n    {\n        type: 'quiz',\n        name: 'Trivia Quiz',\n        description: 'Test your knowledge about African countries, cultures, and achievements',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 5,\n        maxScore: 100\n    },\n    {\n        type: 'matching',\n        name: 'Matching Game',\n        description: 'Match countries with their capitals, flags, and currencies',\n        icon: '🔗',\n        difficulty: 'easy',\n        estimatedTime: 3,\n        maxScore: 100\n    },\n    {\n        type: 'jigsaw-puzzle',\n        name: 'Jigsaw Puzzle Map',\n        description: 'Drag and drop puzzle pieces to complete a map of Africa',\n        icon: '🧩',\n        difficulty: 'hard',\n        estimatedTime: 8,\n        maxScore: 150\n    },\n    {\n        type: 'memory-grid',\n        name: 'Memory Grid',\n        description: 'Memorize African animals, instruments, and cultural items',\n        icon: '🧠',\n        difficulty: 'medium',\n        estimatedTime: 4,\n        maxScore: 100\n    },\n    {\n        type: 'speed-challenge',\n        name: 'Speed Challenge',\n        description: 'Answer as many questions as possible in 60 seconds',\n        icon: '⚡',\n        difficulty: 'hard',\n        estimatedTime: 1,\n        maxScore: 200\n    },\n    {\n        type: 'country-explorer',\n        name: 'Country Explorer',\n        description: 'Click on any African country to discover amazing facts',\n        icon: '🌍',\n        difficulty: 'easy',\n        estimatedTime: 10,\n        maxScore: 50\n    },\n    {\n        type: 'mystery-land',\n        name: 'Mystery Land',\n        description: 'Guess the country from clues about landmarks and culture',\n        icon: '🕵️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 120\n    },\n    {\n        type: 'timeline-builder',\n        name: 'Timeline Builder',\n        description: 'Arrange historical events in the correct chronological order',\n        icon: '📚',\n        difficulty: 'hard',\n        estimatedTime: 7,\n        maxScore: 130\n    },\n    {\n        type: 'dress-character',\n        name: 'Dress the Character',\n        description: 'Dress characters in traditional African clothing from different countries',\n        icon: '🎭',\n        difficulty: 'easy',\n        estimatedTime: 5,\n        maxScore: 80\n    },\n    {\n        type: 'where-in-africa',\n        name: 'Where in Africa?',\n        description: 'Guess the country from images of landmarks, food, and culture',\n        icon: '🗺️',\n        difficulty: 'medium',\n        estimatedTime: 6,\n        maxScore: 110\n    },\n    {\n        type: 'country-name-scramble',\n        name: 'Country Name Scramble',\n        description: 'Unscramble letters to form African country names',\n        icon: '🔤',\n        difficulty: 'medium',\n        estimatedTime: 10,\n        maxScore: 300\n    },\n    {\n        type: 'flag-matching',\n        name: 'Flag Matching',\n        description: 'Match African country flags with their names',\n        icon: '🏁',\n        difficulty: 'easy',\n        estimatedTime: 6,\n        maxScore: 200\n    }\n];\nfunction Home() {\n    _s();\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('geography');\n    const [selectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [celebrationData, setCelebrationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: 'gameComplete',\n        data: {}\n    });\n    const { profile, loadProfile, recordGameCompletion } = (0,_stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsClient(true);\n            loadProfile();\n        }\n    }[\"Home.useEffect\"], [\n        loadProfile\n    ]);\n    const countries = _data_countries_json__WEBPACK_IMPORTED_MODULE_15__.countries;\n    const handleGameSelect = (gameType)=>{\n        setCurrentGame(gameType);\n    };\n    const handleGameComplete = async (score)=>{\n        var _gameConfigs_find;\n        if (!currentGame) return;\n        const gameTitle = ((_gameConfigs_find = gameConfigs.find((g)=>g.type === currentGame)) === null || _gameConfigs_find === void 0 ? void 0 : _gameConfigs_find.name) || currentGame;\n        const completionTime = 300; // This should be passed from the game component\n        const difficulty = (profile === null || profile === void 0 ? void 0 : profile.preferences.difficulty) || 'intermediate';\n        const isPerfectScore = score >= 100; // This should be determined by the game\n        try {\n            const result = await recordGameCompletion(currentGame, score, completionTime, difficulty, isPerfectScore);\n            // Show game completion celebration\n            setCelebrationData({\n                isOpen: true,\n                type: 'gameComplete',\n                data: {\n                    score,\n                    gameTitle,\n                    xpGained: result.xpGained\n                }\n            });\n            // Show level up celebration if applicable\n            if (result.levelUp) {\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'levelUp',\n                        data: {\n                            newLevel: profile === null || profile === void 0 ? void 0 : profile.level,\n                            xpGained: result.xpGained\n                        }\n                    });\n                }, 2000);\n            }\n            // Show achievement celebrations\n            result.newAchievements.forEach((achievement, index)=>{\n                setTimeout(()=>{\n                    setCelebrationData({\n                        isOpen: true,\n                        type: 'achievement',\n                        data: {\n                            achievement\n                        }\n                    });\n                }, 3000 + index * 2000);\n            });\n        } catch (error) {\n            console.error('Error recording game completion:', error);\n        }\n    };\n    const handleBackToMenu = ()=>{\n        setCurrentGame(null);\n    };\n    const renderGame = ()=>{\n        const gameProps = {\n            countries,\n            onComplete: handleGameComplete\n        };\n        switch(currentGame){\n            case 'quiz':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_QuizGame__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    ...gameProps,\n                    category: selectedCategory,\n                    difficulty: selectedDifficulty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this);\n            case 'matching':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MatchingGame__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 16\n                }, this);\n            case 'speed-challenge':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_SpeedChallenge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 16\n                }, this);\n            case 'country-explorer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_CountryExplorer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 16\n                }, this);\n            case 'mystery-land':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MysteryLand__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 16\n                }, this);\n            case 'memory-grid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_MemoryGrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case 'jigsaw-puzzle':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_JigsawPuzzle__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case 'timeline-builder':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_TimelineBuilder__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case 'where-in-africa':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_WhereInAfrica__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            case 'dress-character':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_games_DressTheCharacter__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    ...gameProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Game Coming Soon!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"This game is being developed.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (currentGame) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleBackToMenu,\n                            className: \"mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                            children: \"← Back to Games\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this),\n                        renderGame()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-primary-dark text-text-primary flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl text-accent-gold\",\n                        children: \"Loading Games for Africa...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-primary-dark text-text-primary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-primary-light border-b border-gray-700 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-accent-gold\",\n                                                children: \"Games for Africa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-400 hidden sm:block\",\n                                                children: \"Learn about African countries and cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-gold font-medium\",\n                                                    children: profile.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        \"Level \",\n                                                        profile.level,\n                                                        \" • \",\n                                                        profile.totalXP,\n                                                        \" XP\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowDashboard(true),\n                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-gray-900 font-bold hover:scale-105 transition-transform\",\n                                            children: profile.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-16 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    \"Discover the Magic of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-gold block\",\n                                        children: \"Africa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Embark on an educational journey through 54 African countries. Learn about their rich cultures, remarkable achievements, and incredible diversity through interactive games and challenges.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Educational Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Achievement System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-primary-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: countries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: gameConfigs.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Your Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"games\",\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"Choose Your Adventure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Select from our collection of interactive games designed to teach you about Africa's rich heritage and diverse cultures.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: gameConfigs.map((game)=>{\n                                const gameProgress = profile === null || profile === void 0 ? void 0 : profile.gamesProgress[game.type];\n                                const isCompleted = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.completed) || false;\n                                const bestScore = (gameProgress === null || gameProgress === void 0 ? void 0 : gameProgress.bestScore) || 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-light border rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer relative \".concat(isCompleted ? 'border-green-400' : 'border-gray-700'),\n                                    onClick: ()=>handleGameSelect(game.type),\n                                    children: [\n                                        isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl\",\n                                                            children: game.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"⏱️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        game.estimatedTime,\n                                                                        \"min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-text-primary\",\n                                                    children: game.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4 flex-grow\",\n                                                    children: game.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 rounded text-xs font-medium \".concat(game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' : game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-red-500 bg-opacity-20 text-red-400'),\n                                                            children: game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                bestScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-400 text-sm font-medium\",\n                                                                    children: [\n                                                                        \"Best: \",\n                                                                        bestScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: [\n                                                                        \"Max: \",\n                                                                        game.maxScore,\n                                                                        \" pts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors\",\n                                                    children: \"▶️ Play Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, game.type, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            showDashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserDashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onClose: ()=>setShowDashboard(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 471,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CelebrationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: celebrationData.isOpen,\n                onClose: ()=>setCelebrationData((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        })),\n                type: celebrationData.type,\n                data: celebrationData.data\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 293,\n        columnNumber: 7\n    }, this);\n}\n_s(Home, \"F/q13Zo1gnuX1bo7PfVxtyEnecY=\", false, function() {\n    return [\n        _stores_useProgressStore__WEBPACK_IMPORTED_MODULE_2__.useProgressStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});