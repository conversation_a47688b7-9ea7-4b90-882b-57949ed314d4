'use client';

import React from 'react';

interface LoadingSkeletonProps {
  type?: 'game-card' | 'flag' | 'country-list' | 'game-content';
  count?: number;
  className?: string;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  type = 'game-card', 
  count = 1, 
  className = '' 
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'game-card':
        return (
          <div className={`bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse ${className}`}>
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-gray-700 rounded-lg"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-700 rounded w-full"></div>
              <div className="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
            <div className="flex justify-between items-center mt-4">
              <div className="h-3 bg-gray-600 rounded w-1/4"></div>
              <div className="h-8 bg-gray-700 rounded w-20"></div>
            </div>
          </div>
        );

      case 'flag':
        return (
          <div className={`w-12 h-9 bg-gray-700 rounded-sm animate-pulse ${className}`}></div>
        );

      case 'country-list':
        return (
          <div className={`space-y-3 ${className}`}>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg animate-pulse">
                <div className="w-8 h-6 bg-gray-700 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-700 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'game-content':
        return (
          <div className={`space-y-6 ${className}`}>
            {/* Header skeleton */}
            <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse">
              <div className="flex justify-between items-center mb-4">
                <div className="h-6 bg-gray-700 rounded w-1/3"></div>
                <div className="h-6 bg-gray-700 rounded w-20"></div>
              </div>
              <div className="grid grid-cols-5 gap-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="text-center">
                    <div className="h-8 bg-gray-700 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-600 rounded w-3/4 mx-auto"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Game area skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse">
                <div className="h-5 bg-gray-700 rounded w-1/2 mb-4"></div>
                <div className="grid grid-cols-3 gap-3">
                  {Array.from({ length: 9 }).map((_, i) => (
                    <div key={i} className="aspect-square bg-gray-700 rounded-lg"></div>
                  ))}
                </div>
              </div>
              
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 animate-pulse">
                <div className="h-5 bg-gray-700 rounded w-1/2 mb-4"></div>
                <div className="space-y-3">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="h-12 bg-gray-700 rounded-lg"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className={`h-4 bg-gray-700 rounded animate-pulse ${className}`}></div>
        );
    }
  };

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>
          {renderSkeleton()}
        </div>
      ))}
    </>
  );
};

export default LoadingSkeleton;
