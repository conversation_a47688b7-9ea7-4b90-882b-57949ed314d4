"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/games/CountryNameScramble.tsx":
/*!******************************************************!*\
  !*** ./src/components/games/CountryNameScramble.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils */ \"(app-pages-browser)/./src/utils/index.ts\");\n/* harmony import */ var _components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/FlagImage */ \"(app-pages-browser)/./src/components/ui/FlagImage.tsx\");\n/* harmony import */ var _utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/gameCompletion */ \"(app-pages-browser)/./src/utils/gameCompletion.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CountryNameScramble = (param)=>{\n    let { countries, onComplete } = param;\n    _s();\n    const [currentRound, setCurrentRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rounds, setRounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLetters, setSelectedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userAnswer, setUserAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streak, setStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [difficulty, setDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameComplete, setGameComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHint, setShowHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCorrect, setIsCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roundStartTime, setRoundStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [showVictory, setShowVictory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completionData, setCompletionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameStartTime, setGameStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const totalRounds = 10;\n    const getDifficultyMultiplier = (diff)=>{\n        switch(diff){\n            case 'beginner':\n                return 1;\n            case 'intermediate':\n                return 1.5;\n            case 'advanced':\n                return 2;\n        }\n    };\n    const filterCountriesByDifficulty = (countries, difficulty)=>{\n        return countries.filter((country)=>{\n            const nameLength = country.name.replace(/\\s+/g, '').length;\n            switch(difficulty){\n                case 'beginner':\n                    return nameLength >= 4 && nameLength <= 6;\n                case 'intermediate':\n                    return nameLength >= 7 && nameLength <= 10;\n                case 'advanced':\n                    return nameLength >= 11;\n                default:\n                    return true;\n            }\n        });\n    };\n    const scrambleCountryName = (name)=>{\n        const cleanName = name.replace(/\\s+/g, '').toUpperCase();\n        const letters = cleanName.split('').map((letter, index)=>({\n                id: \"letter-\".concat(index, \"-\").concat(Math.random()),\n                letter,\n                originalIndex: index,\n                isUsed: false\n            }));\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(letters);\n    };\n    const generateRounds = ()=>{\n        const filteredCountries = filterCountriesByDifficulty(countries, difficulty);\n        const selectedCountries = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.shuffleArray)(filteredCountries).slice(0, totalRounds);\n        return selectedCountries.map((country)=>({\n                country,\n                scrambledLetters: scrambleCountryName(country.name),\n                userAnswer: '',\n                isComplete: false,\n                timeSpent: 0\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountryNameScramble.useEffect\": ()=>{\n            if (gameStarted && timeLeft > 0 && !gameComplete && !showVictory) {\n                const timer = setTimeout({\n                    \"CountryNameScramble.useEffect.timer\": ()=>setTimeLeft(timeLeft - 1)\n                }[\"CountryNameScramble.useEffect.timer\"], 1000);\n                return ({\n                    \"CountryNameScramble.useEffect\": ()=>clearTimeout(timer)\n                })[\"CountryNameScramble.useEffect\"];\n            } else if (timeLeft === 0 && !showVictory) {\n                handleGameEnd();\n            }\n        }\n    }[\"CountryNameScramble.useEffect\"], [\n        gameStarted,\n        timeLeft,\n        gameComplete,\n        showVictory\n    ]);\n    const startGame = ()=>{\n        const newRounds = generateRounds();\n        setRounds(newRounds);\n        setCurrentRound(0);\n        setScore(0);\n        setStreak(0);\n        setTimeLeft(60);\n        setUserAnswer('');\n        setSelectedLetters([]);\n        setGameStarted(true);\n        setGameComplete(false);\n        setShowResult(false);\n        setShowVictory(false);\n        setCompletionData(null);\n        setRoundStartTime(Date.now());\n        setGameStartTime(Date.now());\n    };\n    const handleImmediateCompletion = ()=>{\n        const totalTime = 60;\n        const completionTime = (Date.now() - gameStartTime) / 1000;\n        const correctAnswers = rounds.filter((r)=>r.isComplete && r.userAnswer.replace(/\\s+/g, '').toUpperCase() === r.country.name.replace(/\\s+/g, '').toUpperCase()).length;\n        const gameData = {\n            gameType: 'country-name-scramble',\n            score,\n            timeRemaining: timeLeft,\n            totalTime,\n            perfectScore: correctAnswers === totalRounds,\n            difficulty,\n            completionTime\n        };\n        const result = (0,_utils_gameCompletion__WEBPACK_IMPORTED_MODULE_4__.processGameCompletion)(gameData);\n        setCompletionData(result);\n        setGameComplete(true);\n        setShowVictory(true);\n    };\n    const handleLetterClick = (letter)=>{\n        if (letter.isUsed || showResult) return;\n        const newSelectedLetters = [\n            ...selectedLetters,\n            {\n                ...letter,\n                isUsed: true\n            }\n        ];\n        setSelectedLetters(newSelectedLetters);\n        setUserAnswer(newSelectedLetters.map((l)=>l.letter).join(''));\n        // Update the scrambled letters to mark this one as used\n        const currentRoundData = rounds[currentRound];\n        const updatedScrambledLetters = currentRoundData.scrambledLetters.map((l)=>l.id === letter.id ? {\n                ...l,\n                isUsed: true\n            } : l);\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: updatedScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleClearAnswer = ()=>{\n        if (showResult) return;\n        setSelectedLetters([]);\n        setUserAnswer('');\n        // Reset all letters to unused\n        const currentRoundData = rounds[currentRound];\n        const resetScrambledLetters = currentRoundData.scrambledLetters.map((l)=>({\n                ...l,\n                isUsed: false\n            }));\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...currentRoundData,\n            scrambledLetters: resetScrambledLetters\n        };\n        setRounds(updatedRounds);\n    };\n    const handleSubmitAnswer = ()=>{\n        if (!userAnswer || showResult) return;\n        const currentCountry = rounds[currentRound].country;\n        const correctAnswer = currentCountry.name.replace(/\\s+/g, '').toUpperCase();\n        const userAnswerClean = userAnswer.replace(/\\s+/g, '').toUpperCase();\n        const correct = userAnswerClean === correctAnswer;\n        setIsCorrect(correct);\n        setShowResult(true);\n        const timeSpent = (Date.now() - roundStartTime) / 1000;\n        if (correct) {\n            const basePoints = correctAnswer.length;\n            const timeBonus = Math.max(0, Math.floor((10 - timeSpent) * 2));\n            const streakMultiplier = 1 + streak * 0.1;\n            const difficultyMultiplier = getDifficultyMultiplier(difficulty);\n            const roundScore = Math.floor((basePoints + timeBonus) * streakMultiplier * difficultyMultiplier);\n            setScore(score + roundScore);\n            setStreak(streak + 1);\n        } else {\n            setStreak(0);\n        }\n        // Update round data\n        const updatedRounds = [\n            ...rounds\n        ];\n        updatedRounds[currentRound] = {\n            ...updatedRounds[currentRound],\n            userAnswer,\n            isComplete: true,\n            timeSpent\n        };\n        setRounds(updatedRounds);\n        setTimeout(()=>{\n            if (currentRound < totalRounds - 1) {\n                setCurrentRound(currentRound + 1);\n                setUserAnswer('');\n                setSelectedLetters([]);\n                setShowResult(false);\n                setShowHint(false);\n                setRoundStartTime(Date.now());\n                // Reset letters for next round\n                const nextRoundData = updatedRounds[currentRound + 1];\n                const resetLetters = nextRoundData.scrambledLetters.map((l)=>({\n                        ...l,\n                        isUsed: false\n                    }));\n                updatedRounds[currentRound + 1] = {\n                    ...nextRoundData,\n                    scrambledLetters: resetLetters\n                };\n                setRounds(updatedRounds);\n            } else {\n                handleGameEnd();\n            }\n        }, 3000);\n    };\n    const handleGameEnd = ()=>{\n        setGameComplete(true);\n        setTimeout(()=>onComplete(score), 1000);\n    };\n    const toggleHint = ()=>{\n        setShowHint(!showHint);\n    };\n    if (!gameStarted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6 text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white mb-6\",\n                        children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-6\",\n                        children: \"\\uD83E\\uDDE9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Unscramble the letters to form African country names!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-4\",\n                                children: \"Choose Difficulty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4\",\n                                children: [\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDifficulty(diff),\n                                        className: \"px-6 py-3 rounded-lg transition-colors \".concat(difficulty === diff ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-white hover:bg-gray-600'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold capitalize\",\n                                                    children: diff\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        diff === 'beginner' && '4-6 letters',\n                                                        diff === 'intermediate' && '7-10 letters',\n                                                        diff === 'advanced' && '11+ letters'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, diff, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-gray-300 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Unscramble letters to form country names\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Longer names = more points\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Build streaks for bonus multipliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Use hints to see flags and regions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startGame,\n                        className: \"bg-yellow-400 text-gray-900 py-3 px-8 rounded-lg text-xl font-bold hover:bg-yellow-300 transition-colors\",\n                        children: \"\\uD83D\\uDE80 Start Scrambling\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!rounds[currentRound]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentCountry = rounds[currentRound].country;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"\\uD83D\\uDD24 Country Name Scramble\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-yellow-400 text-xl font-bold\",\n                                children: [\n                                    Math.floor(timeLeft / 60),\n                                    \":\",\n                                    (timeLeft % 60).toString().padStart(2, '0')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: score\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: streak\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Streak\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: currentRound + 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: totalRounds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-400 capitalize\",\n                                        children: difficulty\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Difficulty\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-400 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: \"\".concat((currentRound + 1) / totalRounds * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"Unscramble this African country name:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleHint,\n                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            showHint ? 'Hide Hint' : 'Show Hint',\n                                            \" \\uD83D\\uDCA1\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 bg-blue-500 bg-opacity-10 border border-blue-400 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    countryId: currentCountry.id,\n                                                    size: \"large\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: [\n                                                                \"Region: \",\n                                                                currentCountry.region\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: [\n                                                                \"Capital: \",\n                                                                currentCountry.capital\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Available Letters:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-2\",\n                                children: rounds[currentRound].scrambledLetters.map((letter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLetterClick(letter),\n                                        disabled: letter.isUsed || showResult,\n                                        className: \"w-12 h-12 rounded-lg font-bold text-xl transition-all duration-200 \".concat(letter.isUsed ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-yellow-400 text-gray-900 hover:bg-yellow-300 cursor-pointer transform hover:scale-105'),\n                                        children: letter.letter\n                                    }, letter.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-4 text-center\",\n                                children: \"Your Answer:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-700 border border-gray-600 rounded-lg p-4 min-h-[60px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white tracking-wider\",\n                                    children: userAnswer || 'Click letters to build your answer...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearAnswer,\n                                disabled: showResult,\n                                className: \"bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmitAnswer,\n                                disabled: !userAnswer || showResult,\n                                className: \"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Submit Answer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined),\n                    showResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 rounded-lg border \".concat(isCorrect ? 'bg-green-500 bg-opacity-10 border-green-400' : 'bg-red-500 bg-opacity-10 border-red-400'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold mb-2 \".concat(isCorrect ? 'text-green-400' : 'text-red-400'),\n                                    children: isCorrect ? '✓ Correct!' : '✗ Incorrect'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-lg mb-2\",\n                                    children: [\n                                        \"The answer was: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: currentCountry.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FlagImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            countryId: currentCountry.id,\n                                            size: \"large\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        \"Capital: \",\n                                                        currentCountry.capital\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Region: \",\n                                                        currentCountry.region\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: [\n                                                        \"Population: \",\n                                                        currentCountry.population.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, undefined),\n                                isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 font-semibold\",\n                                    children: [\n                                        \"+\",\n                                        Math.floor((currentCountry.name.replace(/\\s+/g, '').length + Math.max(0, Math.floor((10 - rounds[currentRound].timeSpent) * 2))) * (1 + streak * 0.1) * getDifficultyMultiplier(difficulty)),\n                                        \" points!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, undefined),\n            gameComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6 max-w-md w-full border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: score >= 200 ? '🏆' : score >= 150 ? '🎉' : '🔤'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                        children: \"Game Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Final Score: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: score\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Best Streak: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: Math.max(...rounds.map((_, i)=>i <= currentRound ? streak : 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Difficulty: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 capitalize\",\n                                                        children: difficulty\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 34\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startGame,\n                                    className: \"bg-yellow-400 text-gray-900 py-2 px-4 rounded-lg hover:bg-yellow-300 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Play Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n                lineNumber: 481,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\GAMES FOR ARICA\\\\games-for-africa\\\\src\\\\components\\\\games\\\\CountryNameScramble.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CountryNameScramble, \"nU2AS56vxz9naySQPe7ueP3B7Io=\");\n_c = CountryNameScramble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CountryNameScramble);\nvar _c;\n$RefreshReg$(_c, \"CountryNameScramble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/games/CountryNameScramble.tsx\n"));

/***/ })

});