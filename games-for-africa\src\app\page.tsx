'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, Trophy, Clock, Users, BookOpen } from 'lucide-react';
import Header from '@/components/layout/Header';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import QuizGame from '@/components/games/QuizGame';
import { useGameStore } from '@/hooks/useGameStore';
import { GameType, Country } from '@/types';
import countriesData from '@/data/countries.json';

const gameConfigs = [
  {
    type: 'quiz' as GameType,
    name: 'Interactive Quiz',
    description: 'Test your knowledge about African countries, cultures, and achievements',
    icon: '🧠',
    difficulty: 'medium' as const,
    estimatedTime: 5,
    maxScore: 100,
  },
  {
    type: 'matching' as GameType,
    name: 'Country Matching',
    description: 'Match countries with their capitals, flags, and currencies',
    icon: '🔗',
    difficulty: 'easy' as const,
    estimatedTime: 3,
    maxScore: 100,
  },
  {
    type: 'memory' as GameType,
    name: 'Memory Cards',
    description: 'Find matching pairs of African landmarks and cultural symbols',
    icon: '🃏',
    difficulty: 'medium' as const,
    estimatedTime: 4,
    maxScore: 100,
  },
];

export default function Home() {
  const { currentGame, setCurrentGame, userProgress } = useGameStore();
  const [selectedCategory, setSelectedCategory] = useState('geography');
  const [selectedDifficulty, setSelectedDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  const countries: Country[] = countriesData.countries;

  const handleGameSelect = (gameType: GameType) => {
    setCurrentGame(gameType);
  };

  const handleGameComplete = (score: number) => {
    console.log('Game completed with score:', score);
  };

  const handleBackToMenu = () => {
    setCurrentGame(null);
  };

  if (currentGame === 'quiz') {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="pt-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Button
              variant="secondary"
              onClick={handleBackToMenu}
              className="mb-6"
            >
              ← Back to Games
            </Button>
            <QuizGame
              countries={countries}
              category={selectedCategory}
              difficulty={selectedDifficulty}
              onComplete={handleGameComplete}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />

      <section className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Discover the Magic of
              <span className="text-yellow-400 block">Africa</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Embark on an educational journey through 54 African countries. Learn about their rich cultures,
              remarkable achievements, and incredible diversity through interactive games and challenges.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-gray-400">
              <div className="flex items-center space-x-2">
                <BookOpen className="w-5 h-5 text-yellow-400" />
                <span>Educational Content</span>
              </div>
              <div className="flex items-center space-x-2">
                <Trophy className="w-5 h-5 text-yellow-400" />
                <span>Achievement System</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-yellow-400" />
                <span>Progress Tracking</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <section className="py-12 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-3xl font-bold text-yellow-400">{countries.length}</div>
              <div className="text-gray-400">Countries</div>
            </motion.div>
            <motion.div
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
            >
              <div className="text-3xl font-bold text-yellow-400">{gameConfigs.length}</div>
              <div className="text-gray-400">Games</div>
            </motion.div>
            <motion.div
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
            >
              <div className="text-3xl font-bold text-yellow-400">{userProgress.totalScore}</div>
              <div className="text-gray-400">Your Score</div>
            </motion.div>
            <motion.div
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              <div className="text-3xl font-bold text-yellow-400">
                {userProgress.achievements.filter(a => a.unlocked).length}
              </div>
              <div className="text-gray-400">Achievements</div>
            </motion.div>
          </div>
        </div>
      </section>

      <section id="games" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Choose Your Adventure
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Select from our collection of interactive games designed to teach you about Africa's
              rich heritage and diverse cultures.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {gameConfigs.map((game, index) => (
              <motion.div
                key={game.type}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
              >
                <Card hover className="h-full">
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-4xl">{game.icon}</div>
                      <div className="flex items-center space-x-2 text-gray-400 text-sm">
                        <Clock className="w-4 h-4" />
                        <span>{game.estimatedTime}min</span>
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold mb-2">
                      {game.name}
                    </h3>

                    <p className="text-gray-300 mb-4 flex-grow">
                      {game.description}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' :
                        game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' :
                        'bg-red-500 bg-opacity-20 text-red-400'
                      }`}>
                        {game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)}
                      </span>
                      <span className="text-gray-400 text-sm">
                        Max: {game.maxScore} pts
                      </span>
                    </div>

                    <Button
                      onClick={() => handleGameSelect(game.type)}
                      className="w-full"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Play Now
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
