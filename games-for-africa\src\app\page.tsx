'use client';

import React, { useState, useEffect } from 'react';
import { GameType, Country } from '@/types';
import { useProgressStore } from '@/stores/useProgressStore';
import { Achievement } from '@/types/progress';
import UserDashboard from '@/components/UserDashboard';
import CelebrationModal from '@/components/CelebrationModal';
import QuizGame from '@/components/games/QuizGame';
import MatchingGame from '@/components/games/MatchingGame';
import SpeedChallenge from '@/components/games/SpeedChallenge';
import CountryExplorer from '@/components/games/CountryExplorer';
import MysteryLand from '@/components/games/MysteryLand';
import MemoryGrid from '@/components/games/MemoryGrid';
import JigsawPuzzle from '@/components/games/JigsawPuzzle';
import TimelineBuilder from '@/components/games/TimelineBuilder';
import WhereInAfrica from '@/components/games/WhereInAfrica';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/games/DressTheCharacter';
import CountryNameScramble from '@/components/games/CountryNameScramble';
import FlagMatching from '@/components/games/FlagMatching';
import countriesData from '@/data/countries.json';

const gameConfigs = [
  {
    type: 'quiz' as GameType,
    name: 'Trivia Quiz',
    description: 'Test your knowledge about African countries, cultures, and achievements',
    icon: '🧠',
    difficulty: 'medium' as const,
    estimatedTime: 5,
    maxScore: 100,
  },
  {
    type: 'matching' as GameType,
    name: 'Matching Game',
    description: 'Match countries with their capitals, flags, and currencies',
    icon: '🔗',
    difficulty: 'easy' as const,
    estimatedTime: 3,
    maxScore: 100,
  },
  {
    type: 'jigsaw-puzzle' as GameType,
    name: 'Jigsaw Puzzle Map',
    description: 'Drag and drop puzzle pieces to complete a map of Africa',
    icon: '🧩',
    difficulty: 'hard' as const,
    estimatedTime: 8,
    maxScore: 150,
  },
  {
    type: 'memory-grid' as GameType,
    name: 'Memory Grid',
    description: 'Memorize African animals, instruments, and cultural items',
    icon: '🧠',
    difficulty: 'medium' as const,
    estimatedTime: 4,
    maxScore: 100,
  },
  {
    type: 'speed-challenge' as GameType,
    name: 'Speed Challenge',
    description: 'Answer as many questions as possible in 60 seconds',
    icon: '⚡',
    difficulty: 'hard' as const,
    estimatedTime: 1,
    maxScore: 200,
  },
  {
    type: 'country-explorer' as GameType,
    name: 'Country Explorer',
    description: 'Click on any African country to discover amazing facts',
    icon: '🌍',
    difficulty: 'easy' as const,
    estimatedTime: 10,
    maxScore: 50,
  },
  {
    type: 'mystery-land' as GameType,
    name: 'Mystery Land',
    description: 'Guess the country from clues about landmarks and culture',
    icon: '🕵️',
    difficulty: 'medium' as const,
    estimatedTime: 6,
    maxScore: 120,
  },
  {
    type: 'timeline-builder' as GameType,
    name: 'Timeline Builder',
    description: 'Arrange historical events in the correct chronological order',
    icon: '📚',
    difficulty: 'hard' as const,
    estimatedTime: 7,
    maxScore: 130,
  },
  {
    type: 'dress-character' as GameType,
    name: 'Dress the Character',
    description: 'Dress characters in traditional African clothing from different countries',
    icon: '🎭',
    difficulty: 'easy' as const,
    estimatedTime: 5,
    maxScore: 80,
  },
  {
    type: 'where-in-africa' as GameType,
    name: 'Where in Africa?',
    description: 'Guess the country from images of landmarks, food, and culture',
    icon: '🗺️',
    difficulty: 'medium' as const,
    estimatedTime: 6,
    maxScore: 110,
  },
  {
    type: 'country-name-scramble' as GameType,
    name: 'Country Name Scramble',
    description: 'Unscramble letters to form African country names',
    icon: '🔤',
    difficulty: 'medium' as const,
    estimatedTime: 10,
    maxScore: 300,
  },
  {
    type: 'flag-matching' as GameType,
    name: 'Flag Matching',
    description: 'Match African country flags with their names',
    icon: '🏁',
    difficulty: 'easy' as const,
    estimatedTime: 6,
    maxScore: 200,
  },
];

export default function Home() {
  const [currentGame, setCurrentGame] = useState<GameType | null>(null);
  const [selectedCategory] = useState('geography');
  const [selectedDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [isClient, setIsClient] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [celebrationData, setCelebrationData] = useState<{
    isOpen: boolean;
    type: 'achievement' | 'levelUp' | 'gameComplete';
    data: any;
  }>({ isOpen: false, type: 'gameComplete', data: {} });

  const { profile, loadProfile, recordGameCompletion } = useProgressStore();

  useEffect(() => {
    setIsClient(true);
    loadProfile();
  }, [loadProfile]);

  const countries: Country[] = countriesData.countries;

  const handleGameSelect = (gameType: GameType) => {
    setCurrentGame(gameType);
  };

  const handleGameComplete = async (score: number) => {
    if (!currentGame) return;

    const gameTitle = gameConfigs.find(g => g.type === currentGame)?.name || currentGame;
    const completionTime = 300; // This should be passed from the game component
    const difficulty = profile?.preferences.difficulty || 'intermediate';
    const isPerfectScore = score >= 100; // This should be determined by the game

    try {
      const result = await recordGameCompletion(
        currentGame,
        score,
        completionTime,
        difficulty,
        isPerfectScore
      );

      // Show game completion celebration
      setCelebrationData({
        isOpen: true,
        type: 'gameComplete',
        data: {
          score,
          gameTitle,
          xpGained: result.xpGained,
        },
      });

      // Show level up celebration if applicable
      if (result.levelUp) {
        setTimeout(() => {
          setCelebrationData({
            isOpen: true,
            type: 'levelUp',
            data: {
              newLevel: profile?.level,
              xpGained: result.xpGained,
            },
          });
        }, 2000);
      }

      // Show achievement celebrations
      result.newAchievements.forEach((achievement, index) => {
        setTimeout(() => {
          setCelebrationData({
            isOpen: true,
            type: 'achievement',
            data: { achievement },
          });
        }, 3000 + index * 2000);
      });
    } catch (error) {
      console.error('Error recording game completion:', error);
    }
  };

  const handleBackToMenu = () => {
    setCurrentGame(null);
  };

  const renderGame = () => {
    const gameProps = {
      countries,
      onComplete: handleGameComplete,
    };

    switch (currentGame) {
      case 'quiz':
        return (
          <QuizGame
            {...gameProps}
            category={selectedCategory}
            difficulty={selectedDifficulty}
          />
        );
      case 'matching':
        return <MatchingGame {...gameProps} />;
      case 'speed-challenge':
        return <SpeedChallenge {...gameProps} />;
      case 'country-explorer':
        return <CountryExplorer {...gameProps} />;
      case 'mystery-land':
        return <MysteryLand {...gameProps} />;
      case 'memory-grid':
        return <MemoryGrid {...gameProps} />;
      case 'jigsaw-puzzle':
        return <JigsawPuzzle {...gameProps} />;
      case 'timeline-builder':
        return <TimelineBuilder {...gameProps} />;
      case 'where-in-africa':
        return <WhereInAfrica {...gameProps} />;
      case 'dress-character':
        return <DressTheCharacter {...gameProps} />;
      case 'country-name-scramble':
        return <CountryNameScramble {...gameProps} />;
      case 'flag-matching':
        return <FlagMatching {...gameProps} />;
      default:
        return (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Game Coming Soon!</h2>
            <p className="text-gray-300">This game is being developed.</p>
          </div>
        );
    }
  };

  if (currentGame) {
    return (
      <div className="min-h-screen bg-primary-dark text-text-primary">
        <div className="pt-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <button
              onClick={handleBackToMenu}
              className="mb-6 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              ← Back to Games
            </button>
            {renderGame()}
          </div>
        </div>
      </div>
    );
  }

  if (!isClient) {
    return (
      <div className="min-h-screen bg-primary-dark text-text-primary flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🌍</div>
          <div className="text-xl text-accent-gold">Loading Games for Africa...</div>
        </div>
      </div>
    );
  }

  return (
      <div className="min-h-screen bg-primary-dark text-text-primary">
      {/* Header */}
      <header className="bg-primary-light border-b border-gray-700 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">🌍</div>
              <div>
                <h1 className="text-xl font-bold text-accent-gold">
                  Games for Africa
                </h1>
                <p className="text-xs text-gray-400 hidden sm:block">
                  Learn about African countries and cultures
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {profile && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="text-accent-gold font-medium">
                      {profile.username}
                    </div>
                    <div className="text-gray-400 text-sm">
                      Level {profile.level} • {profile.totalXP} XP
                    </div>
                  </div>
                  <button
                    onClick={() => setShowDashboard(true)}
                    className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-gray-900 font-bold hover:scale-105 transition-transform"
                  >
                    {profile.username.charAt(0).toUpperCase()}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-16 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Discover the Magic of
              <span className="text-accent-gold block">Africa</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Embark on an educational journey through 54 African countries. Learn about their rich cultures,
              remarkable achievements, and incredible diversity through interactive games and challenges.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-gray-400">
              <div className="flex items-center space-x-2">
                <span>📚</span>
                <span>Educational Content</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>🏆</span>
                <span>Achievement System</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>👥</span>
                <span>Progress Tracking</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-primary-light">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-accent-gold">{countries.length}</div>
              <div className="text-gray-400">Countries</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent-gold">{gameConfigs.length}</div>
              <div className="text-gray-400">Games</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent-gold">0</div>
              <div className="text-gray-400">Your Score</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent-gold">0</div>
              <div className="text-gray-400">Achievements</div>
            </div>
          </div>
        </div>
      </section>

      {/* Games Section */}
      <section id="games" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Choose Your Adventure
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Select from our collection of interactive games designed to teach you about Africa's
              rich heritage and diverse cultures.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {gameConfigs.map((game) => {
              const gameProgress = profile?.gamesProgress[game.type];
              const isCompleted = gameProgress?.completed || false;
              const bestScore = gameProgress?.bestScore || 0;

              return (
                <div
                  key={game.type}
                  className={`bg-primary-light border rounded-lg p-6 hover:border-accent-gold transition-all duration-300 cursor-pointer relative ${
                    isCompleted ? 'border-green-400' : 'border-gray-700'
                  }`}
                  onClick={() => handleGameSelect(game.type)}
                >
                {/* Completion Badge */}
                {isCompleted && (
                  <div className="absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </div>
                )}

                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-4xl">{game.icon}</div>
                    <div className="flex items-center space-x-2 text-gray-400 text-sm">
                      <span>⏱️</span>
                      <span>{game.estimatedTime}min</span>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold mb-2 text-text-primary">
                    {game.name}
                  </h3>

                  <p className="text-gray-300 mb-4 flex-grow">
                    {game.description}
                  </p>

                  <div className="flex items-center justify-between mb-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      game.difficulty === 'easy' ? 'bg-green-500 bg-opacity-20 text-green-400' :
                      game.difficulty === 'medium' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' :
                      'bg-red-500 bg-opacity-20 text-red-400'
                    }`}>
                      {game.difficulty.charAt(0).toUpperCase() + game.difficulty.slice(1)}
                    </span>
                    <div className="text-right">
                      {bestScore > 0 && (
                        <div className="text-yellow-400 text-sm font-medium">
                          Best: {bestScore}
                        </div>
                      )}
                      <span className="text-gray-400 text-sm">
                        Max: {game.maxScore} pts
                      </span>
                    </div>
                  </div>

                  <button className="w-full bg-accent-gold text-primary-dark py-2 px-4 rounded-lg font-medium hover:bg-yellow-400 transition-colors">
                    ▶️ Play Now
                  </button>
                </div>
              </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* User Dashboard */}
      {showDashboard && (
        <UserDashboard onClose={() => setShowDashboard(false)} />
      )}

      {/* Celebration Modal */}
      <CelebrationModal
        isOpen={celebrationData.isOpen}
        onClose={() => setCelebrationData(prev => ({ ...prev, isOpen: false }))}
        type={celebrationData.type}
        data={celebrationData.data}
      />
    </div>
  );
}
